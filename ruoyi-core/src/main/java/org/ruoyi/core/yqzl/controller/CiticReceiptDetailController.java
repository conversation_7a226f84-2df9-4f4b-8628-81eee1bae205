package org.ruoyi.core.yqzl.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import org.ruoyi.core.yqzl.domain.CiticReceiptDetail;
import org.ruoyi.core.yqzl.domain.req.CiticReceiptDownLoadQuery;
import org.ruoyi.core.yqzl.domain.vo.CiticReceiptDetailVo;
import org.ruoyi.core.yqzl.service.ICiticReceiptDetailService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中信银行回单信息（T+0/T+1）Controller
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/yqzl/receipt/detail")
public class CiticReceiptDetailController extends BaseController
{
    @Autowired
    private ICiticReceiptDetailService citicReceiptDetailService;
    /**
     * 查询中信银行回单信息（T+0/T+1）列表
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(CiticReceiptDetailVo citicReceiptDetail)
    {
        startPage();
        List<CiticReceiptDetailVo> list = citicReceiptDetailService.selectCiticReceiptDetailList(citicReceiptDetail);
        return getDataTable(list);
    }

    /**
     * 导出中信银行回单信息（T+0/T+1）列表
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:export')")
    @Log(title = "中信银行回单信息（T+0/T+1）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CiticReceiptDetailVo citicReceiptDetail)
    {
        List<CiticReceiptDetailVo> list = citicReceiptDetailService.selectCiticReceiptDetailList(citicReceiptDetail);
        ExcelUtil<CiticReceiptDetailVo> util = new ExcelUtil<CiticReceiptDetailVo>(CiticReceiptDetailVo.class);
        util.exportExcel(response, list, "中信银行回单信息（T+0/T+1）数据");
    }

    /**
     * 获取中信银行回单信息（T+0/T+1）详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(citicReceiptDetailService.selectCiticReceiptDetailById(id));
    }

    /**
     * 新增中信银行回单信息（T+0/T+1）
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:add')")
    @Log(title = "中信银行回单信息（T+0/T+1）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CiticReceiptDetail citicReceiptDetail)
    {
        return toAjax(citicReceiptDetailService.insertCiticReceiptDetail(citicReceiptDetail));
    }

    /**
     * 修改中信银行回单信息（T+0/T+1）
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:edit')")
    @Log(title = "中信银行回单信息（T+0/T+1）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CiticReceiptDetail citicReceiptDetail)
    {
        return toAjax(citicReceiptDetailService.updateCiticReceiptDetail(citicReceiptDetail));
    }

    /**
     * 删除中信银行回单信息（T+0/T+1）
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:remove')")
    @Log(title = "中信银行回单信息（T+0/T+1）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(citicReceiptDetailService.deleteCiticReceiptDetailByIds(ids));
    }

    /**
     * 下载中信银行回单信息（T+0/T+1）
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:add')")
    @Log(title = "中信银行下载回单信息（T+0/T+1）", businessType = BusinessType.INSERT)
    @PostMapping("/dowloadReceiptDetail")
    public AjaxResult dowloadReceiptDetail(@RequestBody CiticReceiptDownLoadQuery downLoadQuery) throws Exception {
        return AjaxResult.success(citicReceiptDetailService.dowloadReceiptDetail(downLoadQuery));
    }

    /**
     * 预览PDF回单文件
     */
    @PostMapping("/preview")
    public ResponseEntity<byte[]> previewPdf(@RequestBody CiticReceiptDownLoadQuery downLoadQuery) throws Exception {
        byte[] pdfContent = citicReceiptDetailService.getPdfContent(downLoadQuery);

        if (pdfContent == null || pdfContent.length == 0) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentLength(pdfContent.length);
        // 设置为内联显示，浏览器会尝试在页面中显示PDF
        headers.add("Content-Disposition", "inline; filename=receipt.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfContent);
    }

    /**
     * 下载PDF回单文件
     */
    @PostMapping("/download")
    public ResponseEntity<byte[]> downloadPdf(@RequestBody CiticReceiptDownLoadQuery downLoadQuery) throws Exception {
        byte[] pdfContent = citicReceiptDetailService.getPdfContent(downLoadQuery);

        if (pdfContent == null || pdfContent.length == 0) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentLength(pdfContent.length);
        // 设置为附件下载
        String fileName = "receipt_" + System.currentTimeMillis() + ".pdf";
        headers.add("Content-Disposition", "attachment; filename=" + fileName);

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfContent);
    }
}
