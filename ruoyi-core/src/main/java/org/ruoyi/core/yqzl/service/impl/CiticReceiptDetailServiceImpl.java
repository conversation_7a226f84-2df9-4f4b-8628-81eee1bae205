package org.ruoyi.core.yqzl.service.impl;

import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import com.ruoyi.common.utils.sign.Base64;
import org.ruoyi.core.yqzl.domain.CiticReceiptDetail;
import org.ruoyi.core.yqzl.domain.rep.CiticReceiptDetailResponse;
import org.ruoyi.core.yqzl.domain.rep.DowloadReceiptDetail;
import org.ruoyi.core.yqzl.domain.req.CiticReceiptDownLoadQuery;
import org.ruoyi.core.yqzl.domain.req.CiticReceiptDownLoadRowQuery;
import org.ruoyi.core.yqzl.domain.req.CiticReceiptQuery;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.domain.vo.CiticReceiptDetailVo;
import org.ruoyi.core.yqzl.mapper.CiticReceiptDetailMapper;
import org.ruoyi.core.yqzl.service.ICiticReceiptDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 中信银行回单信息（T+0/T+1）Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class CiticReceiptDetailServiceImpl implements ICiticReceiptDetailService
{
    private static final Logger logger = LoggerFactory.getLogger(CiticReceiptDetailServiceImpl.class);

    @Autowired
    private CiticReceiptDetailMapper citicReceiptDetailMapper;
    @Autowired
    private CiticXml citicXml;

    /**
     * 查询中信银行回单信息（T+0/T+1）
     *
     * @param id 中信银行回单信息（T+0/T+1）主键
     * @return 中信银行回单信息（T+0/T+1）
     */
    @Override
    public CiticReceiptDetail selectCiticReceiptDetailById(Long id)
    {
        return citicReceiptDetailMapper.selectCiticReceiptDetailById(id);
    }

    /**
     * 查询中信银行回单信息（T+0/T+1）列表
     *
     * @param citicReceiptDetail 中信银行回单信息（T+0/T+1）
     * @return 中信银行回单信息（T+0/T+1）
     */
    @Override
    public List<CiticReceiptDetailVo> selectCiticReceiptDetailList(CiticReceiptDetailVo citicReceiptDetail)
    {
        return citicReceiptDetailMapper.selectCiticReceiptDetailList(citicReceiptDetail);
    }

    /**
     * 新增中信银行回单信息（T+0/T+1）
     *
     * @param citicReceiptDetail 中信银行回单信息（T+0/T+1）
     * @return 结果
     */
    @Override
    public int insertCiticReceiptDetail(CiticReceiptDetail citicReceiptDetail)
    {
        return citicReceiptDetailMapper.insertCiticReceiptDetail(citicReceiptDetail);
    }

    /**
     * 修改中信银行回单信息（T+0/T+1）
     *
     * @param citicReceiptDetail 中信银行回单信息（T+0/T+1）
     * @return 结果
     */
    @Override
    public int updateCiticReceiptDetail(CiticReceiptDetail citicReceiptDetail)
    {
        return citicReceiptDetailMapper.updateCiticReceiptDetail(citicReceiptDetail);
    }

    /**
     * 批量删除中信银行回单信息（T+0/T+1）
     *
     * @param ids 需要删除的中信银行回单信息（T+0/T+1）主键
     * @return 结果
     */
    @Override
    public int deleteCiticReceiptDetailByIds(Long[] ids)
    {
        return citicReceiptDetailMapper.deleteCiticReceiptDetailByIds(ids);
    }

    /**
     * 删除中信银行回单信息（T+0/T+1）信息
     *
     * @param id 中信银行回单信息（T+0/T+1）主键
     * @return 结果
     */
    @Override
    public int deleteCiticReceiptDetailById(Long id)
    {
        return citicReceiptDetailMapper.deleteCiticReceiptDetailById(id);
    }

    /**
     * 批量新增中信银行回单信息（T+0/T+1）
     *
     * @param list 中信银行回单信息（T+0/T+1）
     * @return 结果
     */
    @Override
    public int batchInsertCiticReceiptDetail(List<CiticReceiptDetail> list)
    {
        return citicReceiptDetailMapper.batchInsertCiticReceiptDetail(list);
    }

    @Override
    public int GenerationDailyReceiptDetail() throws Exception {
        CiticReceiptQuery query = new CiticReceiptQuery();
        query.setQryType("2");
        query.setAccNo("8110701014101248723");
        query.setBillType("0");
        query.setMinAmt(new BigDecimal(0));
        query.setMaxAmt(new BigDecimal("9999999999"));
        query.setStartDate("20250301");
        query.setEndDate("20250620");
        query.setPageSize(10); // 每页最多10条记录

        // 存储所有查询到的数据
        List<CiticReceiptDetail> allDataList = new ArrayList<>();
        int currentStartNo = 1;
        Integer totalCount = null;

        // 分页查询所有数据
        while (true) {
            query.setStartNo(currentStartNo);
            String responseXMl = citicXml.responseXMl("DLEDDRSQ", query);
            CiticReceiptDetailResponse citicReceiptDetailXmlParseResult = XmlMessageParser.parseXmlToEntity(responseXMl, CiticReceiptDetailResponse.class);

            // 检查响应状态
            if (!"AAAAAAA".equals(citicReceiptDetailXmlParseResult.getStatus())) {
                String error = citicReceiptDetailXmlParseResult.getStatus() + ":" + citicReceiptDetailXmlParseResult.getStatusText();
                logger.error("[{}] 中信银行返回错误：{}",
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), error);
                throw new RuntimeException("中信银行返回错误:" + error);
            }

            // 第一次查询时获取总记录数
            if (totalCount == null) {
                totalCount = citicReceiptDetailXmlParseResult.getTotalCount();
            }

            // 获取当前页的数据
            List<CiticReceiptDetail> currentPageData = citicReceiptDetailXmlParseResult.getRow();

            // 中信接口返回数据为空时直接返回
            if (currentPageData == null || currentPageData.isEmpty()) {
                break;
            }

            allDataList.addAll(currentPageData);
            currentStartNo += currentPageData.size();

            // 如果当前页数据量小于页面大小，说明已经是最后一页
            if (currentPageData.size() < query.getPageSize()) {
                break;
            }

            // 如果已经获取了所有数据，跳出循环
            if (totalCount != null && allDataList.size() >= totalCount) {
                break;
            }
        }

        // 批量插入所有查询到的数据
        if (!allDataList.isEmpty()) {
            return batchInsertCiticReceiptDetail(allDataList);
        }

        return 0;
    }

    /**
     * 下载回单
     * @return
     * @throws Exception
     */
    @Override
    public DowloadReceiptDetail dowloadReceiptDetail(CiticReceiptDownLoadQuery query) throws Exception {

        String responseXMl = citicXml.responseXMlWithRowList("DLEDCDTD", query);
        DowloadReceiptDetail transferOrderResponse = XmlMessageParser.parseXmlToEntity(responseXMl, DowloadReceiptDetail.class);
        if ("AAAAAA".equals(transferOrderResponse.getStatus())) {
            throw new RuntimeException(transferOrderResponse.getStatusText());
        }

        // 处理fileContent：Base64解码 + ZIP解压缩
        if (transferOrderResponse.getFileConTent() != null && !transferOrderResponse.getFileConTent().isEmpty()) {
            try {
                // Base64解码
                byte[] zipBytes = Base64.decode(transferOrderResponse.getFileConTent());

                // ZIP解压缩，提取PDF文件
                byte[] pdfContent = extractPdfFromZip(zipBytes);

                if (pdfContent != null) {
                    transferOrderResponse.setPdfContent(pdfContent);
                    transferOrderResponse.setPdfFileName("receipt_" + System.currentTimeMillis() + ".pdf");

                    // 设置预览和下载URL（这些URL将在Controller中实现）
                    String baseUrl = "/yqzl/receipt/detail";
                    transferOrderResponse.setPreviewUrl(baseUrl + "/preview");
                    transferOrderResponse.setDownloadUrl(baseUrl + "/download");
                }
            } catch (Exception e) {
                logger.error("处理回单文件失败", e);
                throw new RuntimeException("处理回单文件失败: " + e.getMessage());
            }
        }

        return transferOrderResponse;
    }

    /**
     * 获取PDF文件字节数组（用于预览和下载）
     */
    @Override
    public byte[] getPdfContent(CiticReceiptDownLoadQuery query) throws Exception {
        DowloadReceiptDetail detail = dowloadReceiptDetail(query);
        return detail.getPdfContent();
    }

    /**
     * 从ZIP字节数组中提取PDF文件
     * @param zipBytes ZIP文件字节数组
     * @return PDF文件字节数组
     * @throws IOException
     */
    private byte[] extractPdfFromZip(byte[] zipBytes) throws IOException {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(zipBytes);
             ZipInputStream zis = new ZipInputStream(bais)) {

            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                // 查找PDF文件
                if (!entry.isDirectory() && entry.getName().toLowerCase().endsWith(".pdf")) {
                    // 读取PDF文件内容
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        baos.write(buffer, 0, len);
                    }
                    return baos.toByteArray();
                }
                zis.closeEntry();
            }
        }

        throw new RuntimeException("ZIP文件中未找到PDF文件");
    }
}
