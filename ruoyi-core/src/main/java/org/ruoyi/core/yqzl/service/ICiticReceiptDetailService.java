package org.ruoyi.core.yqzl.service;

import org.ruoyi.core.yqzl.domain.CiticReceiptDetail;
import org.ruoyi.core.yqzl.domain.rep.DowloadReceiptDetail;
import org.ruoyi.core.yqzl.domain.req.CiticReceiptDownLoadQuery;
import org.ruoyi.core.yqzl.domain.vo.CiticReceiptDetailVo;

import java.util.List;

/**
 * 中信银行回单信息（T+0/T+1）Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface ICiticReceiptDetailService
{
    /**
     * 查询中信银行回单信息（T+0/T+1）
     *
     * @param id 中信银行回单信息（T+0/T+1）主键
     * @return 中信银行回单信息（T+0/T+1）
     */
    public CiticReceiptDetail selectCiticReceiptDetailById(Long id);

    /**
     * 查询中信银行回单信息（T+0/T+1）列表
     *
     * @param citicReceiptDetail 中信银行回单信息（T+0/T+1）
     * @return 中信银行回单信息（T+0/T+1）集合
     */
    public List<CiticReceiptDetailVo> selectCiticReceiptDetailList(CiticReceiptDetailVo citicReceiptDetail);

    /**
     * 新增中信银行回单信息（T+0/T+1）
     *
     * @param citicReceiptDetail 中信银行回单信息（T+0/T+1）
     * @return 结果
     */
    public int insertCiticReceiptDetail(CiticReceiptDetail citicReceiptDetail);

    /**
     * 修改中信银行回单信息（T+0/T+1）
     *
     * @param citicReceiptDetail 中信银行回单信息（T+0/T+1）
     * @return 结果
     */
    public int updateCiticReceiptDetail(CiticReceiptDetail citicReceiptDetail);

    /**
     * 批量删除中信银行回单信息（T+0/T+1）
     *
     * @param ids 需要删除的中信银行回单信息（T+0/T+1）主键集合
     * @return 结果
     */
    public int deleteCiticReceiptDetailByIds(Long[] ids);

    /**
     * 删除中信银行回单信息（T+0/T+1）信息
     *
     * @param id 中信银行回单信息（T+0/T+1）主键
     * @return 结果
     */
    public int deleteCiticReceiptDetailById(Long id);

    /**
     * 批量新增中信银行回单信息（T+0/T+1）
     *
     * @param list 中信银行回单信息（T+0/T+1）
     * @return 结果
     */
    public int batchInsertCiticReceiptDetail(List<CiticReceiptDetail> list);

    public int GenerationDailyReceiptDetail() throws Exception;

    public DowloadReceiptDetail dowloadReceiptDetail(CiticReceiptDownLoadQuery query) throws Exception;

    /**
     * 获取PDF文件字节数组（用于预览和下载）
     * @param query 下载查询参数
     * @return PDF文件字节数组
     * @throws Exception
     */
    public byte[] getPdfContent(CiticReceiptDownLoadQuery query) throws Exception;
}
