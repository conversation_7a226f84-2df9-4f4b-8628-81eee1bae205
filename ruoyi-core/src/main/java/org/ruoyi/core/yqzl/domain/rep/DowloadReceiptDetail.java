package org.ruoyi.core.yqzl.domain.rep;

import lombok.Data;

/**
 * 回单下载
 */
@Data
public class DowloadReceiptDetail {
    /**
     * 状态
     */
    private String status;

    /**
     * 交易状态信息
     */
    private String statusText;

    /**
     * 回单汇总文件内容varchar(1048576)，需用base64解码后再进行zip解压缩-
     */
    private String fileConTent;

    private String fileName;

    private int size;

    /**
     * PDF文件字节数组（解压后的PDF内容）
     */
    private byte[] pdfContent;

    /**
     * PDF文件名
     */
    private String pdfFileName;

    /**
     * PDF预览URL
     */
    private String previewUrl;

    /**
     * PDF下载URL
     */
    private String downloadUrl;
}
