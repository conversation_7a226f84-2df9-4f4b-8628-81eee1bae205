# 中信银行回单PDF处理功能说明

## 功能概述

本次改造主要针对中信银行回单下载功能，实现了对返回的Base64编码ZIP文件的自动解压缩和PDF文件提取，提供了PDF预览和下载功能。

## 主要改动

### 1. 后端改动

#### 1.1 DowloadReceiptDetail类扩展
- 新增 `pdfContent` 字段：存储解压后的PDF文件字节数组
- 新增 `pdfFileName` 字段：PDF文件名
- 新增 `previewUrl` 字段：PDF预览URL
- 新增 `downloadUrl` 字段：PDF下载URL

#### 1.2 Service层改造
**ICiticReceiptDetailService接口新增方法：**
```java
/**
 * 获取PDF文件字节数组（用于预览和下载）
 * @param query 下载查询参数
 * @return PDF文件字节数组
 * @throws Exception
 */
public byte[] getPdfContent(CiticReceiptDownLoadQuery query) throws Exception;
```

**CiticReceiptDetailServiceImpl实现类改造：**
- 修改 `dowloadReceiptDetail` 方法，增加Base64解码和ZIP解压缩逻辑
- 新增 `getPdfContent` 方法，用于获取PDF文件字节数组
- 新增 `extractPdfFromZip` 私有方法，用于从ZIP文件中提取PDF

#### 1.3 Controller层新增接口
**新增PDF预览接口：**
```java
@PostMapping("/preview")
public ResponseEntity<byte[]> previewPdf(@RequestBody CiticReceiptDownLoadQuery downLoadQuery)
```

**新增PDF下载接口：**
```java
@PostMapping("/download")
public ResponseEntity<byte[]> downloadPdf(@RequestBody CiticReceiptDownLoadQuery downLoadQuery)
```

### 2. 前端改动

#### 2.1 API接口封装
创建了 `ruoyi-ui/src/api/yqzl/receiptDetail.js` 文件，封装了以下API：
- `downloadReceiptDetail`: 下载回单详情（包含PDF处理）
- `previewReceiptPdf`: 预览PDF回单文件
- `downloadReceiptPdf`: 下载PDF回单文件

#### 2.2 Vue组件示例
创建了 `ruoyi-ui/src/views/yqzl/receiptDetail/index.vue` 组件，实现了：
- 回单信息列表展示
- PDF预览功能（使用iframe）
- PDF下载功能
- 批量下载回单功能

## 使用方法

### 1. 后端API调用

#### 获取回单信息（包含PDF处理）
```http
POST /yqzl/receipt/detail/dowloadReceiptDetail
Content-Type: application/json

{
  "bankId": "银行号",
  "accNo": "账号",
  "isCurrDay": "2",
  "row": [
    {
      "brseqNo": "回单编号",
      "tranDate": "交易日期"
    }
  ]
}
```

#### PDF预览
```http
POST /yqzl/receipt/detail/preview
Content-Type: application/json

{
  "bankId": "银行号",
  "accNo": "账号",
  "isCurrDay": "2",
  "row": [
    {
      "brseqNo": "回单编号",
      "tranDate": "交易日期"
    }
  ]
}
```

#### PDF下载
```http
POST /yqzl/receipt/detail/download
Content-Type: application/json

{
  "bankId": "银行号",
  "accNo": "账号",
  "isCurrDay": "2",
  "row": [
    {
      "brseqNo": "回单编号",
      "tranDate": "交易日期"
    }
  ]
}
```

### 2. 前端使用示例

#### 预览PDF
```javascript
import { previewReceiptPdf } from "@/api/yqzl/receiptDetail";

// 预览PDF
handlePreview(row) {
  const downloadQuery = {
    bankId: this.queryParams.bankId,
    accNo: this.queryParams.accNo,
    isCurrDay: "2",
    row: [{
      brseqNo: row.brseqNo,
      tranDate: row.tranDate
    }]
  };
  
  previewReceiptPdf(downloadQuery).then(response => {
    const blob = new Blob([response], { type: 'application/pdf' });
    this.pdfUrl = window.URL.createObjectURL(blob);
    this.pdfPreviewDialog = true;
  });
}
```

#### 下载PDF
```javascript
import { downloadReceiptPdf } from "@/api/yqzl/receiptDetail";

// 下载PDF
handleDownload(row) {
  const downloadQuery = {
    bankId: this.queryParams.bankId,
    accNo: this.queryParams.accNo,
    isCurrDay: "2",
    row: [{
      brseqNo: row.brseqNo,
      tranDate: row.tranDate
    }]
  };
  
  downloadReceiptPdf(downloadQuery).then(response => {
    const blob = new Blob([response], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `receipt_${row.brseqNo}_${new Date().getTime()}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  });
}
```

## 技术要点

### 1. Base64解码
使用项目中现有的 `com.ruoyi.common.utils.sign.Base64` 工具类进行Base64解码。

### 2. ZIP解压缩
使用Java标准库的 `ZipInputStream` 进行ZIP文件解压缩，自动查找并提取第一个PDF文件。

### 3. 文件流处理
- 后端使用 `ResponseEntity<byte[]>` 返回文件流
- 前端使用 `Blob` 对象处理二进制数据
- 预览使用 `iframe` 加载PDF
- 下载使用动态创建的 `<a>` 标签

### 4. 内存管理
- 使用 `try-with-resources` 确保流的正确关闭
- 前端及时释放 `Blob URL` 避免内存泄漏

## 注意事项

1. **文件大小限制**：需要注意PDF文件大小，避免内存溢出
2. **错误处理**：已添加完善的异常处理机制
3. **浏览器兼容性**：PDF预览功能依赖浏览器的PDF支持
4. **安全性**：建议添加文件类型验证和大小限制
5. **性能优化**：对于大文件，可考虑使用流式处理

## 测试建议

1. 测试正常的Base64 ZIP文件处理
2. 测试异常情况（无效Base64、损坏的ZIP文件、无PDF文件等）
3. 测试大文件处理性能
4. 测试不同浏览器的PDF预览兼容性
5. 测试并发访问情况下的稳定性
