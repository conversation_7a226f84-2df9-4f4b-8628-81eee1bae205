/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : 凭证</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月01日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import request from '@/utils/request'
import Qs from "qs";

export default {
	list(params) {
    return request({
      url: '/voucher',
      method: 'get',
      params: params
    })
	},
	load(id, accountSetsId) {
    return request({
      url: '/voucher/'+`${id}`,
      method: 'get',
      params: {'accountSetsId': accountSetsId}
    })
	},
	loadCode(params) {
    return request({
      url: '/voucher/code',
      method: 'get',
      params: params
    })
	},
	summary(params) {
    return request({
      url: '/voucher/summary',
      method: 'get',
      params: params
    })
	},
	delete(id, params) {
    return request({
      url: '/voucher/'+`${id}`,
      method: 'delete',
      params: params
    })
	},
  valid(id, params) {
    return request({
      url: '/voucher/valid/'+`${id}`,
      method: 'post',
      params: params
    })
  },
	save(params = {}) {
    return request({
      url: '/voucher',
      method: 'post',
      data: params
    })
	},
	update(params = {}) {
    return request({
      url: '/voucher',
      method: 'put',
      data: params
    })
	},
	carryForwardMoney(params = {}) {
    return request({
      url: '/voucher/carryForwardMoney',
      method: 'post',
      data: Qs.stringify(params, {indices: false}),
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
	},
	finishingOffNo(params = {}) {
    return request({
      url: '/voucher/finishingOffNo',
      method: 'get',
      params: params
    })
	},
	beforeId(params = {}) {
    return request({
      url: '/voucher/beforeId',
      method: 'get',
      params: params
    })
	},
	nextId(params = {}) {
    return request({
      url: '/voucher/nextId',
      method: 'get',
      params: params
    })
	},
	batchDelete(params = {}) {
    return request({
      url: '/voucher/batchDelete',
      method: 'post',
      data: Qs.stringify(params, {indices: false}),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'}
    })
	},
	audit(params = {}) {
    return request({
      url: '/voucher/audit',
      method: 'post',
      data: Qs.stringify(params, {indices: false}),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'}
    })
	},
  batchAudit(params = {}) {
    return request({
      url: '/voucher/batchAudit',
      method: 'post',
      data: Qs.stringify(params, {indices: false}),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'}
    })
  },
	cancelAudit(params = {}) {
    return request({
      url: '/voucher/cancelAudit',
      method: 'post',
      data: Qs.stringify(params, {indices: false}),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'}
    })
	},
	import(params) {
    return request({
      url: '/voucher/import',
      method: 'post',
      data: params,
      headers: {'Content-Type': 'multipart/form-data'}
    })
	},
  reorderCode(params={}) {
    return request({
      url: '/voucher/reorderCode',
      method: 'post',
      params: params
    })
  }
}
