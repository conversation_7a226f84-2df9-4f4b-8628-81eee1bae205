/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月01日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import Qs from "qs";
import request from "@/utils/request";

export default {
	//账套管理
	accountSets: {
		list(params = {}) {
          return request({
            url: '/account-sets',
            method: 'get',
            params: params
          })
		},
		load(id) {
          return request({
            url: '/account-sets/'+`${id}`,
            method: 'get'
          })
		},
		delete(id, smsCode) {
          return request({
            url: '/account-sets/'+`${id}`+"/"+`${smsCode}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/account-sets',
            method: 'post',
            data: params
          })
		},
		addUser(params = {}) {
          return request({
            url: '/account-sets/addUser',
            method: 'get',
            params: params
          })
		},
		updateUserRole(params = {}) {
          return request({
            url: '/account-sets/updateUserRole',
            method: 'get',
            params: params
          })
		},
		addNewUser(params = {}) {
          return request({
            url: '/account-sets/addNewUser',
            method: 'get',
            params: params
          })
		},
		removeUser(uid) {
          return request({
            url: '/account-sets/removeUser/'+`${uid}`,
            method: 'get'
          })
		},
		checkUse(asid) {
          return request({
            url: '/account-sets/checkUse',
            method: 'get',
            params: {asid}
          })
		},
		update(params = {}) {
          return request({
            url: '/account-sets',
            method: 'put',
            data: params
          })
		},
		identification(params = {}) {
          return request({
            url: '/account-sets/identification',
            method: 'post',
            data: Qs.stringify(params)
          })
		},
		handOver(params = {}) {
          return request({
            url: '/account-sets/handOver',
            method: 'post',
            data: Qs.stringify(params)
          })
		},
		updateEncode(params = {}) {
          return request({
            url: '/account-sets/updateEncode',
            method: 'post',
            data: Qs.stringify(params),
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
          })
		}
	},
	//凭证字
	voucherWord: {
		list(params = {}) {
          return request({
            url: '/voucher-word',
            method: 'get',
            params: params
          })
		},
		load(id) {
          return request({
            url: '/voucher-word/'+`${id}`,
            method: 'get'
          })
		},
		delete(id) {
          return request({
            url: '/voucher-word/'+`${id}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/voucher-word',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/voucher-word',
            method: 'put',
            data: params
          })
		}
	},
	//币别
	currency: {
		list(params = {}) {
          return request({
            url: '/currency',
            method: 'get',
            params: params
          })
		},
		load(id) {
          return request({
            url: '/currency/'+`${id}`,
            method: 'get'
          })
		},
		delete(id) {
          return request({
            url: '/currency/'+`${id}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/currency',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/currency',
            method: 'put',
            data: params
          })
		}
	},
	//科目
	subject: {
		list(params = {}) {
          return request({
            url: '/subject',
            method: 'get',
            params: params
          })
		},
		load(id) {
          return request({
            url: '/subject/'+`${id}`,
            method: 'get'
          })
		},
		delete(id, params) {
          return request({
            url: '/subject/'+`${id}`,
            method: 'delete',
            params: params
          })
		},
		save(params = {}) {
          return request({
            url: '/subject',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/subject',
            method: 'put',
            data: params
          })
		},
		voucherSelect(params = {}) {
          return request({
            url: '/subject/voucher/select',
            method: 'get',
            params: params
          })
		},
		loadByCode(code = [], checkData = {}) {
          return request({
            url: '/subject/loadByCode',
            method: 'post',
            data: Qs.stringify(Object.assign(checkData, {code: code.filter(v => !!v)}), {indices: false}), headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
          })
		},
		checkUse(id) {
          return request({
            url: '/subject/checkUse/'+`${id}`,
            method: 'get'
          })
		},
		balance(params = {}) {
          return request({
            url: '/subject/balance',
            method: 'get',
            params: params
          })
		},
		import(params) {
          return request({
            url: '/subject/import',
            method: 'post',
            data: params,
            headers: {'Content-Type': 'multipart/form-data'}
          })
		},
    copySubject(params = {}) {
      return request({
        url: '/subject/copySubject',
        method: 'post',
        params: params
      })
    },
    codeSort(params = {}) {
      return request({
        url: '/subject/codeSort',
        method: 'post',
        params: params
      })
    },
    getProjectList(params = {}){
      return request({
        url: '/subject/getProjectList',
        method: 'get',
        params: params
      })
    },
    bindProject(params = {}) {
      return request({
        url: '/subject/bindProject',
        method: 'post',
        data: params
      })
    },
	},
	//科目
	initialBalance: {
		list(params = {}) {
          return request({
            url: '/initial-balance',
            method: 'get',
            params: params
          })
		},
		load(id) {
          return request({
            url: '/initial-balance/'+`${id}`,
            method: 'get'
          })
		},
		delete(id) {
          return request({
            url: '/initial-balance/'+`${id}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/initial-balance',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/initial-balance',
            method: 'put',
            data: params
          })
		},
		trialBalance(params) {
          return request({
            url: '/initial-balance/trialBalance',
            method: 'get',
            params: params
          })
		},
		saveAuxiliary(params) {
          return request({
            url: '/initial-balance/auxiliary',
            method: 'post',
            data: params
          })
		}
	},
	//辅助核算类别
	accountingCategory: {
		list(params = {}) {
          return request({
            url: '/accounting-category',
            method: 'get',
            params: params
          })
		},
		load(id, params) {
          return request({
            url: '/accounting-category/'+`${id}`,
            method: 'get',
            params: params
          })
		},
		delete(id) {
          return request({
            url: '/accounting-category/'+`${id}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/accounting-category',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/accounting-category',
            method: 'put',
            data: params
          })
		},
		import(params) {
          return request({
            url: '/accounting-category/import',
            method: 'post',
            data: params,
            headers: {'Content-Type': 'multipart/form-data'}
          })
		}
	},
	accountingCategoryDetails: {
		list(params = {}) {
          return request({
            url: '/accounting-category-details',
            method: 'get',
            params: params
          })
		},
		load(id) {
          return request({
            url: '/accounting-category-details/'+`${id}`,
            method: 'get',
          })
		},
		delete(id) {
          return request({
            url: '/accounting-category-details/'+`${id}`,
            method: 'delete',
          })
		},
		clearData(id, params) {
          return request({
            url: '/accounting-category-details/clear/'+`${id}`,
            method: 'delete',
            params: params
          })
		},
		save(params = {}) {
          return request({
            url: '/accounting-category-details',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/accounting-category-details',
            method: 'put',
            data: params
          })
		},
		loadAuxiliaryAccountingData(params) {
          return request({
            url: '/accounting-category-details/loadAuxiliaryAccountingData',
            method: 'post',
            data: params
          })
		}
	},
	//用户
	user: {
		list() {
          return request({
            url: '/user',
            method: 'get'
          })
		},
		load(id) {
          return request({
            url: '/user/'+`${id}`,
            method: 'get'
          })
		},
		delete(id) {
          return request({
            url: '/user/'+`${id}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/user',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/user',
            method: 'put',
            data: params
          })
		},
    getUserInfo(params = {}) {
      return request({
        url: '/user/getUserInfo',
        method: 'get',
        params: params
      })
    },
    queryRoleTypeUser(params = {}){
      return request({
        url: '/account-sets/queryRoleTypeUser',
        method: 'get',
        params: params
      })
    }
	},
	//模板
	voucherTemplate: {
		list() {
          return request({
            url: '/voucher-template',
            method: 'get'
          })
		},
		load(id) {
          return request({
            url: '/voucher-template/'+ `${id}`,
            method: 'get'
          })
		},
		delete(id) {
          return request({
            url: '/voucher-template/'+ `${id}`,
            method: 'delete'
          })
		},
		save(params = {}) {
          return request({
            url: '/voucher-template',
            method: 'post',
            data: params
          })
		},
		update(params = {}) {
          return request({
            url: '/voucher-template',
            method: 'put',
            data: params
          })
		}
	}
}
