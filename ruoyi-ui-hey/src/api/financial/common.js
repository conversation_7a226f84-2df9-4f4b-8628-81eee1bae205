/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月01日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import Qs from "qs";
import request from "@/utils/request";

export default {
	login(params = {}) {
    return request({
      url: '/login',
      method: 'post',
      data: Qs.stringify(params)
    })
	},
	register(params = {}) {
    return request({
      url: '/register',
      method: 'post',
      data: Qs.stringify(params)
    })
	},
	logout() {
    return request({
      url: '/logout',
      method: 'get',
    })
	},
	init(params = {}) {
    return request({
      url: '/init',
      method: 'get',
      params: params
    })
	},
	updateUser(params = {}) {
    return request({
      url: '/updateUser',
      method: 'post',
      data: Qs.stringify(params)
    })
	},
	updatePwd(params = {}) {
    return request({
      url: '/updatePwd',
      method: 'post',
      data: Qs.stringify(params)
    })
	},
	changePhoneNumber(params = {}) {
    return request({
      url: '/changePhoneNumber',
      method: 'post',
      data: Qs.stringify(params)
    })
	},
	sendMsg(mobile) {
    return request({
      url: '/sendMsg/'+`${mobile}`,
      method: 'get'
    })
	},
	regMsg(mobile) {
    return request({
      url: '/regMsg/'+`${mobile}`,
      method: 'get'
    })
	},
	changeAccountSets(accountSetsId) {
    return request({
      url: '/changeAccountSets?accountSetsId='+`${accountSetsId}`,
      method: 'get'
    })
	},
	resetPassword(params = {}) {
    return request({
      url: '/resetPassword',
      method: 'post',
      data: Qs.stringify(params)
    })
	},
  getConfig(){
    return request({
      url: 'financial/getConfig',
      method: 'get',
    })
  },
  getUser(){
    return request({
      url: 'financial/getUser',
      method: 'get',
    })
  },
  getMenu(params){
    return request({
      url: 'financial/getMenu',
      method: 'get',
      params: params
    })
  }
}
