/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月10日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import request from "@/utils/request";

export default {
	voucherCount() {
    return request({
      url: '/home/<USER>/count',
      method: 'get',
    })
	},
	chart: {
		revenueProfit() {
      return request({
        url: '/home/<USER>/revenueProfit',
        method: 'get',
      })
		},
		cost() {
      return request({
        url: '/home/<USER>/cost',
        method: 'get',
      })
		},
		cash() {
      return request({
        url: '/home/<USER>/cash',
        method: 'get',
      })
		}
	}
}
