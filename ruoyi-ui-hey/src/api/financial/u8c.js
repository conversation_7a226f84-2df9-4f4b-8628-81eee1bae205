import request from '@/utils/request'
export default {
    list(data) {
        return request({
            url: '/U8C/suppliers/list',
            method: 'post',
            data
        })
    },
    ruleAdd(data) {
        return request({
            url: '/U8C/rule',
            method: 'post',
            data
        })
    },
    singlePushU8C(data) {
        return request({
            url: '/U8C/setVoucher/singlePushU8C',
            method: 'post',
            data
        })
    },
    getChooseList(params) {
        return request({
            url: '/U8C/rule/getChooseList',
            method: 'get',
            params
        })
    },
    singlePushU8CMerge(data) {
        return request({
            url: '/U8C/setVoucher/singlePushU8CMerge',
            method: 'post',
            data
        })
    },
    pushSingleVoucherMerge(data) {
        return request({
            url: '/U8C/setVoucher/pushSingleVoucherMerge',
            method: 'post',
            data
        })
    },
    pushSingleVoucher(data) {
        return request({
            url: '/U8C/setVoucher/pushSingleVoucher',
            method: 'post',
            data
        })
    },
    getRecordList(data) {
        return request({
            url: '/U8C/rule/getRecord',
            method: 'post',
            data
        })
    },
    ruleEdit(data) {
        return request({
            url: '/U8C/rule',
            method: 'put',
            data
        })
    },
    bankList(params) {
        return request({
            url: '/U8C/bankaccount/list',
            method: 'get',
            params
        })
    },
    setVoucherList(params) {
        return request({
            url: '/U8C/setVoucher/list',
            method: 'get',
            params
        })
    },
    getRulesEnum(params) {
        return request({
            url: '/U8C/rule/getRulesEnum',
            method: 'get',
            params
        })
    },
    ruleList(params) {
        return request({
            url: '/U8C/rule/list',
            method: 'get',
            params
        })
    },
    bankaccountAddEdit(data) {
        return request({
            url: '/U8C/bankaccount',
            method: 'post',
            data
        })
    },
    getU8CBankaccount(data) {
        return request({
            url: '/U8C/bankaccount/getU8CBankaccount',
            method: 'post',
            data
        })
    },
    suppliersEidt(data) {
        return request({
            url: '/U8C/suppliers',
            method: 'post',
            data
        })
    },
    getU8CGlorgbook(data) {
        return request({
            url: '/U8C/Ledger/getU8CGlorgbook',
            method: 'post',
            data
        })
    },
    saveLedger(data) {
        return request({
            url: '/U8C/Ledger/saveLedger',
            method: 'post',
            data
        })
    },
    getDicts(dictType) {
        return request({
            url: '/system/dict/data/type/' + dictType,
            method: 'get'
        })
    },
    selectUpdateAcc(params) {
        return request({
            url: '/U8C/Ledger/selectUpdate',
            method: 'get',
            params
        })
    },
    selectUpdate(params) {
        return request({
            url: '/U8C/suppliers/selectUpdate',
            method: 'get',
            params
        })
    },
    selectUpdateBank(data) {
        return request({
            url: '/U8C/bankaccount/selectUpdate',
            method: 'post',
            data
        })
    },
    getWisdomBusi(params) {
        return request({
            url: '/U8C/suppliers/getWisdomBusi',
            method: 'get',
            params: params
        })
    },
    getLedgerList(params) {
        return request({
            url: '/U8C/Ledger/getLedgerList',
            method: 'get',
            params: params
        })
    },
    setVoucherDetail(data) {
        return request({
            url: '/U8C/setVoucher/getPushRecord',
            method: 'post',
            data

        })
    },
    deleterule(id) {
        return request({
            url: '/U8C/rule/' + id,
            method: 'delete',

        })
    },

}