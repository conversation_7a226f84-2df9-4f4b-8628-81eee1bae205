/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月25日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import request from "@/utils/request";

export default {
	loadSubject(params = {}) {
    return request({
      url: '/accountBook/list',
      method: 'get',
      params: params
    })
	},
	loadVoucherDetails(params = {}) {
    return request({
      url: '/accountBook/details',
      method: 'get',
      params: params
    })
	},
	loadGeneralLedger(params = {}) {
    return request({
      url: '/accountBook/generalLedger',
      method: 'get',
      params: params
    })
	},
	loadSubjectBalance(params = {}) {
    return request({
      url: '/accountBook/subjectBalance',
      method: 'get',
      params: params
    })
	},
	loadSubjectSummary(params = {}) {
    return request({
      url: '/accountBook/subjectSummary',
      method: 'get',
      params: params
    })
	},
	loadAuxiliaryDetails(params = {}) {
    return request({
      url: '/accountBook/auxiliaryDetails',
      method: 'get',
      params: params
    })
	},
	loadAuxiliaryList(params = {}) {
    return request({
      url: '/accountBook/auxiliaryList',
      method: 'get',
      params: params
    })
	},
	loadAuxiliaryBalance(params = {}) {
    return request({
      url: '/accountBook/auxiliaryBalance',
      method: 'get',
      params: params
    })
	},
  getEarliestTime(accountSetsId) {
    return request({
      url: '/accountBook/getEarliestTime?accountSetsId='+accountSetsId,
      method: 'get',
    })
  },
  loadJournalDetails(params = {}) {
    return request({
      url: '/accountBook/journalDetails',
      method: 'get',
      params: params
    })
  },
}
