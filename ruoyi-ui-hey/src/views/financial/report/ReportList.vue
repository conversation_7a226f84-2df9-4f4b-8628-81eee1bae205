<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @change="accountsSetsChange"></Tabs>-->
    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar"><span class="h-panel-title">报表</span></div>
		<div class="h-panel-body">
			<Row :space="20">
				<Cell width="3" v-for="item in dataList" :key="item.id">
					<router-link :to="{name:'ReportView',params:{reportId:item.id, accountSetsId: item.accountSetsId}}" tag="div" class="report-item">{{item.name}}</router-link>
				</Cell>
			</Row>
		</div>
	</app-content>
</template>

<script>
import moment from "moment";

export default {
	name: "ReportList",
	data() {
		return {
			loading: false,
			templateModel: false,
			form: {
				name: '',
				templateKey: '',
				type: 0
			},
			validationRules: {
				required: ['name', 'templateKey'],
				rules: {
					templateKey: {
						valid: {
							pattern: /^[a-z]+$/i,
							message: '只能为纯字母'
						}
					}
				}
			},
			dataList: [],
      //账套选中标识
      accountsSetsSelected: null,
      accountSetsList: [],
      accountSetsName: "",
		}
	},
  computed: {
    currentAccountSets() {
      return this.$store.state.financial.currentAccountSets;
    },
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    }
  },
	methods: {
		loadList() {
			this.$api.report.template.list({account_sets_id: this.currentAccountSets.id}).then(({data}) => {
				this.dataList = data;
			});
		},
		save() {
			let validResult = this.$refs.templateForm.valid();
			if (validResult.result) {
				this.loading = true;
				this.$api.report.template.save(this.form).then(({data}) => {
					this.loadList();
				}).finally(() => {
					this.loading = false;
					this.templateModel = false;
				});
			}
		},
		remove(data) {
			this.$Confirm("确认删除?").then(() => {
				this.$api.report.template.delete(data.id).then(() => {
					this.loadList();
				})
			})
		},
    accountsSetsChange(data) {
      //查询
      this.$store.dispatch('init', data.key).then(() => {
        this.loadList();
      })
    },
    pushDatas(){
      this.accountSetsList = [];
      //账套列表
      this.myAccountSets.forEach(e=>{
        let accountSet = {
          title: e.companyName,
          key: e.id
        }
        this.accountSetsList.push(accountSet)
      })
      this.accountsSetsSelected = this.currentAccountSets.id;
    },
    searchAccountSets(event){
      if(this.accountSetsName != ''){
        let accountSetsMap = {};
        this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
        //查询
        this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
          this.accountSetsList = [];
          let accountSet = {
            title: this.currentAccountSets.companyName,
            key: this.currentAccountSets.id
          }
          this.accountSetsList.push(accountSet)
          this.loadList();
        })

      }else{
        this.pushDatas()
      }
    },
	},
	mounted() {
		this.loadList();
    //账套列表
    this.pushDatas()
	}
}
</script>

<style scoped lang="less">
	.report {
		&-item {
			border-radius: 5px;
			border: 1px solid @primary-color;
			text-align: center;
			padding: 10px;

			&:hover {
				cursor: pointer;
				box-shadow: 0 0 20px 2px rgba(102, 100, 100, 0.24), 0px 2px 4px 0px rgba(199, 198, 198, 0.5);
			}
		}
	}
</style>
