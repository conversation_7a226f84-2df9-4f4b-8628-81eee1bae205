<template>
      <Select v-if="currentAccountSets" class="app-header-message-vue" v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="changeAccountSets">
      </Select>
</template>
<script>
	import {mapState} from 'vuex';

	export default {
		name: 'AppHeaderAccountSets',
		data() {
			return {
				messageList: [],
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
			};
		},
    mounted() {
      //账套列表
      this.pushDatas()
    },
		methods: {
			changeAccountSets(data) {
        this.$Loading("正在切换，请稍后...");
        this.$store.dispatch('init', data.key).then(() => {
          this.$Message('切换成功！');
          window.location.replace("/")
        })
			},
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
		},
		computed: {
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      },
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      }
		}
	};
</script>
<style lang='less'>
	.app-header-message-vue {
    height: 33px;
    width: 300px;
		float: left;
    border: #dadada solid 1px;
		margin-right: 15px;
		margin-top: 15px;

		.h-dropdowncustom-show {
			height: @layout-header-height;

			.app-header-icon-item {
				margin-right: 0;
			}

			&.h-pop-trigger {
				background: @hover-background-color;
			}
		}
	}

	.app-account-sets-dropdown-dropdown-container {
		width: 300px;
		min-height: 300px;

		.common-list-container {
			.common-list-item {
				cursor: pointer;
				padding: 10px;

				&:hover {
					background: @hover-background-color;
				}

				&.current {
					background: @primary-color;
					color: @white-color;
				}
			}
		}
	}
</style>
