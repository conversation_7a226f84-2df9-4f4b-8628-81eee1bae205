<template>
	<div class="app-logo">
		<router-link to="/">
      <span>智慧财务系统</span>
<!--      <span class="app-logo-icon"></span>-->
    </router-link>
	</div>
</template>
<script>
	export default {
		name: "App<PERSON><PERSON>"
	};
</script>
<style lang='less'>
	.app-logo {
		font-family: Futura, Helvetica Neue For Number, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
		padding: 0 24px;
		line-height: @layout-header-height;
		height: @layout-header-height;
		overflow: hidden;
		white-space: nowrap;
		transition: padding .3s;
		text-align: center;

		.app-logo-text {
			font-size: 20px;
			vertical-align: middle;
			color: #FFF;
			font-weight: bold;
		}

		.app-logo-icon {
			background: url(../../../assets/financial/images/logo.png);
			background-size: contain;
			height: 40px;
			width: 80px;
			display: inline-block;
			margin-right: 10px;
			transition: .3s;
			vertical-align: middle;
		}
	}

	.h-layout-sider-collapsed {
		.app-logo-text {
			display: none;
		}

		.app-logo-icon {
			margin-left: 15px;
		}
	}
</style>
