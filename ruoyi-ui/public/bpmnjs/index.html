<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>在线绘制流程</title>
  <link rel="icon" href="favicon.ico">
  <link rel="stylesheet" href="css/diagram-js.css" />
  <link rel="stylesheet" href="vendor/bpmn-font/css/bpmn-embedded.css" />
  <link rel="stylesheet" href="css/app.css" />

  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css">

  <style>
    .item {
      display: none;
      cursor: pointer;
    }

    .bjs-powered-by {
      display: none;
    }

    .buttons>li {
      /* display: inline-block; */
      margin-right: 10px;
      height: 26px;
      line-height: 26px;
      float: left;
    }

    .buttons>li>a.btn {
      background: #00BCD4;
      border: none;
      outline: none;
      padding: 0px 10px;
      color: #fff;
      display: inline-block;
      opacity: 1;
      height: 26px;
      font-size: 14px;
      line-height: 26px;
    }

    .label {
      background: #00BCD4;
      border: none;
      outline: none;
      padding: 0px 10px;
      color: #fff;
      display: inline-block;
      cursor: pointer;
      opacity: 1;
      height: 26px;
      font-size: 14px;
      line-height: 26px;
    }

    .sy-mask {
      width: 100%;
      height: 100%;
      position: fixed;
      background: rgba(0, 0, 0, 0.8);
      left: 0;
      top: 0;
      z-index: 1000;
      display: none;
    }

    .sy-alert {
      position: fixed;
      display: none;
      background: #fff;
      border-radius: 5px;
      overflow: hidden;
      width: 300px;
      max-width: 90%;
      max-height: 80%;
      left: 0;
      right: 0;
      margin: 0 auto;
      z-index: 9999
    }

    .sy-alert.animated {
      -webkit-animation-duration: .3s;
      animation-duration: .3s
    }

    .sy-alert .sy-title {
      height: 45px;
      color: #333;
      line-height: 45px;
      font-size: 15px;
      border-bottom: 1px solid #eee;
      padding: 0 12px
    }

    .sy-alert .sy-content {
      padding: 20px;
      text-align: center;
      font-size: 14px;
      line-height: 24px;
      color: #666;
      overflow-y: auto
    }

    .sy-alert .sy-btn {
      height: 50%;
      border-top: 1px solid #eee;
      overflow: hidden
    }

    .sy-alert .sy-btn button {
      float: left;
      border: 0;
      color: #333;
      cursor: pointer;
      background: #fff;
      width: 50%;
      line-height: 45px;
      font-size: 15px;
      text-align: center
    }

    .sy-alert .sy-btn button:nth-child(1) {
      color: #888;
      border-right: 1px solid #eee
    }

    .sy-alert.sy-alert-alert .sy-btn button {
      float: none;
      width: 100%
    }

    .sy-alert.sy-alert-tips {
      text-align: center;
      width: 150px;
      background: rgba(0, 0, 0, 0.7)
    }

    .sy-alert.sy-alert-tips .sy-content {
      padding: 8px;
      color: #fff;
      font-size: 14px
    }

    .sy-alert.sy-alert-model .sy-content {
      text-align: left
    }

    .sy-alert.sy-alert-model .sy-content .form .input-item {
      margin-bottom: 12px;
      position: relative
    }

    .sy-alert.sy-alert-model .sy-content .form .input-item input {
      display: block;
      position: relative;
      width: 100%;
      border: 1px solid #eee;
      padding: 10px
    }

    .sy-alert.sy-alert-model .sy-content .form .input-item .getcode {
      border: 0;
      top: 0;
      right: 0;
      position: absolute;
      background: 0;
      line-height: 37px;
      color: #f60;
      width: 100px;
      text-align: center
    }

    .gangwei-search {
      display: flex;
      margin-bottom: 12px;
      align-items: center;
    }

    .gangwei-search input {
      height: 30px;
      border: 1px solid #dfdfdf;
      border-radius: 4px;
      margin-right: 12px;
      padding-left: 12px;
    }
    input[type=text]:focus {
				outline: none;
			}
  </style>
</head>

<body>
  <div class="content with-diagram" id="js-drop-zone">
    <div class="message error">
      <div class="note">
        <p>无法显示bpms2.0</p>
        <div class="details">
          <span>错误详细信息</span>
          <pre></pre>
        </div>
      </div>
    </div>
    <div class="canvas" id="js-canvas"></div>
    <div class="properties-panel-parent" id="js-properties-panel"></div>
  </div>
  <ul class="buttons">
    <li class="item upload">
      <form id="form1" name="myForm" onsubmit="return false" method="post" enctype="multipart/form-data" title="上传文件">
        <input type="file" name="uploadFile" id="uploadFile" accept=".bpmn" style="display: none">
        <label class="label" for="uploadFile">导入</label>
      </form>
    </li>
    <li class="item download">
      <a class="btn" href id="downloadBpmn">导出</a>
    </li>
    <li class="item submit">
      <a class="btn" id="js-download-diagram">
        部署
      </a>
    </li>
  </ul>
  <div class="sy-alert sy-alert-model animated" sy-enter="zoomIn" sy-leave="zoomOut" sy-type="confirm" sy-mask="true"
    id="alert">
    <div class="sy-title">部署流程</div>
    <div class="sy-content">
      确认是否部署该流程
      <!--    <div class="form">-->
      <!--      <p class="input-item"><input id="deploymentName" type="text" placeholder="请输入流程名称"></p>-->
      <!--    </div>-->
    </div>
    <div class="sy-btn">
      <button id="sure">确定</button>
      <button class="cancel">取消</button>
    </div>
  </div>
  <div class="sy-mask cancel"></div>

  <!-- singleUserModal -->
  <div class="modal fade" id="singleUserModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">请选择人员</h5>
        </div>
        <div class="modal-body">
          <div class="gangwei-search">
            <input type="text" class="gangwei-input" id="people" placeholder="请输入关键字">
            <button type="button" class="btn btn-primary" onclick="singleUserDlgSearch()">搜索</button>
          </div>
          <div class="list-group" id="singleUserList" role="tablist">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
            onclick="singleUserDlgCancel()">取消</button>
          <button type="button" class="btn btn-primary" onclick="singleUserDlgConfirm()">确认</button>
        </div>
      </div>
    </div>
  </div>

  <!-- singleGroupModal -->
  <div class="modal fade" id="singleGroupModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">请选择岗位</h5>
        </div>
        <div class="modal-body">
          <div class="gangwei-search">
            <input type="text" class="gangwei-input" id="gangwei" placeholder="请输入关键字">
            <button type="button" class="btn btn-primary" onclick="singleGroupDlgSearch()">搜索</button>
          </div>
          <div class="list-group" id="singleGroupList" role="tablist">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
            onclick="singleGroupDlgCancel()">取消</button>
          <button type="button" class="btn btn-primary" onclick="singleGroupDlgConfirm()">确认</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="index.js"></script>
</body>

</html>