<template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel>
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from "@/components/RightPanel";
import { AppMain, Navbar, Settings, Sidebar, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
    variables() {
      return variables;
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.checkEdition();
    },
    checkEdition() {
      let reloadTimer = null;
      let isRefreshing = false;
      const getAppHash = (scripts) => {
        let localVersion = "";
        for (let i = 0; i < scripts.length; i++) {
          let src = scripts[i].getAttribute("src");
          if (src && src.indexOf("app.") != -1) {
            // 正则返回中间版本号(如果没有，返回空)
            let regRes = /app\.(.*?).js/;
            if (regRes.test(src)) {
              localVersion = regRes.exec(src)[1];
            }
          }
        }
        return localVersion;
      };
      // 获取本地的app.js版本号
      const getLocalHash = () => {
        return getAppHash(document.getElementsByTagName("script"));
      };
      // 获取线上的app.js版本号
      const checkHash = () => {
        return new Promise((resolve, reject) => {
          // 加上时间戳，防止缓存
          fetch("/?_time=" + Date.now(), {
            method: "GET",
            headers: {
              "Cache-Control": "no-cache", // 避免缓存
            },
          })
            .then(async (res) => {
              if (!res.ok) {
                throw new Error("Failed to fetch page");
              }
              let html = await res.text(); //转成字符串判断
              let doc = new DOMParser().parseFromString(html, "text/html");
              let newVersion = getAppHash(doc.getElementsByTagName("script"));
              resolve(newVersion);
            })
            .catch((err) => {
              console.log("获取版本号失败", err);
              reject(err);
            });
        });
      };
      const reloads = async () => {
        if (isRefreshing) return; // 如果已经在刷新，直接返回
        isRefreshing = true;
        try {
          let localVersion = getLocalHash();
          let newVersion = await checkHash();
          if (localVersion != newVersion) {
            window.location.replace(window.location.href + '?_=' + new Date().getTime()) 
          }
        } catch (error) {
          console.error("检查版本号失败:", error);
        } finally {
          isRefreshing = false; // 刷新完成后恢复标志
        }
      };
      reloads();
      const startReloadTimer = () => {
        if (reloadTimer) {
          clearInterval(reloadTimer); // 如果定时器已经存在，先清除
        }
        reloadTimer = setInterval(async () => {
          await reloads();
        }, 1000 * 60 * 60 * 8); // 每8小时执行一次
      };
      // 定时执行，自动更新逻辑(每8小时检测一次)
      startReloadTimer();
    },
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
