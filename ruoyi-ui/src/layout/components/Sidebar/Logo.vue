<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}" :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <!-- <h1 v-else class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }} </h1> -->
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <!-- <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }} </h1> -->
      </router-link>
    </transition>
  </div>
</template>

<script>
import zxlogoImg from '@/assets/logo/zxLog.png'
import logoImg from '@/assets/logo/logo.png'
import sjptlogo from '@/assets/logo/sjLOGO.jpg'
import hnzhlogo from '@/assets/logo/hnzhlogo.jpg'
import yndflogo from '@/assets/logo/yndflogo.jpg'
import zbgxLOGO2 from '@/assets/logo/zbgxLOGO2.jpg'

import cqtrdlogo from '@/assets/logo/CQTRD.jpg'
import fjcwgxdlogo from '@/assets/logo/FJCWGXD.jpg'
import fjdydlogo from '@/assets/logo/FJDYD.jpg'
import gsjddlogo from '@/assets/logo/GSJDD.jpg'
import hbfcdlogo from '@/assets/logo/HBFCD.jpg'
import hnzhdlogo from '@/assets/logo/HNZHD.jpg'
import hnztdlogo from '@/assets/logo/HNZTD.jpg'
import jhrsdlogo from '@/assets/logo/JHRSD.jpg'
import qhctdlogo from '@/assets/logo/QHCTD.jpg'
import yndfdlogo from '@/assets/logo/YNDFD.jpg'
import zbgxdlogo from '@/assets/logo/ZBGXD.jpg'

import variables from '@/assets/styles/variables.scss'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    }
  },
  data() {
    return {
      // title: null,
      logo: null
      // title: '中保国信数据平台',
      // logo: logoImg
    }
  },mounted(){
    this.getuserCustNo()
  },methods:{
    getuserCustNo(){
      var custno =  window.sessionStorage.getItem('userCustNo');
      if(custno=='ZBGX'){
        // this.title = '中保国信数据平台',
        this.logo = zbgxdlogo
      }else if(custno == 'HNZH'){
        // this.title = '湖南樽昊数据平台',
        this.logo = hnzhdlogo
      }else if(custno == 'YNDF'){
          this.logo = yndfdlogo
      }
      else if(custno == 'CQTR'){
          this.logo = cqtrdlogo
      }else if(custno == 'FJCWGX'){
          this.logo = fjcwgxdlogo
      }else if(custno == 'FJDY'){
          this.logo = fjdydlogo
      }else if(custno == 'GSJD'){
          this.logo = gsjddlogo
      }else if(custno == 'HBFC'){
          this.logo = hbfcdlogo
      }else if(custno == 'HNZT'){
          this.logo = hnztdlogo
      }else if(custno == 'JHRS'){
          this.logo = jhrsdlogo
      }else if(custno == 'QHCT'){
          this.logo = qhctdlogo
      }

    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 100px;
  line-height: 100px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 240px;
      height: 60px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 80px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
