import Layout from "@/layout";

export default [
  {
    path: "/debtConversionOther",
    name: "DebtConversionOther",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "debtEquityManagement/fileImportAdd",
        component: () =>
          import(
            "@/views/debtConversion/debtEquityManagement/fileImport/components/addFile"
          ),
        name: "FileImportAdd",
        meta: {
          title: "新增债转文件",
          activeMenu: "/debtConversion/debtConversionManagent/fileImport",
          noCache: true,
        },
      },
      {
        path: "debtEquityManagement/fileDetail/:id",
        component: () =>
          import(
            "@/views/debtConversion/debtEquityManagement/fileImport/components/fileDetail"
          ),
        name: "FileDetail",
        meta: {
          title: "",
          activeMenu: "/debtConversion/debtConversionManagent/fileImport",
          noCache: true,
        },
      },
      {
        path: "batchInvoicingApplication/fileImportAdd",
        component: () =>
          import(
            "@/views/debtConversion/batchInvoicingApplication/components/addFile"
          ),
        name: "FileImportAddBatch",
        meta: {
          title: "新增对比文件",
          activeMenu: "/debtConversion/batchInvoicingApplication",
          noCache: true,
        },
      },
      {
        path: "batchInvoicingApplication/fileDetail/:id",
        component: () =>
          import(
            "@/views/debtConversion/batchInvoicingApplication/components/fileDetail"
          ),
        name: "FileDetailBatch",
        meta: {
          title: "",
          activeMenu: "/debtConversion/batchInvoicingApplication",
          noCache: true,
        },
      },
      {
        path: "batchInvoicingApplication/application/:id",
        component: () =>
          import(
            "@/views/debtConversion/batchInvoicingApplication/components/application"
          ),
        name: "Application",
        meta: {
          title: "",
          activeMenu: "/debtConversion/batchInvoicingApplication",
          noCache: true,
        },
      },
      {
        path: "businessInvoicingApplication/invoiceDetail/:id",
        component: () =>
          import(
            "@/views/debtConversion/businessInvoicingApplication/components/invoiceDetail"
          ),
        name: "InvoiceDetail",
        meta: {
          title: "",
          activeMenu: "/debtConversion/businessInvoicingApplication",
          noCache: true,
        },
      },
      {
        path: "businessInvoicingApplication/fileImportAdd",
        component: () =>
          import(
            "@/views/debtConversion/businessInvoicingApplication/components/addFile"
          ),
        name: "BusinessInvoicingApplicationFileImportAdd",
        meta: {
          title: "导入开票信息",
          activeMenu: "/debtConversion/businessInvoicingApplication",
          noCache: true,
        },
      },
      {
        path: "financialInvoicingOrder/fileImportAdd",
        component: () =>
          import(
            "@/views/debtConversion/financialInvoicingOrder/components/addFile"
          ),
        name: "financialInvoicingOrderFileImportAdd",
        meta: {
          title: "批量上传发票附件",
          activeMenu: "/debtConversion/financialInvoicingOrder",
          noCache: true,
        },
      },
      {
        path: "financialInvoicingOrder/invoiceAttachmentDetail/:id",
        component: () =>
          import(
            "@/views/debtConversion/financialInvoicingOrder/components/invoiceDetail"
          ),
        name: "InvoiceAttachmentDetail",
        meta: {
          title: "",
          activeMenu: "/debtConversion/financialInvoicingOrder",
          noCache: true,
        },
      },
    ],
  },
];
