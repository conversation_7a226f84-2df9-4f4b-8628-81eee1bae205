import Layout from "@/layout";

export default [
  {
    path: "/badSystemOther",
    name: "BadSystemOther",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "assetManagementDetail/:id",
        component: () => import("@/views/badSystem/assetManagement/assetManagementDetail/index"),
        name: "AssetManagementDetail",
        meta: {
          title: "",
          activeMenu: "/badSystem/assetManagement",
          noCache: true,
        },
      },
      {
        path: "organizationalManagementDetail/:id",
        component: () => import("@/views/badSystem/organizationalManagement/Detail/index"),
        name: "OrganizationalManagementDetail",
        meta: {
          title: "",
          activeMenu: "/badSystem/organizationalManagement",
          noCache: true,
        },
      },
      {
        path: "organizationalManagementDetailView/:id",
        component: () => import("@/views/badSystem/organizationalManagement/View/index"),
        name: "OrganizationalManagementDetailView",
        meta: {
          title: "",
          activeMenu: "/badSystem/organizationalManagement",
          noCache: true,
        },
      },
      {
        path: "outsourcedDetail/:id",
        component: () => import("@/views/badSystem/outsourced/Detail/index"),
        name: "OutsourcedDetail",
        meta: {
          title: "",
          activeMenu: "/badSystem/outsourced",
          noCache: true,
        },
      },
      {
        path: "outsourcedDetailView/:id",
        component: () => import("@/views/badSystem/outsourced/View/index"),
        name: "OutsourcedDetailView",
        meta: {
          title: "",
          activeMenu: "/badSystem/outsourced",
          noCache: true,
        },
      },
      {
        path: "channelBusinessDetail/:id",
        component: () => import("@/views/badSystem/channelBusiness/Detail/index"),
        name: "ChannelBusinessDetail",
        meta: {
          title: "",
          activeMenu: "/badSystem/channelBusiness",
          noCache: true,
        },
      },
      {
        path: "channelBusinessDetailView/:id",
        component: () => import("@/views/badSystem/channelBusiness/View/index"),
        name: "ChannelBusinessDetailView",
        meta: {
          title: "",
          activeMenu: "/badSystem/channelBusiness",
          noCache: true,
        },
      },
      {
        path: "financialSettlementDetail/:id",
        component: () => import("@/views/badSystem/financialSettlement/Detail/index"),
        name: "FinancialSettlementDetail",
        meta: {
          title: "",
          activeMenu: "/badSystem/financialSettlementDetail",
          noCache: true,
        },
      },
      {
        path: "financialSettlementDetailView/:id",
        component: () => import("@/views/badSystem/financialSettlement/View/index"),
        name: "FinancialSettlementDetailView",
        meta: {
          title: "",
          activeMenu: "/badSystem/financialSettlementDetail",
          noCache: true,
        },
      },
     
    ],
  },
];
