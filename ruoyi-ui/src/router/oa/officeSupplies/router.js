import Layout from "@/layout";

export default [
    {
        path: "/officeSuppliesOther",
        name: "OfficeSuppliesOther",
        component: Layout,
        hidden: true,
        children: [
            {
                path: "addMaintenance",
                component: () => import("@/views/officeSupplies/maintenance/addMaintenance"),
                name: "AddMaintenance",
                meta: { title: '新增物品', activeMenu: "/officeSupplies/maintenance", noCache: true },
            },
            {
                path: "maintenanceDetail",
                component: () => import("@/views/officeSupplies/maintenance/maintenanceDetail"),
                name: "MaintenanceDetail",
                meta: { title: '物品详情', activeMenu: "/officeSupplies/maintenance", noCache: true },
            },
            {
                path: "suppliesRequisition",
                component: () => import("@/views/officeSupplies/officeUse/suppliesRequisition"),
                name: "SuppliesRequisition",
                meta: { title: '办公用品领用申请', activeMenu: "/officeSupplies/officeUse", noCache: true },
            },

        ],
    },
];
