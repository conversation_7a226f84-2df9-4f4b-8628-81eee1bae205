import Layout from "@/layout";

export default [
  {
    path: "/businessSystem",
    name: "BusinessSystem",
    component: Layout,
    hidden: true,
    children: [

      {
        path: "addNewProject",
        component: () => import("@/views/businessSystem/newProject/addNewProject"),
        name: "AddNewProject",
        meta: { title: '添加新项目', activeMenu: "/businessSystem/newProject", noCache: false },
      },
      {
        path: "newProjectDetail",
        component: () => import("@/views/businessSystem/newProject/newProjectDetail"),
        name: "NewProjectDetail",
        meta: { title: '查看详情', activeMenu: "/businessSystem/newProject", noCache: true },
      },
      {
        path: "controlRulesDetail",
        component: () => import("@/views/businessSystem/controlRules/controlRulesDetail"),
        name: "ControlRulesDetail",
        meta: { title: '查看详情', activeMenu: "/businessSystem/controlRulesDetail", noCache: true },
      },
      {
        path: "addControlRules",
        component: () => import("@/views/businessSystem/controlRules/addControlRules"),
        name: "AddControlRules",
        meta: { title: '添加管控规则', activeMenu: "/businessSystem/addControlRules", noCache: false },
      },
      {
        path: "editControlRules",
        component: () => import("@/views/businessSystem/controlRules/editControlRules"),
        name: "EditControlRules",
        meta: { title: '编辑管控规则', activeMenu: "/businessSystem/editControlRules", noCache: true },
      },

    ],
  },
];