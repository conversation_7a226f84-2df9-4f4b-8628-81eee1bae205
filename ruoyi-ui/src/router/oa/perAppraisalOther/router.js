import Layout from "@/layout";

export default [
  {
    path: "/perAppraisalOther",
    name: "PerAppraisalOther",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "annualPlan/:id",
        component: () => import("@/views/perAppraisal/annualPlan/annualPlanDetail/index"),
        name: "AnnualPlan",
        meta: {
          title: "",
          activeMenu: "/perAppraisal/annualPlan",
          noCache: true,
        },
      },
      {
        path: "annualPlanReview",
        component: () => import("@/views/perAppraisal/annualPlanReview/index"),
        name: "AnnualPlanReview",
        meta: {
          title: "提交年度计划审核",
          activeMenu: "/perAppraisal/annualPlan",
          noCache: true,
        },
      },
      {
        path: "projectPerformance/:id",
        component: () => import("@/views/perAppraisal/projectPerformance/projectPerformanceDetail/index"),
        name: "ProjectPerformance",
        meta: {
          title: "",
          activeMenu: "/perAppraisal/projectPerformance",
          noCache: true,
        },
      },
      {
        path: "projectPerformanceReview",
        component: () => import("@/views/perAppraisal/projectPerformanceReview/index"),
        name: "ProjectPerformanceReview",
        meta: {
          title: "提交项目业绩审核",
          activeMenu: "/perAppraisal/projectPerformance",
          noCache: true,
        },
      },
      {
        path: "assessmentConfiguration/:id",
        component: () => import("@/views/perAppraisal/assessmentConfiguration/assessmentConfigurationDetail/index"),
        name: "AssessmentConfigurationDetail",
        meta: {
          title: "",
          activeMenu: "/perAppraisal/assessmentConfiguration",
          noCache: true,
        },
      },
      {
        path: "assessmentConfigurationReview",
        component: () => import("@/views/perAppraisal/assessmentConfigurationReview/index"),
        name: "AssessmentConfigurationReview",
        meta: {
          title: "提交考核配置审核",
          activeMenu: "/perAppraisal/assessmentConfiguration",
          noCache: true,
        },
      },
      {
        path: "templateList",
        component: () => import("@/views/perAppraisal/templateList/index"),
        name: "TemplateList",
        meta: {
          title: "配置模板",
          activeMenu: "/perAppraisal/assessmentConfiguration",
          noCache: true,
        },
      },
      {
        path: "templateList/:id",
        component: () => import("@/views/perAppraisal/templateList/templateListDetail/index"),
        name: "TemplateListDetail",
        meta: {
          title: "",
          activeMenu: "/perAppraisal/assessmentConfiguration",
          noCache: true,
        },
      },
      {
        path: "assessmentResults/:id",
        component: () => import("@/views/perAppraisal/assessmentResults/assessmentResultsDetail/index"),
        name: "AssessmentResultsDetail",
        meta: {
          title: "",
          activeMenu: "/perAppraisal/assessmentResults",
          noCache: true,
        },
      },
      {
        path: "assessmentResultsConfirmation",
        component: () => import("@/views/perAppraisal/assessmentResultsConfirmation/index"),
        name: "AssessmentResultsConfirmation",
        meta: {
          title: "考核结果确认",
          activeMenu: "/perAppraisal/assessmentResults",
          noCache: true,
        },
      },
      {
        path: "assessmentResultsConfirmation/:id",
        component: () => import("@/views/perAppraisal/assessmentResultsConfirmation/assessmentResultsConfirmationDetail/index"),
        name: "AssessmentResultsConfirmationDetail",
        meta: {
          title: "查看考核详情",
          activeMenu: "/perAppraisal/assessmentResults",
          noCache: true,
        },
      },
    ],
  },
];
