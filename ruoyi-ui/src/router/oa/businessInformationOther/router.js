import Layout from "@/layout";

export default [
  {
    path: "/businessInformationOther",
    name: "BusinessInformationOther",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "dutyUser",
        component: () => import("@/views/businessInformation/dutyUser/index"),
        name: "DutyUser",
        meta: { title: '责任人管理', activeMenu: "/businessInformation/projectDeploy", noCache: true },
      },
      {
        path: "companyType",
        component: () => import("@/views/businessInformation/companyType/index"),
        name: "CompanyType",
        meta: { title: '管理公司类型', activeMenu: "/businessInformation/companyInformation", noCache: true },
      },
      {
        path: "businessType",
        component: () => import("@/views/businessInformation/businessType/index"),
        name: "BusinessType",
        meta: { title: '管理支持类型', activeMenu: "/businessInformation/companyInformation", noCache: true },
      },
      {
        path: "addTemplate",
        component: () => import("@/views/businessInformation/authTemplate/addTemplate"),
        name: "AddTemplate",
        meta: { title: '新增模板', activeMenu: "/businessInformation/authTemplate", noCache: true },
      },
      {
        path: "authTemplate",
        component: () => import("@/views/businessInformation/authTemplate/index"),
        name: "AddTemplate",
        meta: { title: '授权模板管理', activeMenu: "/businessInformation/authTemplate", noCache: true },
      },
      {
        path: "editCollecPay",
        component: () => import("@/views/businessInformation/projectDeploy/components/addEdit/components/components/EditCollecPay"),
        name: "EditCollecPay",
        meta: { title: '编辑项目收付款信息', activeMenu: "/businessInformation/projectDeploy" },
      },
      {
        path: "editInformation",
        component: () => import("@/views/businessInformation/projectDeploy/components/addEdit/components/components/EditInformation"),
        name: "EditInformation",
        meta: { title: '编辑项目信息费信息', activeMenu: "/businessInformation/projectDeploy" },
      },
    ],
  },
];
