import Layout from "@/layout";

export default [
  // {
  //   path: "/financialInquiryOther",
  //   name: "FinancialInquiryOther",
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: "projectRevenue",
  //       component: () => import("@/views/financialInquiry/president/projectRevenue/index"),
  //       name: "ProjectRevenue",
  //       meta: { title: '项目收入', activeMenu: "/financialInquiry/president", },
  //     },
  //     {
  //       path: "netProfit",
  //       component: () => import("@/views/financialInquiry/president/netProfit/index"),
  //       name: "NetProfit",
  //       meta: { title: '公司净利润', activeMenu: "/financialInquiry/president", },
  //     },
  //     {
  //       path: "dividends",
  //       component: () => import("@/views/financialInquiry/president/dividends/index"),
  //       name: "Dividends",
  //       meta: { title: '待分配红利', activeMenu: "/financialInquiry/president", },
  //     },
  //     {
  //       path: "margin",
  //       component: () => import("@/views/financialInquiry/operations/margin/index"),
  //       name: "Margin",
  //       meta: { title: '平台方保证金', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "fundingMargin",
  //       component: () => import("@/views/financialInquiry/operations/fundingMargin/index"),
  //       name: "FundingMargin",
  //       meta: { title: '资金方保证金', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "compensationPayment",
  //       component: () => import("@/views/financialInquiry/operations/compensationPayment/index"),
  //       name: "CompensationPayment",
  //       meta: { title: '代偿款', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "channelFee",
  //       component: () => import("@/views/financialInquiry/operations/channelFee/index"),
  //       name: "ChannelFee",
  //       meta: { title: '项目收入', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "platformTechnology",
  //       component: () => import("@/views/financialInquiry/operations/platformTechnology/index"),
  //       name: "PlatformTechnology",
  //       meta: { title: '平台技术服务费', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "thirdPartyTechnology",
  //       component: () => import("@/views/financialInquiry/operations/thirdPartyTechnology/index"),
  //       name: "ThirdPartyTechnology",
  //       meta: { title: '第三方技术服务费', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "offlineRepayment",
  //       component: () => import("@/views/financialInquiry/operations/offlineRepayment/index"),
  //       name: "OfflineRepayment",
  //       meta: { title: '线下还款', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "projectInformationFees",
  //       component: () => import("@/views/financialInquiry/operations/projectInformationFees/index"),
  //       name: "ProjectInformationFees",
  //       meta: { title: '应付项目信息费明细', activeMenu: "/financialInquiry/operations", },
  //     },
  //     {
  //       path: "compensationAmount",
  //       component: () => import("@/views/financialInquiry/risk/compensationAmount/index"),
  //       name: "CompensationAmount",
  //       meta: { title: '通道业务代偿金额', activeMenu: "/financialInquiry/risk", },
  //     },
  //     {
  //       path: "notCompensationAmount",
  //       component: () => import("@/views/financialInquiry/risk/notCompensationAmount/index"),
  //       name: "NotCompensationAmount",
  //       meta: { title: '非通道业务代偿金额', activeMenu: "/financialInquiry/risk", },
  //     },
  //     {
  //       path: "grossProfit",
  //       component: () => import("@/views/financialInquiry/risk/grossProfit/index"),
  //       name: "GrossProfit",
  //       meta: { title: '各项目毛利率', activeMenu: "/financialInquiry/risk", },
  //     },
  //     {
  //       path: "financialStatistics",
  //       component: () => import("@/views/financialInquiry/finance/financialStatistics/index"),
  //       name: "FinancialStatistics",
  //       meta: { title: '资金收支统计', activeMenu: "/financialInquiry/finance", },
  //     },
  //     {
  //       path: "costStatistics",
  //       component: () => import("@/views/financialInquiry/finance/costStatistics/index"),
  //       name: "CostStatistics",
  //       meta: { title: '费用统计', activeMenu: "/financialInquiry/finance", },
  //     },
  //     {
  //       path: "costProportion",
  //       component: () => import("@/views/financialInquiry/finance/costProportion/index"),
  //       name: "CostProportion",
  //       meta: { title: '费用占比', activeMenu: "/financialInquiry/finance", },
  //     },
  //     {
  //       path: "indicatorStatistics",
  //       component: () => import("@/views/financialInquiry/finance/indicatorStatistics/index"),
  //       name: "IndicatorStatistics",
  //       meta: { title: '财务指标统计', activeMenu: "/financialInquiry/finance", },
  //     },
  //     {
  //       path: "compensationRateMonth",
  //       component: () => import("@/views/financialInquiry/bigData/compensationRateMonth/index"),
  //       name: "CompensationRateMonth",
  //       meta: { title: '代偿金额与代偿率（按月）', activeMenu: "/financialInquiry/bigData", },
  //     },
  //     {
  //       path: "compensationCollectionProject",
  //       component: () => import("@/views/financialInquiry/bigData/compensationCollectionProject/index"),
  //       name: "CompensationCollectionProject",
  //       meta: { title: '代偿/催回/坏账（按项目)', activeMenu: "/financialInquiry/bigData", },
  //     },
  //     {
  //       path: "grossProfitMargin",
  //       component: () => import("@/views/financialInquiry/bigData/grossProfitMargin/index"),
  //       name: "GrossProfitMargin",
  //       meta: { title: '各项目毛利率', activeMenu: "/financialInquiry/bigData", },
  //     },
  //   ],
  // },
];
