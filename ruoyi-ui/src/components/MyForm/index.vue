<template>
  <div>
    <el-form
      ref="form"
      :model="innerValue"
      size="small"
      :label-position="labelPosition"
      :label-width="labelWidth"
      :inline="true"
      v-bind="$attrs"
      v-on="$listeners"
      @submit.native.prevent
    >
      <!-- 样式依次排列 -->
      <template v-if="formType == 'search'">
        <template v-for="column in columns">
          <slot v-if="column.type == 'slot'" :name="column.slotName" />
          <el-form-item
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :class="column.class"
          >
            <template v-if="column.type == 'input'">
              <el-input
                class="inputs"
                v-model.trim="innerValue[column.prop]"
                :disabled="column.disabled"
                :clearable="
                  column.clearable !== undefined ? column.clearable : true
                "
                size="small"
                :style="column.style || { width: '240px' }"
                :placeholder="column.placeholder"
                @keyup.enter.native="getSearchList(column.prop)"
                @clear="getSearchList(column.prop)"
              >
                <template v-if="column.appendText" slot="append">{{
                  column.appendText
                }}</template>
              </el-input>
            </template>
            <template v-if="column.type == 'select'">
              <el-select
                v-model="innerValue[column.prop]"
                :disabled="column.disabled"
                :clearable="
                  column.clearable !== undefined ? column.clearable : true
                "
                size="small"
                :style="column.style || { width: '240px' }"
                :filterable="column.filterable"
                :multiple="column.multiple"
                :placeholder="column.placeholder"
                @change="getSearchList(column.prop)"
                @clear="getSearchList(column.prop)"
              >
                <template v-if="column.key">
                  <el-option
                    v-for="item in $store.state.data.OPTION_MAP[column.key]"
                    :key="item.value"
                    :label="
                      column.dataProp ? item[column.dataProp.label] : item.label
                    "
                    :value="
                      column.dataProp ? item[column.dataProp.value] : item.value
                    "
                  />
                </template>
                <template v-if="column.options && !column.dataProp">
                  <el-option
                    v-for="item in column.options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </template>
                <template v-if="column.options && column.dataProp">
                  <el-option
                    v-for="item in column.options"
                    :key="item[column.dataProp.value]"
                    :label="item[column.dataProp.label]"
                    :value="item[column.dataProp.value]"
                  />
                </template>
              </el-select>
            </template>
            <template v-if="column.type == 'treeselect'">
              <treeselect
                v-model="innerValue[column.prop]"
                :style="column.style || { width: '240px' }"
                :options="column.options"
                :disabled="column.disabled"
                :normalizer="normalizer"
                noOptionsText="暂无数据"
                :show-count="true"
                :placeholder="column.placeholder"
              />
            </template>
            <template v-if="column.type == 'datePicker'">
              <el-date-picker
                v-model="innerValue[column.prop]"
                :disabled="column.disabled"
                :type="column.dateType"
                size="small"
                :style="column.style || { width: '240px' }"
                :value-format="column.valueFormat || 'yyyy-MM-dd'"
                :placeholder="column.placeholder"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                range-separator="-"
                :picker-options="
                  column.dateType == 'date'
                    ? column.pickerOptions || pickerOptions
                    : null
                "
                @change="getSearchList(column.prop)"
              />
            </template>
          </el-form-item>
        </template>
        <el-form-item class="ml-4">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            :disabled="loading"
            @click="getSearchList"
            >搜索</el-button
          >
          <el-button
            type="default"
            icon="el-icon-refresh"
            size="mini"
            @click="reset"
            >重 置</el-button
          >
        </el-form-item>
      </template>
      <!--  样式span控制 -->
      <template v-else>
        <el-row type="flex" justify="space-between" class="flex-wrap">
          <el-col
            v-for="(column, index) in columns"
            :key="index"
            :span="column.span || 11"
          >
            <slot v-if="column.type == 'slot'" :name="column.slotName" />
            <el-form-item
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :class="column.class"
            >
              <template v-if="column.type == 'divText'">
                <div>{{ innerValue[column.prop] }}</div>
              </template>
              <template v-if="column.type == 'input'">
                <el-input
                  class="inputs"
                  v-model.trim="innerValue[column.prop]"
                  :disabled="column.disabled"
                  :clearable="
                    column.clearable !== undefined ? column.clearable : true
                  "
                  size="small"
                  :style="column.style || { width: '240px' }"
                  :placeholder="column.placeholder"
                  :maxlength="column.maxlength || 80"
                  :show-word-limit="column.maxlength ? true : false"
                >
                  <template v-if="column.appendText" slot="append">{{
                    column.appendText
                  }}</template>
                </el-input>
              </template>
              <template v-if="column.type == 'inputNumbers'">
                <el-input
                  class="inputs"
                  v-model.trim="innerValue[column.prop]"
                  :disabled="column.disabled"
                  :clearable="
                    column.clearable !== undefined ? column.clearable : true
                  "
                  @input="
                    innerValue[column.prop] = innerValue[column.prop].replace(
                      /[^\d.]/g,
                      ''
                    )
                  "
                  size="small"
                  :style="column.style || { width: '240px' }"
                  :placeholder="column.placeholder"
                  :maxlength="column.maxlength || 80"
                  :show-word-limit="column.maxlength ? true : false"
                >
                  <template v-if="column.appendText" slot="append">{{
                    column.appendText
                  }}</template>
                </el-input>
              </template>
              <template v-if="column.type == 'textarea'">
                <el-input
                  class="inputs"
                  v-model="innerValue[column.prop]"
                  :disabled="column.disabled"
                  type="textarea"
                  :clearable="
                    column.clearable !== undefined ? column.clearable : true
                  "
                  size="small"
                  :rows="column.rows"
                  :style="column.style || { width: '240px' }"
                  :placeholder="column.placeholder"
                  :maxlength="column.maxlength || 255"
                  :show-word-limit="column.maxlength ? true : false"
                />
              </template>
              <template v-if="column.type == 'select'">
                <el-select
                  v-model="innerValue[column.prop]"
                  :disabled="column.disabled"
                  :clearable="
                    column.clearable !== undefined ? column.clearable : true
                  "
                  size="small"
                  :style="column.style || { width: '240px' }"
                  :filterable="column.filterable"
                  :multiple="column.multiple"
                  :placeholder="column.placeholder"
                >
                  <template v-if="column.key">
                    <el-option
                      v-for="item in $store.state.data.OPTION_MAP[column.key]"
                      :key="item.value"
                      :label="
                        column.dataProp
                          ? item[column.dataProp.label]
                          : item.label
                      "
                      :value="
                        column.dataProp
                          ? item[column.dataProp.value]
                          : item.value
                      "
                    />
                  </template>
                  <template v-if="column.options && !column.dataProp">
                    <el-option
                      v-for="item in column.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </template>
                  <template v-if="column.options && column.dataProp">
                    <el-option
                      v-for="item in column.options"
                      :key="item[column.dataProp.value]"
                      :label="item[column.dataProp.label]"
                      :value="item[column.dataProp.value]"
                    />
                  </template>
                </el-select>
              </template>
              <template v-if="column.type == 'treeselect'">
                <treeselect
                  v-model="innerValue[column.prop]"
                  :style="column.style || { width: '240px' }"
                  :options="column.options"
                  :disabled="column.disabled"
                  :normalizer="normalizer"
                  noOptionsText="暂无数据"
                  :show-count="true"
                  :placeholder="column.placeholder"
                />
              </template>
              <template v-if="column.type == 'switch'">
                <el-switch
                  v-model="innerValue[column.prop]"
                  active-color="'#13ce66'"
                  inactive-color="'#ff4949'"
                  :active-text="column.activeText || '是'"
                  :inactive-text="column.inactiveText"
                  :active-value="column.activeValue"
                  :inactive-value="column.inactiveValue"
                >
                </el-switch>
              </template>
              <template v-if="column.type == 'radio'">
                <el-radio-group
                  v-model="innerValue[column.prop]"
                  :placeholder="column.placeholder"
                  class="w-full"
                  :disabled="column.disabled"
                >
                  <template v-if="column.key">
                    <el-radio
                      v-for="item in $store.state.data.OPTION_MAP[column.key]"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-radio>
                  </template>
                  <template v-if="column.options && !column.dataProp">
                    <el-radio
                      v-for="item in column.options"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-radio>
                  </template>
                  <template v-if="column.options && column.dataProp">
                    <el-radio
                      v-for="item in column.options"
                      :key="item[column.dataProp.value]"
                      :label="item[column.dataProp.value]"
                    >
                      {{ item[column.dataProp.label] }}
                    </el-radio>
                  </template>
                </el-radio-group>
              </template>
              <template v-if="column.type == 'checkbox'">
                <el-checkbox-group
                  v-model="innerValue[column.prop]"
                  :placeholder="column.placeholder"
                  class="w-full"
                  :disabled="column.disabled"
                >
                  <template v-if="column.key">
                    <el-checkbox
                      v-for="item in $store.state.data.OPTION_MAP[column.key]"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-checkbox>
                  </template>
                  <template v-if="column.options && !column.dataProp">
                    <el-checkbox
                      v-for="item in column.options"
                      :key="item.value"
                      :label="item.value"
                    >
                      {{ item.label }}
                    </el-checkbox>
                  </template>
                  <template v-if="column.options && column.dataProp">
                    <el-checkbox
                      v-for="item in column.options"
                      :key="item[column.dataProp.value]"
                      :label="item[column.dataProp.value]"
                    >
                      {{ item[column.dataProp.label] }}
                    </el-checkbox>
                  </template>
                </el-checkbox-group>
              </template>
              <template v-if="column.type == 'datePicker'">
                <el-date-picker
                  v-model="innerValue[column.prop]"
                  :disabled="column.disabled"
                  :type="column.dateType"
                  size="small"
                  :style="column.style || { width: '240px' }"
                  :value-format="column.valueFormat || 'yyyy-MM-dd'"
                  :format="column.format"
                  :placeholder="column.placeholder"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="
                    column.dateType == 'date' ||
                    column.dateType == 'datetimerange'
                      ? column.pickerOptions || pickerOptions
                      : null
                  "
                  range-separator="-"
                  @focus="onFocus"
                />
              </template>
              <template v-if="column.type == 'inputNumber'">
                <el-input-number
                  class="inputs"
                  v-model.trim="innerValue[column.prop]"
                  :disabled="column.disabled"
                  :clearable="
                    column.clearable !== undefined ? column.clearable : true
                  "
                  size="small"
                  :controls-position="column.controlsPosition"
                  :style="column.style || { width: '240px' }"
                  :placeholder="column.placeholder"
                  @keyup.enter.native="getSearchList(column.prop)"
                  @clear="getSearchList(column.prop)"
                >
                </el-input-number>
              </template>
              <template v-if="column.type == 'upload'">
                <el-upload
                  :disabled="column.disabled"
                  :headers="upload.headers"
                  :action="upload.url + column.url"
                  :on-success="handleFileSuccess"
                  :on-remove="handleRemove"
                  :before-remove="beforeRemove"
                  :multiple="column.multiple"
                  :limit="column.limit"
                  :on-exceed="handleExceed"
                  :file-list="fileList"
                  :on-preview="openFile"
                  :accept="column.accept"
                >
                  <el-button><i class="el-icon-upload2"></i>上传附件</el-button>
                </el-upload>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import vModelMixin from "@/mixin/v-model";
import privew from "@/mixin/privew";
import { getToken } from "@/utils/auth";
export default {
  name: "MyForm",
  components: { Treeselect },
  mixins: [vModelMixin, privew],
  props: {
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    formType: {
      type: String,
      required: false,
      default: "search",
    },
    labelWidth: {
      type: String,
      required: false,
      default: "85px",
    },
    labelPosition: {
      type: String,
      required: false,
      default: "right",
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
    customizeRemove: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      datetimeRangePickerAll: [],
      upload: Object.freeze({
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API,
      }),
      fileList: [],
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.id,
          label: node.label,
          children: node.children,
        };
      },
      pickerOptions: Object.freeze({
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      }),
    };
  },
  watch: {
    "innerValue.files": {
      handler(val) {
        if (val && val.length) {
          this.fileList = val.map((item) => {
            return {
              name: item.fileName,
              id: item.id,
              url:
                process.env.VUE_APP_BASE_API + (item.fileUrl || item.filePath),
              downLoadUrl: item.fileUrl || item.filePath,
            };
          });
        } else {
          this.fileList = [];
        }
        this.getFileListIds(this.fileList);
      },
      immediate: true,
    },
  },
  mounted() {
    const inp = this.$refs.form;
    this.validate = inp.validate;
    this.resetFields = inp.resetFields;
    this.clearValidate = inp.clearValidate;
  },
  methods: {
    onFocus() {
      this.$nextTick(() => {
        const datetimeRangePickerAll = document.querySelectorAll(
          ".el-time-spinner__wrapper .el-scrollbar__wrap"
        );
        this.datetimeRangePickerAll = datetimeRangePickerAll;
        if (datetimeRangePickerAll && datetimeRangePickerAll.length) {
          // 监听 wheel 事件
          datetimeRangePickerAll.forEach((item) => {
            item.addEventListener("wheel", (event) => {
              this.handleWheelEvent(event, item);
            });
          });
        }
      });
    },
    handleWheelEvent(event, item) {
      event.preventDefault(); // 阻止默认滚动行为
      // 设置滚动速度系数
      const scrollFactor = 0.1;
      // 判断滚动方向
      let Y = event.deltaY * scrollFactor;
      requestAnimationFrame(() => {
        item.scrollBy(0, Y);
      });
    },
    getSearchList(value) {
      this.$emit("onSearchList", value);
    },
    reset() {
      this.$refs["form"].resetFields();
      this.$emit("onSearchList");
    },

    handleFileSuccess(response, file, fileList) {
      this.fileList = fileList;
      this.getFileListIds(fileList);
    },
    handleRemove(file, fileList) {
      this.getFileListIds(fileList);
    },
    beforeRemove(file, fileList) {
      if (this.customizeRemove) {
        // 触发 beforeRemove 事件，让父组件处理确认对话框
        this.$emit("beforeRemove", file, fileList);
        // 阻止默认删除行为
        return false;
      }
    },
    // 新增方法：实际执行文件删除
    confirmRemove(file, fileList) {
      this.fileList = fileList;
      this.getFileListIds(fileList);
    },
    handleExceed(files, fileList) {
      this.$message.error(`最多只能上传${fileList.length}个文件`);
    },
    getFileListIds(value) {
      this.fileListIds = value.map((item) => item.id || item.response.data.id);
      this.$set(
        this.innerValue,
        "fileIds",
        this.fileListIds.length ? this.fileListIds : null
      );
      this.$set(this.innerValue, "filesWeb", this.fileList);
      this.$set(this.innerValue, "fileListAll", value);
      this.$refs.form?.validateField("fileIds");
    },
    openFile(value) {
      this.handlePreview(value);
    },
  },
  beforeDestroy() {
    // 在组件销毁时移除事件监听器
    if (this.datetimeRangePickerAll.length > 0) {
      this.datetimeRangePickerAll.forEach((item) => {
        item.removeEventListener("wheel", (event) => {
          this.handleWheelEvent(event, item);
        });
      });
    }
  },
};
</script>
<style lang="less" scoped>
.inputs {
  ::v-deep .el-input__inner {
    padding-right: 60px;
  }
}
</style>