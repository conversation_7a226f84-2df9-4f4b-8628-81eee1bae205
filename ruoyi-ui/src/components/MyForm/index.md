### 基本使用

```javascript
    <MyForm v-model="form" :columns="formColumns" formType='12'/>
```

```javascript
formColumns:[
        {
          label: "员工姓名",
          prop: "name",
          type: "input",
          placeholder: "请输入操作人",
        },
        {
          label: "备注",
          prop: "remark",
          type: "textarea",
          placeholder: "请输入备注",
          maxlength:100,
          rows:3
        },
        {
          label: "入职职级",
          prop: "onboardingRank",
          type: "select",
          placeholder: "请选择入职职级",
          key:'entry_rank'
        },
        {
          label: "门禁卡申领",
          prop: "accessCard",
          type: "select",
          placeholder: "请选择门禁卡申领",
          options:[{value:1,label:'测试1'}],
          dataProp:{value:'value',label:'label'},//非必写  需转化value label键名时使用
        },
        {
          label: "转正日期",
          prop: "formalTime",
          type: "datePicker",
        },
        {
          label: "单选",
          prop: "radios",
          type: "radio",
          key:'entry_rank'
        },
        {
          label: "树选择",
          prop: "ceshi",
          type: "treeselect",
          options:[],//数据源
        },
        
        {
          label: "上传",
          prop: "fileList",
          type: "upload",
          span:24
        },
      ],
```
### 传值

```javascript
//必传
form:{},//表单绑定的值
formColumns:[]
//columns中的可配置项(label:标题、props:绑定字段,key:字典值(下拉框，单选组等选择项的来源)，type:类型,options:下拉框，单选组等选择项的来源(非字典值时使用),dataProp:下拉框，单选组的lable，value对应值，非必写,span:每行分配的宽度默认6)


//非必传
formType:false,//是否用于搜索 默认用于搜索 传值不为search时为表单

```
