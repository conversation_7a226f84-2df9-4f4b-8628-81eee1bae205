<template>
  <div>
    <MyTable
      :columns="columns"
      :source="configList"
      :row-style="rowStyle"
      :cell-style="cellStyle"
      :header-cell-style="headerEellStyle"
      border
      size="mini"
    />
  </div>
</template>

<script>
export default {
  name: "ComparisonTable",

  props: {
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    configList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.columns.forEach((item) => {
        this.$set(item, "align", "center");
      });
    },
    headerEellStyle() {
      return { background: "rgba(228, 228, 228,.6)", fontWeight: "400" };
    },
    rowStyle({ row, rowIndex }) {
      if (row.diff) {
        return {
          color: "#363636",
        };
      } else {
        return {
          color: "#cccccc",
        };
      }
    },
    cellStyle({ column, columnIndex }) {
      // 设置第一列的字体颜色为黑色
      if (columnIndex === 0) {
        return {
          color: "black",
          fontWeight: "700",
          textAlign:['部门名称'].includes(column.label)? "right":'center',
        };
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table__body-wrapper {
  font-size: 14px;
}
</style>
