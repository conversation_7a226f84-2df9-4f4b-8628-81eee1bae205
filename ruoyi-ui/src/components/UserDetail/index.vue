<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="用户详情"
      :visible.sync="innerValue"
      @open="handleOpen"
      width="550px"
    >
      <div class="flex">
        <div class="w-1/3">
           <el-image
            style="width: 100px; height: 100px; border-radius: 50%"
            :src="user.avatar"
            :preview-src-list="[user.avatar]"
          >
          </el-image>
          <div style="margin-left: 24px;"><el-button type="text" @click="toDetail">查看详情</el-button></div>
        </div>
        <div class="w-2/3">
          <div class="text-xl font-bold">{{ user.nickName }}</div>
          <div class="flex mb-2 mt-5">
            <div class="mr-2">公司: </div>
            <div>{{ user.unit.unitShortName }}</div>
          </div>
          <div class="flex mb-2">
            <div class="mr-2">部门: </div>
            <div>{{ user.dept.deptName }}</div>
          </div>
          <div class="flex mb-2">
            <div class="mr-2">岗位: </div>
            <div>{{ user.post.postName }}</div>
          </div>
          <div class="flex mb-2">
            <div class="mr-2">手机: </div>
            <div>{{ user.phonenumber }}</div>
          </div>
          <div class="flex mb-2">
            <div class="mr-2">邮箱: </div>
            <div>{{ user.email }}</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { mapGetters } from "vuex";
import { profileUserByUserId } from "@/api/system/user";

export default {
  name:"UserDetail",
  mixins: [vModelMixin],
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      user: {
        unit:{},
        dept:{},
        post:{},
      },
    };
  },
  watch: {},
   computed: {
    ...mapGetters(["avatar"]),
  },
  mounted() {},
  methods: {
    toDetail(){
      this.innerValue=false;
      this.$router.push({
        path:'/user/userDetail',
        query:{
          userId:this.user.userId
        }
      })
      this.$emit("close")
    },
    async handleOpen() {
      const {data}=await profileUserByUserId(this.id);
      this.user=data
     
       this.user.avatar = this.user.avatar
        ? process.env.VUE_APP_BASE_API + this.user.avatar
        : this.avatar;
    },
  },
};
</script>
