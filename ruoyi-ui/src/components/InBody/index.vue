<template>
  <div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'InBody',
  mounted() {
    const node = this.$mount().$el
    document.body.appendChild(node)//将里面所有的元素放到body里面
  },
  activated(){
    const node = this.$mount().$el
    document.body.appendChild(node)//将里面所有的元素放到body里面
  },
  destroyed() {
    const node = this.$mount().$el
    node.remove()
  },
  deactivated(){
     const node = this.$mount().$el
     node.remove()
  }
}
</script>

