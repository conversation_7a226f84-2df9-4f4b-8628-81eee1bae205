<template>
  <div>
    <el-dialog
      :title="isProxy ? '添加代理人' : '添加用户'"
      :visible.sync="dialogVisible"
      width="680px"
      :before-close="handleClose"
    >
      <p v-if="isProxy">请选择您的下级用户，作为代理人</p>
      <p v-else>请选择您的下级用户，进行授权，支持多选</p>
      <el-button type="text" @click="changeCheckAll" v-if="!isProxy"
        >全选/全不选</el-button
      >
      <el-divider></el-divider>
      <el-input
        style="width: 250px"
        placeholder="输入关键字进行过滤"
        v-model="filterText"
      >
      </el-input>
      <el-tree
        style="margin-top: 16px"
        class="treeLine"
        :data="userList"
        show-checkbox
        ref="tree"
        check-strictly
        default-expand-all
        check-on-click-node
        node-key="userId"
        :filter-node-method="filterNode"
        :props="defaultProps"
        @check-change="handleCheckedChange"
      >
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button size="mini" type="primary" @click="submit">下一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "AddingUsers",
  props: {
    subordinateList: Object,
    isProxy: Boolean,
  },
  data() {
    return {
      filterText: "",
      selectAuthTimeTpye: false,
      checkTpye: false,
      defaultProps: {
        children: "subordinateList",
        label: "name",
      },
      dialogVisible: true,
      userList: [],
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    if (this.subordinateList) {
      this.userList = [this.subordinateList];
    }
  },
  methods: {
    filterNode(value, data) {
      console.log(value, data);
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheckedChange(data, checked, indeterminate) {
      if (this.isProxy) {
        console.log(123);
        if (checked) {
          this.$refs.tree.setCheckedNodes([data]);
        }
      }
    },
    changeCheckAll() {
      if (!this.checkTpye) {
        let arr = [];
        this.arrForEach(this.userList, arr, "userId");
        console.log(arr);
        this.$refs.tree.setCheckedKeys(arr);
        this.checkTpye = true;
      } else {
        this.$refs.tree.setCheckedKeys([]);
        this.checkTpye = false;
      }
    },
    arrForEach(arr, arr1, str) {
      arr.forEach((item) => {
        if (str) {
          arr1.push(item[str]);
        } else {
          arr1.push(item);
        }
        if (item.subordinateList && item.subordinateList.length) {
          this.arrForEach(item.subordinateList, arr1, str);
        }
      });
    },

    handleClose() {
      this.$emit("close");
    },
    submit() {
      let arr = this.$refs.tree.getCheckedNodes();

      if (this.checkTpye) {
        arr.splice(0, 1);
      }
      if (arr.length == 0) {
        this.$message.warning("请选择");
        return;
      }
      this.$emit("submit", arr);
    },
  },
};
</script>

<style lang="less" scoped>
.el-divider--horizontal {
  margin: 12px 0 !important;
}
::v-deep .treeLine > .el-tree-node > .el-tree-node__content .el-checkbox {
  display: none;
}
</style>