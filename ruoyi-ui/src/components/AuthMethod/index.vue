<template>
  <div>
    <el-dialog
      title="选择授权模板"
      :visible.sync="dialogVisible"
      width="780px"
      :before-close="handleClose"
    >
      <p>
        选择授权模板，按模板设置的模块范围及规则进行授权（按项目、按公司、按所有3个维度有独立的模板）
      </p>
      <el-input
        class="mr-3"
        v-model="queryParams.templateName"
        style="width: 200px"
        placeholder="请输入模板名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary" @click="getList"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
      <MyTable
        class="mt-4"
        :showCheckbox="true"
        @selectList="selectList"
        :selectType="true"
        rowKey="id"
        :queryParams="queryParams"
        :columns="dataColumns"
        :source="dataList"
      >
      </MyTable>
      <pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button size="mini" type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { templateList } from "@/api/businessInformation/authTemplate";
export default {
  name: "AuthMethod",
  props: {
    type: String,
  },
  data() {
    return {
      dialogVisible: true,
      radio: "1",
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        templateName: "",
        total: 0,
      },
      dataList: [],
      dataColumns: [
        {
          label: "模板名称",
          prop: "templateName",
          showOverflowTooltipField: true,
          width: "200",
        },
        {
          label: "模板说明",
          prop: "templateExplain",
          showOverflowTooltipField: true,
        },
      ],
      selectTempList: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    reset() {
      this.queryParams = {
        pageSize: 10,
        pageNum: 1,
        templateName: "",
        total: 0,
      };
      this.getList()
    },
    getList() {
      templateList({
        templateType: this.type,
        ...this.queryParams,
        agencyUserId:this.$store.state.principalId
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          this.queryParams.total = res.total;
        }
      });
    },
    selectList(e) {
      console.log(e);
      this.selectTempList = e;
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      if (this.selectTempList.length == 0) {
        this.$message.warning("请选择");
        return;
      }
      this.$emit("submitMeth", this.selectTempList);
    },
  },
};
</script>

<style>
</style>