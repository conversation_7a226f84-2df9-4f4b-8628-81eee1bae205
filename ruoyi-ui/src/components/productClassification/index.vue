<template>
  <div>
    <div class="flex flex-wrap">
      <div
        v-for="(item, index) in myForm.businessTypeList"
        :key="index"
        class="mr-3 mb-3 border border-solid rounded px-2 flex h-6 items-center relative top-2"
        style="
          border-color: #cccccc;
          background-color: #f2f2f2;
          font-size: 13px;
        "
      >
        <div class="h-6 leading-6">{{ item.typeName }}</div>
        <div  @click="deletType(item)" class="cursor-pointer">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <el-button
        style="height: 30px"
        class="relative top-1"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        @click="add"
        >添加</el-button
      >
    </div>
    <el-dialog
      title="添加产品分类"
      :visible.sync="dialogVisible"
      width="550px"
      @open="open"
      append-to-body
    >
      <el-cascader
        v-model="value"
        :options="options"
        :props="{
          expandTrigger: 'hover',
          children: 'fPiattaformas',
          label: 'dataName',
          value: 'id',
        }"
      ></el-cascader>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getDataManageList } from "@/api/notice/management";
import { setEmptyArrayToUndefined, getNameById } from "@/utils";
import { projectNameRuleGetRule } from "@/api/businessInformation/specialProducts";
export default {
  name: "ProductClassification",
  props: {
    myForm: {
      type: Object,
      required: false,
      default: () => {},
    },
  },
  data() {
    return {
      options: [],
      value: [],
      dialogVisible: false,
    };
  },
 
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.getDataManageList();
    },
    add() {
      this.myForm.businessTypeList?.some((item) => {
        if (item.typeName.indexOf("保函") != -1) {
          this.$alert("保函类项目不允许存在多种产品分类", {
            confirmButtonText: "确定",
          });
        } else if (item.typeName.indexOf("不良出表") != -1) {
          this.$alert("不良出表类项目不允许存在多种产品分类", {
            confirmButtonText: "确定",
          });
        } else if (item.typeName.indexOf("不良出表") != -1) {
          this.$alert("不良出表类项目不允许存在多种产品分类", {
            confirmButtonText: "确定",
          });
        } else {
          this.dialogVisible = true;
        }
      });
      if (!this.myForm.businessTypeList?.length) this.dialogVisible = true;
    },
    async open() {
      
      this.handleOptions();
      this.value = [];
    },
    async getDataManageList() {
      const { rows } = await getDataManageList({
        firstDataCode: "business_type",
      });
      this.options = rows[0].fPiattaformas;
      setEmptyArrayToUndefined(this.options, "fPiattaformas");
      this.handleOptions();
    },
    addDisabled(options) {
      options?.forEach((item) => {
        if (
          this.myForm.businessTypeList
            ?.map((item1) => item1.typeId)
            .includes(item.id)
        ) {
          this.$set(item, "disabled", true);
        } else {
          this.$set(item, "disabled", false);
        }
        if (item.fPiattaformas && item.fPiattaformas.length) {
          this.addDisabled(item.fPiattaformas);
        }
      });
    },
    handleOptions() {
      this.addDisabled(this.options);
    },

    deletType(value) {
      this.myForm.businessTypeList = this.myForm.businessTypeList.filter(
        (item) => item.typeName !== value.typeName
      );
    },
    async submit() {
      if (!this.value || !this.value.length) {
        this.$message.warning("请选择产品分类");
        return;
      }
      const typeName = getNameById(
        this.options,
        this.value[this.value.length - 1]
      );
      const businessTypeList = this.myForm.businessTypeList
        ? this.myForm.businessTypeList.concat([
            { typeName, typeId: this.value[this.value.length - 1] },
          ])
        : [{ typeName, typeId: this.value[this.value.length - 1] }];
      let isPass = true;
      if (businessTypeList && businessTypeList.length > 1) {
        businessTypeList?.some((item) => {
          if (item.typeName.indexOf("保函") != -1) {
            this.$alert("保函类项目不允许存在多种产品分类", {
              confirmButtonText: "确定",
            });
            isPass = false;
            return false;
          } else if (item.typeName.indexOf("不良出表") != -1) {
            this.$alert("不良出表类项目不允许存在多种产品分类", {
              confirmButtonText: "确定",
            });
            isPass = false;
            return false;
          } else if (item.typeName.indexOf("分保业务") != -1) {
            this.$alert("分保业务类项目不允许存在多种产品分类", {
              confirmButtonText: "确定",
            });
            isPass = false;
            return false;
          }
        });
      }
      if (isPass) {
        if (businessTypeList.length > 0 && businessTypeList[0].typeName === "代收类-代收咨询费") {
          if (businessTypeList.length > 1) {
            this.$message.warning("选择代收咨询费时不允许选择其他分类");
            return;
          }
        } else {
          if (businessTypeList.length > 2) {
            this.$message.warning("最多只能添加两项");
            return;
          }
          if (businessTypeList.length === 2 && businessTypeList[1].typeName !== "代收类-代收咨询费") {
            console.log(businessTypeList[1],123)
            this.$message.warning("第二个产品必须是代收咨询费");
            return;
          }
        }

        const { data } = await projectNameRuleGetRule({
          productClassificationId: businessTypeList.map((item) => item.typeId),
        });
        this.$set(this.myForm, "businessTypeList", businessTypeList);
        this.$emit("save-success", data);
        this.dialogVisible = false;
      }
    },
  },
};
</script>


