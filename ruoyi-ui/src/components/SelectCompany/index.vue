<template>
  <el-dialog
    title="选择公司"
    :visible.sync="dialogVisible"
    width="480px"
    append-to-body
    :before-close="handleClose"
  >
    <p style="color: #999;">请选择发起本流程所在的公司</p>
    <el-select
      v-model="companyId"
      placeholder="请选择公司"
      style="width: 400px"
      filterable 
    >
      <el-option
        v-for="item in companyList"
        :key="item.id"
        :label="item.companyName"
        :value="item.id"
      ></el-option>
    </el-select>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
// import { getUnitListEnable } from "@/api/system/unit";
import { newAuthorityCompany } from "@/api/directoryMation/directoryMationSupervise";

export default {
  name:'SelectCompany',
  data() {
    return {
      dialogVisible: true,
      companyList: [],
      companyId: "",
    };
  },
  mounted() {
    newAuthorityCompany({AuthModuleEnumCode: "OALAUNCH",}).then((response) => {
      this.companyList = response.rows;
    });
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    submit() {
      if (!this.companyId) {
        this.$message.warning("请选择公司");
        return;
      }
      this.$emit("submit", this.companyId);
    },
  },
};
</script>

<style>
</style>