<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="编辑记录"
      :visible.sync="innerValue"
      width="700px"
    >
      <div style="max-height: 70vh" class="overflow-y-auto">
        <ContrastDetailNewTable :data="data" />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";

export default {
  name: "ContrastDetailNew",
  mixins: [vModelMixin],
  props: {
    data: {
      type: Object,
      default: () => {},
      required: true,
    },
  },
  data() {
    return {
     
    };
  },
  watch: {},
  mounted() {},
  methods: {
   
  },
};
</script>
