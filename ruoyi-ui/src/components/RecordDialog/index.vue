<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="700px"
    >
      <div style="max-height: 80vh">
        <MyForm
          v-model="queryParams"
          :columns="formColumns"
          @onSearchList="handleQuery"
        />
        <MyTable :columns="columnsRecord" :source="recordList">
          <template #businessType="{ record }">
            <dict-tag
              :options="dict.type.sys_oper_type"
              :value="record.businessType"
            />
          </template>
          <template #operate="{ record }">
            <el-button type="text" @click="openDetail(record)">查看详情</el-button>
          </template>
        </MyTable>
        <pagination
          v-show="total > 20"
          :total="total"
          :page.sync="queryParams.pageNum"
          :page-sizes="[20, 50, 100]"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
      <ContrastDetail v-model="contrastDetail" :data="data"/>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { listLogquery } from "@/api/system/logQuery";

export default {
  name: "RecordDialog",
  dicts: ["sys_oper_type"],
  mixins: [vModelMixin],
  props: {
    title: {
      type: String,
      default: "编辑记录",
    },
    columnsRecord: {
      type: Array,
      default: () => [
        { label: "编辑日期", prop: "operTime" },
        { label: "编辑人员", prop: "nickName" },
        { label: "操作", key: "operate" },
      ],
    },
    formColumns: {
      type: Array,
      default: () => [
        {
          label: "编辑人员",
          prop: "operName",
          type: "input",
          placeholder: "请输入编辑人员",
        },
        {
          label: "编辑日期",
          prop: "operTime",
          type: "datePicker",
          dateType: "daterange",
          placeholder: "请选择编辑日期",
        },
      ],
    },
    params: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      data:null,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operTime: [],
        operName: undefined,
      },
      total: 0,
      recordList: [],
      contrastDetail:false,
      operId:0,
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handelOpen() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        operTime: [],
        operName: undefined,
      };
      this.getList();
    },
    async getList() {
      const operBeginTime=this.queryParams.operTime?`${this.queryParams.operTime[0]} 00:00`:undefined;
      const operEndTime=this.queryParams.operTime?`${this.queryParams.operTime[1]} 24:00`:undefined;
      const params = {
        ...this.queryParams,
        operBeginTime,
        operEndTime,
        relationId: this.params.relationId,
        functionNode: this.params.functionNode,
        title: this.params.title,
      };
      delete params.operTime;
      const { rows, total } = await listLogquery(params);
      this.recordList = rows;
      this.total = total;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    openDetail(record){
      this.data = record
      this.contrastDetail=true;
      this.$emit('openDetail',record)
    }
  },
};
</script>
