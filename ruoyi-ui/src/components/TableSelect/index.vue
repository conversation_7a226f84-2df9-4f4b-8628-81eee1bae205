<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="1050px"
      @open="handlerOpen"
    >
      <div class="pr-5">
        <div class="mb-2 font-medium text-base">
          已选中{{ dataList.length }}条
        </div>
        <MyTable :columns="dataColumns" :source="dataList" :showIndex="true">
          <template #opertion="{ record }">
            <el-button type="text" @click="handleDelete(record)"
              >删除</el-button
            >
          </template>
        </MyTable>
      </div>
      <span slot="footer">
        <div class="flex justify-end">
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import XEUtils from "xe-utils";

export default {
  mixins: [vModelMixin],
  name: "TableSelect",
  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    columns: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
  },
  computed: {
    dataColumns() {
      const arr = this.columns.concat([
        {
          label: "操作",
          key: "opertion",
          width: "100",
        },
      ]);
      return arr;
    },
  },
  data() {
    return {
      dataList: [],
      deleteId: [],
      dataListCancel:[]
    };
  },
  mounted() {},
  methods: {
    handlerOpen() {
      this.dataList = XEUtils.clone(this.tableData, true);
      this.deleteId = [];
      this.dataListCancel = [];
    },
    handleDelete(row) {
      this.dataList = this.dataList.filter((item) => item.id != row.id);
      this.deleteId.push(row.id);
      const tableRow=this.tableData.filter(item=>item.id==row.id);
      this.dataListCancel=this.dataListCancel.concat(tableRow);
    },
    onSubmit() {
      this.innerValue = false;
      this.$emit("on-submit-success", this.deleteId);
      this.$emit("on-submit-success-row", this.dataListCancel);
    },
  },
};
</script>
<style lang="less" scoped>
</style>