<template>
  <el-table
    ref="myTable"
    :stripe="isStripe"
    :class="className"
    :row-class-name="rowClassNameFun"
    :header-row-class-name="headerRowClassName"
    :data="source"
    tooltip-effect="dark"
    :header-cell-class-name="headerCellStyle"
    @selection-change="handleSelectionChange"
    @sort-change="handleSortChange"
    @select="selectFun"
    @select-all="selectAllFun"
    :row-key="rowKey"
    :tree-props="treeProps"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-table-column
      v-if="showCheckbox"
      type="selection"
      :selectable="selectable"
      width="60"
      reserve-selection
      center
    ></el-table-column>
    <el-table-column
      v-if="showIndex"
      type="index"
      label="序号"
      fixed="left"
      width="60"
      :index="handleIndex"
    />
    <el-table-column
      v-for="item in columnsFilter"
      :key="item.prop"
      :prop="item.prop"
      :label="item.label"
      :type="item.type"
      :min-width="item.width||item.minWidth"
      :fixed="item.fixed"
      :align="item.align"
      :show-overflow-tooltip="item.showOverflowTooltipField"
      :sortable="item.sortable"
    >
      <template slot="header" slot-scope="{ column }">
        <slot v-if="item.isHSlot" :name="`h_${item.prop || item.key}`"></slot>
        <span v-else>{{ column.label }}</span>
      </template>
      <template slot-scope="{ row ,$index}">
        <slot v-if="item.key" :name="item.key" :record="row" :index="$index"></slot>
        <span v-else>
          <span v-if="item.showOverflowTooltipField" class="truncate ...">
            {{(row[item.prop] !== null && row[item.prop] !== undefined) ? row[item.prop] : '-' }}
          </span>
          <span v-else>{{(row[item.prop] !== null && row[item.prop] !== undefined) ? row[item.prop] : '-' }}</span>
        </span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import XEUtils from "xe-utils";
export default {
  name: "MyTable",
  props: {
    //true单选false多选
    selectType: {
      type: Boolean,
      required: false,
      default: false,
    },
    source: {
      //数据源
      type: Array,
      required: true,
      default: () => [],
    },
    //已选择
    tableSelectList: {
      type: Array,

      default: () => [],
    },
    columns: {
      //列表项配置项
      type: Array,
      required: true,
      default: () => [],
    },
    showIndex: {
      //是否显示序列
      type: Boolean,
      required: false,
      default: false,
    },
    showSequenceNumber: {
      type: Boolean,
      required: false,
      default: true,
    },
    className: {
      //支持设置个性化样式
      type: String,
      required: false,
      default: "",
    },
    isStripe: {
      type: Boolean,
      required: false,
      default: false,
      required: false,
    },
    selectableType: {
      type: Boolean,
      default: false,
      required: false,
    },
    disabledState: {
      type: Boolean,
      default: false,
      required: false,
    },
    rowKey: {
      type: String,
      required: false,
      default: "id",
    },
    treeProps: {
      type: Object,
      required: false,
      default: () => {},
    },
    checkStrictly: {
      type: Boolean,
      required: false,
      default: false,
    },
    isFlat: {
      type: Boolean,
      required: false,
      default: false,
    },
    queryParams: {
      type: Object,
      required: false,
      default: () => {},
    },
    showCheckbox: {
      type: Boolean,
      required: false,
      default: false,
    },
    allSelectType: {
      type: Boolean,
      required: false,
      default: false,
    },
    isClearSelect: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      selectList: [],
      orderArray: [],
    };
  },
  computed: {
    columnsFilter() {
      return this.columns.filter((item) => item.visible != false);
    },
  },
  mounted() {
    const inp = this.$refs.myTable;
    this.toggleAllSelection = inp.toggleAllSelection;
    this.toggleRowSelection = inp.toggleRowSelection;
    this.clearSelection = inp.clearSelection;
    this.doLayout = inp.doLayout;
    this.$nextTick(() => {
      // setTimeout(() => {
        this.$refs.myTable.doLayout();
      // }, 1000);
    });
    window.addEventListener("resize", () => {
        this.$refs.myTable.doLayout();
    });
  },

  methods: {
    selectable(row, index) {
      if ((row.status == 1 || row.delFlag == 1) && this.selectableType) {
        return false;
      } else if (this.disabledState && (["2", "3"].includes(row.state)||!row.id)) {
        return false;
      } else {
        return true;
      }
    },

    headerCellStyle({ column, columnIndex }) {
      // console.log(column,columnIndex,'112');
      if (columnIndex === 0 && this.selectType) {
        return "hiddenCheck";
      }
       this.orderArray.forEach((element) => {
        if (column.property === element.prop) {
          column.order = element.order;
        }
      });
    },

    handleSelectionChange(e) {
      if (this.selectType) {
        if (e.length > 1) {
          const newRows = e.filter((it, index) => {
            if (index == e.length - 1) {
              this.$refs.myTable.toggleRowSelection(it, true);
              return true;
            } else {
              this.$refs.myTable.toggleRowSelection(it, false);
              return false;
            }
          });
          // 赋值给全局变量保存
          this.selectList = [...newRows];
        } else {
          this.selectList = [...e];
        }
      } else {
        this.selectList = [...e];
      }

      this.$emit("selectList", this.selectList);
    },
    handleIndex(index) {
      if (this.queryParams && this.queryParams.pageNum) {
        return (
          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +
          (index + 1)
        );
      } else {
        return index + 1;
      }
    },
    handleSortChange({ column, prop, order }) {
      if (order) {
        //参与排序
        let flagIsHave = false;
        this.orderArray.forEach((element) => {
          if (element.prop === prop) {
            element.order = order;
            flagIsHave = true;
          }
        });
        if (!flagIsHave) {
          this.orderArray.push({
            prop: prop,
            order: order,
          });
        }
      } else {
        //不参与排序
        this.orderArray = this.orderArray.filter((element) => {
          return element.prop !== prop;
        });
      }
      const orderArray=XEUtils.clone(this.orderArray,true);
      orderArray.forEach(item=>{
        item.order=item.order.slice(0, -6);
      })
      this.$emit('orderChange',orderArray)
    },

    selectFun(selection, row) {
      this.setRowIsSelect(row);
    },
    selectAllFun(selection) {
      if(!this.treeProps){
        return;
      }
      let isAllSelect = this.checkIsAllSelect();

      this.source.forEach((item) => {
        item.isSelect = isAllSelect;
        if (!item.disabled) {
          this.$refs.myTable.toggleRowSelection(item, !isAllSelect);
        }
        this.selectFun(selection, item);
      });
    },
    setRowIsSelect(row) {
      //当点击父级点复选框时，当前的状态可能为未知状态，所以当前行状态设为false并选中，即可实现子级点全选效果
      if (row.isSelect === "") {
        row.isSelect = false;
        this.$refs.myTable.toggleRowSelection(row, true);
      }
      row.isSelect = !row.isSelect;
      let that = this;
      if (!this.checkStrictly || this.isFlat) {
        this.$emit(
          "checkStrictlyChange",
          this.filterAndRemoveNested(this.source)
        );
        return;
      }
      function selectAllChildrens(data) {
        data.forEach((item) => {
          item.isSelect = row.isSelect;
          if (!item.disabled) {
            that.$refs.myTable.toggleRowSelection(item, row.isSelect);
          }
          if (
            item[that.treeProps.children] &&
            item[that.treeProps.children].length
          ) {
            selectAllChildrens(item[that.treeProps.children]);
          }
        });
      }
      function getSelectStatus(selectStatuaArr, data) {
        data.forEach((childrenItem) => {
          selectStatuaArr.push(childrenItem.isSelect);
          if (
            childrenItem[that.treeProps.children] &&
            childrenItem[that.treeProps.children].length
          ) {
            getSelectStatus(
              selectStatuaArr,
              childrenItem[that.treeProps.children]
            );
          }
        });
        return selectStatuaArr;
      }
      function getLevelStatus(row) {
        //如果当前节点的parantId =0 并且有子节点，则为1
        //如果当前节点的parantId !=0 并且子节点没有子节点 则为3
        if (row.parentId == 0) {
          if (
            row[that.treeProps.children] &&
            row[that.treeProps.children].length
          ) {
            return 1;
          } else {
            return 4;
          }
        } else {
          if (
            !row[that.treeProps.children] ||
            !row[that.treeProps.children].length
          ) {
            return 3;
          } else {
            return 2;
          }
        }
      }
      let result = {};
      //获取明确的节点
      function getExplicitNode(data, parentId) {
        data.forEach((item) => {
          if (item.id == parentId) {
            result = item;
          }
          if (
            item[that.treeProps.children] &&
            item[that.treeProps.children].length
          ) {
            getExplicitNode(item[that.treeProps.children], parentId);
          }
        });
        return result;
      }
      function operateLastLeve(row) {
        //操作的是子节点  1、获取父节点  2、判断子节点选中个数，如果全部选中则父节点设为选中状态，如果都不选中，则为不选中状态，如果部分选择，则设为不明确状态
        let selectStatuaArr = [];
        let item = getExplicitNode(that.source, row.parentId);
        selectStatuaArr = getSelectStatus(
          selectStatuaArr,
          item[that.treeProps.children] || []
        );
        if (
          selectStatuaArr.every((selectItem) => {
            return true == selectItem;
          })
        ) {
          item.isSelect = true;
          that.$refs.myTable.toggleRowSelection(item, true);
        } else if (
          selectStatuaArr.every((selectItem) => {
            return false == selectItem;
          })
        ) {
          item.isSelect = false;
          that.$refs.myTable.toggleRowSelection(item, false);
        } else {
          item.isSelect = "";
        }
        //则还有父级
        if (item?.parentId&&item?.parentId != 0) {
          operateLastLeve(item);
        }
      }
      //判断操作的是子级点复选框还是父级点复选框，如果是父级点，则控制子级点的全选和不全选

      //1、只是父级 2、既是子集，又是父级 3、只是子级
      let levelSataus = getLevelStatus(row);

      if (levelSataus == 1) {
        selectAllChildrens(row[that.treeProps.children] || []);
      } else if (levelSataus == 2) {
        selectAllChildrens(row[that.treeProps.children] || []);
        operateLastLeve(row);
      } else if (levelSataus == 3) {
        operateLastLeve(row);
      }
      this.$emit(
        "checkStrictlyChange",
        this.filterAndRemoveNested(this.source)
      );
    },
    checkIsAllSelect() {
      let oneProductIsSelect = [];
      this.source.forEach((item) => {
        oneProductIsSelect.push(item.isSelect);
      });
      //判断一级是否是全选.如果一级全为true，则设置为取消全选，否则全选
      let isAllSelect = oneProductIsSelect.every((selectStatusItem) => {
        return true == selectStatusItem;
      });
      return isAllSelect;
    },
    headerRowClassName({ row }) {
      let oneProductIsSelect = [];
      this.source.forEach((item) => {
        oneProductIsSelect.push(item.isSelect);
      });
      if (oneProductIsSelect.includes("")) {
        return "indeterminate";
      }
      return "";
    },
    rowClassNameFun({ row ,rowIndex }) {
      row.xh = rowIndex + 1;
      if(this.showSequenceNumber) row.sequenceNumber = rowIndex + 1;
      if (row.isSelect === "") {
        return "indeterminate";
      }
    },
    filterAndRemoveNested(data) {
      let filteredData = [];
      data.forEach((item) => {
        if (item.isSelect && !item.disabled) {
          // 克隆当前对象，删除 children 属性，并添加到 filteredData
          let clonedItem = { ...item };
          delete clonedItem[this.treeProps?.children];
          filteredData.push(clonedItem);
        }

        // 递归处理子节点数据
        if (
          item[this.treeProps?.children] &&
          item[this.treeProps?.children].length > 0
        ) {
          let nestedFiltered = this.filterAndRemoveNested(
            item[this.treeProps?.children]
          );
          filteredData = filteredData.concat(nestedFiltered);
        }
      });

      return filteredData;
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep .hiddenCheck > .cell .el-checkbox__inner {
  display: none;
}
::v-deep .el-table__row .cell {
  white-space: pre-line !important;
}
::v-deep .indeterminate .el-checkbox__input .el-checkbox__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #fff !important;
}

::v-deep
  .indeterminate
  .el-checkbox__input.is-checked
  .el-checkbox__inner::after {
  transform: scale(0.5);
}

::v-deep .indeterminate .el-checkbox__input .el-checkbox__inner {
  background-color: #f2f6fc;
  border-color: #dcdfe6;
}
::v-deep .indeterminate .el-checkbox__input .el-checkbox__inner::after {
  border-color: #c0c4cc !important;
  background-color: #c0c4cc;
}
::v-deep .product-show th .el-checkbox__inner {
  display: none !important;
}

::v-deep .indeterminate .el-checkbox__input .el-checkbox__inner::after {
  content: "";
  position: absolute;
  display: block;
  background-color: #fff;
  height: 2px;
  transform: scale(0.5);
  left: 0;
  right: 0;
  top: 5px;
  width: auto !important;
}
::v-deep .product-show .el-checkbox__inner {
  display: block !important;
}

::v-deep .product-show .el-checkbox {
  display: block !important;
}


</style>

