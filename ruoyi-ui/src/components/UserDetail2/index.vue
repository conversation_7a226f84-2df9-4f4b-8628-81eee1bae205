<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleClose"
    >
      <div style="display: flex">
        <div style="margin-right: 40px">
          <el-avatar :size="80" :src="avatar"></el-avatar>
          <div class="mt-5" style="text-align: center">
            <el-button type="text" @click="toUserDetail">查看详情</el-button>
          </div>
        </div>
        <div class="message">
          <H2>{{ userData.nickName }}</H2>
          <p>
            公司<span>{{
              userData.unit && userData.unit.unitName
                ? userData.unit.unitName
                : ""
            }}</span>
          </p>
          <p>
            部门<span>{{ userData.dept.deptName }}</span>
          </p>
          <p>
            岗位<span>{{ userPost }}</span>
          </p>
          <p>
            手机<span>{{ userData.phonenumber }}</span>
          </p>
          <p>
            邮箱<span>{{ userData.email }}</span>
          </p>
        </div>
      </div>
      <el-divider></el-divider>
      <p>授权维度：{{ authType }}</p>
      <span slot="footer" class="dialog-footer"> </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import { getUserData } from "@/api/flow/flow";

export default {
  name: "UserDetail2",
  props: {
    userId: Number,
    authType: String,
  },
  data() {
    return {
      userPost:'',
      avatar: "",
      userData: null,
      dialogVisible: true,
    };
  },
  mounted() {
    this.getData();
  },
  //   const avatar = user.avatar == "" ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
  methods: {
    toUserDetail() {
      this.$router.push({
        path: "/user/userDetail",
        query: {
          userId: this.userData.userId,
        },
      });
    },
    getData() {
      getUserData(this.userId).then((res) => {
        this.userPost = res.mainPost
        this.userData = res.data;
        this.avatar =
          res.data.avatar == ""
            ? require("@/assets/images/profile.jpg")
            : process.env.VUE_APP_BASE_API + res.data.avatar;
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
  
  <style lang="less" scoped>
.message {
  p {
    color: #999999;
  }
  span {
    margin-left: 10px;
    color: #000;
  }
}
</style>