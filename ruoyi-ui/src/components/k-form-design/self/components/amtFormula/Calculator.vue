<template>
  <a-config-provider :locale="locale">
    <a-row>
      <a-col :span="7">
        <div style="border-right: solid 1px #cccccc; margin-right: 20px; line-height: 25px; font-size: 14px;">
          <ATree :selectedKeys="selectedKeys" show-icon show-line selectable default-expand-all
                 style="width: 220px;"
                 :tree-data="treeDatas"
                 @select="onSelect">
          </ATree>
        </div>
      </a-col>
      <a-col :span="16">
        <div style="font-weight: bold; ">
          <div style="margin-bottom: 20px">
            <span>表达式：</span><br>
            <TextArea style="width: 80%;" v-model="editFormula" :rows="4" @change="changeFormula"/>
          </div>
          <a-row>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="1" @click="calculate('1')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="2" @click="calculate('2')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="3" @click="calculate('3')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'/'" @click="calculate('/')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'('" @click="calculate('(')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="')'" @click="calculate(')')"/>
              </div>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="4" @click="calculate('4')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="5" @click="calculate('5')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="6" @click="calculate('6')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'*'" @click="calculate('*')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aEmpty"></div>
            </a-col>
            <a-col :span="3">
              <div class="aEmpty"></div>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="7" @click="calculate('7')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="8" @click="calculate('8')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="9" @click="calculate('9')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'-'" @click="calculate('-')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aEmpty"></div>
            </a-col>
            <a-col :span="3">
              <div class="aEmpty"></div>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="0" @click="calculate('0')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'.'" @click="calculate('.')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'%'" @click="calculate('%')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aBorder">
                <AButton class="aButton" v-text="'+'" @click="calculate('+')"/>
              </div>
            </a-col>
            <a-col :span="3">
              <div class="aEmpty"></div>
            </a-col>
            <a-col :span="3">
              <div class="aEmpty"></div>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </a-config-provider>
</template>
<style>
.aBorder{
  float: left;
  width: 120px;
  border: solid 1px #cccccc;
  padding: 5px 0px;
  text-align: center
}
.aButton{
  width: 80px;
  height: 25px;
  background: #eeeeee
}
.aEmpty{
  float: left;
  width: 120px;
  height: 43px;
  border: solid 1px #cccccc;
  padding: 5px 0px;
  text-align: center
}
</style>
<script>
import ATree from "ant-design-vue/lib/tree";
import AButton from "ant-design-vue/lib/button";
import TextArea from "ant-design-vue/lib/input/TextArea";
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
export default {
  name: "AmtFormula",
  props: ["datas", "formula",'resetFormula'],
  components: { ATree, AButton, TextArea },
  created() {
    this.init()
  },
  watch: {
    resetFormula(val) {
      console.info("resetFormula:"+val)
      if(val){
        this.editFormula = "";
        this.reset();
      }else{
        this.editFormula = this.formula;
      }
    },
  },
  data() {
    return {
      locale: zhCN,
      treeDatas:[{
        key: "formulaDef",
        title: "公式定义器",
        children:[
          {
            title: '变量',
            key: 'variable',
            children: this.init()
          }
        ]
      }
      ],
      selectedKeys: [],
      editFormula: ""
    };
  },
  methods: {
    init() {
      this.editFormula = this.formula;
       return this.datas.map((obj) => {
        return {
          key: obj.value,
          title: obj.label,
        };
      });
    },
    reset(){
      this.$emit("input",this.editFormula);
    },
    onSelect(selectedKeys, info){
      this.editFormula+="$"+info.node.title+"$";
      this.$emit("input",this.editFormula);
    },
    calculate(text){
      this.editFormula+=text;
      this.$emit("input",this.editFormula);
    },
    changeFormula(event){
      this.editFormula = event.target.value;
      this.$emit("input",this.editFormula);
    }
  }
};
</script>
