<template>
  <a-config-provider :locale="locale">
    <a-form>
      <a-form-item label="类型">
        <Select v-model="options.selectType" :options="options.selectTypes" @change="changeType"/>
      </a-form-item>
      <a-form-item label="选择范围">
        <Select v-model="options.selectRange" :options="options.selectRanges" @change="changeRange"/>
        <div v-if="showRange">
          <Select v-model="selectedRange" placeholder="请选择" mode="multiple" :options="rangeDict" show-search @select="selectRange" @deselect="deSelectRange"/>
<!--          <TreeSelect
            v-model="options.selectRange"
            show-search
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择"
            allow-clear
            multiple
            show-arrow
            tree-default-expand-all
            :tree-data="deptTrees"
            :field-names="{
              children: 'children',
              value: 'id',
              label: 'label',
            }"
            tree-node-filter-prop="label"
          ></TreeSelect>-->
        </div>
      </a-form-item>

      <a-form-item label="初始值">
        <Select v-model="options.initValue" :options="options.selectInits" @change="changeInitValue" placeholder="请选择"/>
        <div v-if="showInitValue">
          <Select v-model="selectedInitValue" :options="initDict" show-search @select="selectInitValue" placeholder="请选择"/>
        </div>
      </a-form-item>
    </a-form>
  </a-config-provider>
</template>

<script>
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import {pluginManager} from "../../../packages";
import {getDictList, getDeptList, getPostList, getUserList} from "@/api/form/formdesign";

const Select = pluginManager.getComponent("select").component;
const TreeSelect = pluginManager.getComponent("treeSelect").component;

export default {
  name: "AddressNote",
  props: ["options"],
  components: { Select, TreeSelect },
  created() {
    this.init()
  },
  data() {
    return {
      locale: zhCN,
      showRange: false,
      showInitValue: false,
      //范围字典
      rangeDict: [],
      initDict: [],
      // 部门树选项
      deptTrees: undefined,
      companyMap: {},
      deptMap: {},
      postMap: {},
      userMap: {},
      selectedRange: undefined,
      selectedInitValue: undefined,
      types: [
        {value: "1", label: "公司"},
        {value: "2", label: "部门"},
        {value: "3", label: "岗位"},
        {value: "4", label: "人员"}
      ]
    };
  },
  methods: {
    init() {
      this.options.selectTypes = this.types;
    },
    typeReset(){
      this.options.options = [];
      this.options.defaultValue = "";
      this.options.selectRanges = [];
      this.options.selectInits = [];
      this.options.selectRange = "";
      this.options.initValue = "";
      this.rangeDict = [];
      this.initDict = [];
      this.showRange = false;
      this.showInitValue = false;
      this.selectedRange = undefined;
      this.selectedInitValue = undefined;
    },
    changeType(value){
      this.typeReset();

      this.options.selectRanges.push({value: "1", label: "全部"})
      this.options.selectInits.push({value: "1", label: "无"})

      if(value == 1 || value == 2 || value == 3){
        this.options.selectRanges.push({value: "2", label: "发起人所属"})
        var str = value ==1?"公司":value==2?"部门":"岗位";
        this.options.selectRanges.push({value: "3", label: "指定"+str})

        this.options.selectInits.push({value: "2", label: "发起人所属主要"})
      }else {
        this.options.selectRanges.push({value: "2", label: "指定部门"})
        this.options.selectRanges.push({value: "3", label: "指定岗位"})
        this.options.selectRanges.push({value: "4", label: "指定人员"})
      }
      this.options.selectInits.push({value: "3", label: "固定值"})

      this.$emit("input", value);
    },

    /*********************************选择范围-begin***************************************/
    rangeReset(){
      this.options.selectInits = [];
      this.options.initValue = "";
      this.rangeDict = [];
      this.initDict = [];
      this.showInitValue = false;
      this.selectedInitValue = undefined;
    },
    changeRange(value){
      this.rangeReset();
      this.options.selectInits.push({value: "1", label: "无"})
      if(this.options.selectType != 4){
        this.options.selectInits.push({value: "2", label: "发起人所属主要"})
      }
      this.options.selectInits.push({value: "3", label: "固定值"})

      this.showRange = false
      if(value == 3||
        (this.options.selectType == 4 && this.options.selectRange == 2)||
        (this.options.selectType == 4 && this.options.selectRange == 3)||
        (this.options.selectType == 4 && this.options.selectRange == 4)){
        this.showRange = true
        this.getDatabaseData();
      }else{
        this.options.options = [];
        this.options.defaultValue = "";
        this.selectedRange = undefined;
      }
    },
    selectRange(value){
      this.switchSelected(value);
    },
    deSelectRange(value){
      this.options.options = this.options.options.filter((val, index) => value != val.value);
    },
    /*********************************选择范围-end***************************************/

    /*********************************初始值-begin***************************************/
    changeInitValue(value){
      this.showInitValue = false
      if(value == 3){
        this.showInitValue = true
        if(this.options.options.length > 0){
          this.initDict = this.options.options;
        }else{
          this.getDatabaseData();
        }
      }else{
        this.selectedInitValue = undefined;
        this.options.defaultValue = "";
      }
    },
    selectInitValue(value){
      this.options.defaultValue = value;
      if(this.initDict.length == 0){
        this.switchSelected(value);
      }
    },
    /*********************************初始值-end***************************************/

    /**switch下拉列表*/
    switchSelected(value){
      switch (this.options.selectType){
        case '1':
          this.options.options.push(this.companyMap[value])
          break;
        case '2':
          this.options.options.push(this.deptMap[value])
          break;
        case '3':
          this.options.options.push(this.postMap[value])
          break;
        case '4':
          if(this.options.selectRange ==2){
            this.options.options.push(this.deptMap[value])
          }else if(this.options.selectRange ==3){
            this.options.options.push(this.postMap[value])
          }else{
            this.options.options.push(this.userMap[value])
          }
          break;
      }
    },
    /**获取数据库数据*/
    getDatabaseData(){
      //指定公司
      if(this.options.selectType == 1){
        getDictList({dictType: 'cust_no'}).then(response => {
          this.rangeDict = response.rows.map((obj) => {
            return {
              value: obj.dictValue,
              label: obj.dictLabel,
            };
          });
          this.initDict = this.rangeDict;
          response.rows.forEach(obj => {this.companyMap[obj.dictValue] =  {value: obj.dictValue,label: obj.dictLabel}})
        });
      }else if(this.options.selectType == 2 ||(this.options.selectType == 4 && this.options.selectRange == 2)){//指定部门
        /*treeselect().then(response => {
          this.deptTrees = response.data;
        });*/
        getDeptList({}).then(response => {
          this.rangeDict = response.data.map((obj) => {
            return {
              value: obj.deptId,
              label: obj.deptName,
            };
          });
          this.initDict = this.rangeDict;
          response.data.forEach(obj => {this.deptMap[obj.deptId] =  {value: obj.deptId,label: obj.deptName}})
        });
      }else if(this.options.selectType == 3 ||(this.options.selectType == 4 && this.options.selectRange == 3)){//指定岗位
        getPostList({}).then(response => {
          this.rangeDict = response.rows.map((obj) => {
            return {
              value: obj.postId,
              label: obj.postName,
            };
          });
          this.initDict = this.rangeDict;
          response.rows.forEach(obj => {this.postMap[obj.postId] =  {value: obj.postId,label: obj.postName}})
        });
      }else if((this.options.selectType == 4 && this.options.selectRange == 4) || this.options.selectType == 4){//指定人员
        getUserList({}).then(response => {
          this.rangeDict = response.rows.map((obj) => {
            return {
              value: obj.userId,
              label: obj.nickName,
            };
          });
          this.initDict = this.rangeDict;
          response.rows.forEach(obj => {this.userMap[obj.userId] =  {value: obj.userId,label: obj.nickName}})
        });
      }
    }
  }
};
</script>
