import { nodeSchema } from "../packages/mini";

import Input from "ant-design-vue/lib/input";
import InputNumber from "ant-design-vue/lib/input-number";
import Select from "ant-design-vue/lib/select";

// 金额转换大写组件
nodeSchema.addSchemas([
  {
    type: "amtConvert", // 表单类型
    label: "金额转换", // 标题文字
    icon: "icon-money",
    component: Input,
    options: {
      type: "text",
      width: "100%", // 宽度
      placeholder: "请输入", // 没有输入时，提示文字
      showLabel: true,
      associatedElement: "",//关联元素
      associatedElements:[],//下拉关联元素
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  }
]);

// 公式
nodeSchema.addSchemas([
  {
    type: "amtFormula", // 表单类型
    label: "公式", // 标题文字
    icon: "icon-ai-code",
    component: InputNumber,
    options: {
      type: "text",
      width: "100%", // 宽度
      placeholder: "请输入", // 没有输入时，提示文字
      showLabel: true,
      formula: "",//公式
      selectType: "",
      associatedElements:[],//form元素
      associatedElement: "",//关联元素
      calculateType: "",
      fixedNum: 2,
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  }
]);

// 地址本组件
nodeSchema.addSchemas([
  {
    type: "addressNote", // 表单类型
    label: "地址本", // 标题文字
    icon: "icon-xiala",
    component: Select,
    options: {
      type: "select",
      width: "100%", // 宽度
      placeholder: "请输入", // 没有输入时，提示文字
      clearable: false,
      defaultValue: "", // 默认值
      options: [],//下拉选项
      selectType: "",
      selectRange: "",
      initValue: "",
      selectTypes: [],//下拉类型
      selectRanges: [],//下拉选择范围
      selectInits: [],//下拉初始值
      showSearch: false, // 是否显示搜索框，搜索选择的项的值，而不是文字
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  }
]);

// 下拉绑定组件
nodeSchema.addSchemas([
  {
    type: "payee", // 表单类型
    label: "收/付款人", // 标题文字
    icon: "icon-people",
    component: Select,
    options: {
      type: "select",
      width: "100%", // 宽度
      placeholder: "请输入", // 没有输入时，提示文字
      options: [],//下拉选项
      associatedElement: "",//关联元素
      associatedElements:[],//下拉关联元素
      selectType: "",//类型
      componentType: "",//组件类型
      isEdit: false,//是否可编辑
      clearable: false, // 是否显示清除按钮
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  }
]);

// 时间差
nodeSchema.addSchemas([
  {
    type: "diffTime", // 表单类型
    label: "时间差", // 标题文字
    icon: "icon-ai-code",
    component: Input,
    options: {
      type: "text",
      width: "100%", // 宽度
      placeholder: "请输入", // 没有输入时，提示文字
      showLabel: true,
      formula: "",//公式
      selectType: "",
      associatedElements:[],//form元素
      associatedElement: "",//关联元素
    },
    model: "",
    key: "",
    help: "",
    rules: [
      {
        required: false,
        message: "必填项"
      }
    ]
  }
]);

// 添加分组
nodeSchema.addSchemaGroup({
  title: "自定义组件",
  list: ["amtConvert", "amtFormula", "addressNote", "payee", "diffTime"]
});
