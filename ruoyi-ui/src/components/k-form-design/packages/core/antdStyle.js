// 按需加载 antd ui 样式
import "ant-design-vue/lib/input/style";
import "ant-design-vue/lib/input-number/style";
import "ant-design-vue/lib/select/style";
import "ant-design-vue/lib/radio/style";
import "ant-design-vue/lib/switch/style";
import "ant-design-vue/lib/rate/style";
import "ant-design-vue/lib/slider/style";
import "ant-design-vue/lib/tree-select/style";
import "ant-design-vue/lib/cascader/style";
import "ant-design-vue/lib/time-picker/style";
import "ant-design-vue/lib/date-picker/style";
import "ant-design-vue/lib/upload/style";
import "ant-design-vue/lib/button/style";
import "ant-design-vue/lib/layout/style";
import "ant-design-vue/lib/config-provider/style";
import "ant-design-vue/lib/checkbox/style";
import "ant-design-vue/lib/card/style";
import "ant-design-vue/lib/empty/style";
import "ant-design-vue/lib/form/style";
import "ant-design-vue/lib/row/style";
import "ant-design-vue/lib/col/style";
import "ant-design-vue/lib/modal/style";
import "ant-design-vue/lib/table/style";
import "ant-design-vue/lib/tabs/style";
import "ant-design-vue/lib/icon/style";
import "ant-design-vue/lib/alert/style";
import "ant-design-vue/lib/tag/style";
import "ant-design-vue/lib/divider/style";
import "ant-design-vue/lib/message/style";
import "ant-design-vue/lib/tooltip/style";
import "ant-design-vue/lib/collapse/style";
import "ant-design-vue/lib/form-model/style";
import "ant-design-vue/lib/steps/style";
