<template>
  <div>
    <div class="flex flex-wrap">
      <div
        v-for="(item, index) in myForm.projectTypeList"
        :key="index"
        class="mr-3 mb-3 border border-solid rounded px-2 flex h-6 items-center relative top-2"
        style="
          border-color: #cccccc;
          background-color: #f2f2f2;
          font-size: 13px;
        "
      >
        <div class="h-6 leading-6">{{ item.typeName }}</div>
        <div v-if="!item.id" @click="deletType(item)" class="cursor-pointer">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <el-button
        style="height: 30px"
        class="relative top-1"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        @click="dialogVisible=true"
        >添加</el-button
      >
    </div>
    <el-dialog title="添加项目类型" :visible.sync="dialogVisible" width="550px" @open="open" append-to-body	>
      <el-cascader
        v-model="value"
        :options="options"
        :props="{
          expandTrigger: 'hover',
          children: 'fPiattaformas',
          label: 'dataName',
          value: 'id',
        }"
      ></el-cascader>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getDataManageList } from "@/api/notice/management";
import { setEmptyArrayToUndefined ,getNameById} from "@/utils";
export default {
  name: "ProjectTypes",
  props: {
    myForm: {
      type: Object,
      required: false,
      default: () => {},
    },
  },
  data() {
    return {
      options: [],
      value: [],
      dialogVisible: false,
    };
  },
  watch: {
   
  },
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.getDataManageList();
    },

    open(){
      this.handleOptions();
      this.value=[];
    },
    async getDataManageList() {
      const { rows } = await getDataManageList({
        firstDataCode: "project_type",
      });
      this.options=rows[0].fPiattaformas;
      setEmptyArrayToUndefined(this.options, "fPiattaformas");
      this.handleOptions();
    },
    addDisabled(options){
       options.forEach((item) => {
        if (this.myForm.projectTypeList?.map(item1=>item1.typeId).includes(item.id)) {
          this.$set(item,'disabled',true);
        }else{
          this.$set(item,'disabled',false);
        }
        if(item.fPiattaformas&&item.fPiattaformas.length){
          this.addDisabled(item.fPiattaformas)
        }
      });
    },
    handleOptions() {
      this.addDisabled(this.options);
      
    },

    deletType(value) {
      this.myForm.projectTypeList = this.myForm.projectTypeList.filter(
        (item) => item.typeName !== value.typeName
      );
    },
    async submit() {
      if (!this.value || !this.value.length) {
        this.$message.warning("请选择项目类型");
        return;
      }
      const typeName=getNameById(this.options,this.value[this.value.length-1]);
      const projectTypeList = this.myForm.projectTypeList
        ? this.myForm.projectTypeList.concat([{typeName,typeId:this.value[this.value.length-1]}])
        : [{typeName,typeId:this.value[this.value.length-1]}];
      this.$set(this.myForm, "projectTypeList", projectTypeList);
      this.dialogVisible=false;
    },
  },
};
</script>


