<template>
  <div>
    <MyTable
      :columns="columnsRecord"
      :source="configList"
      :row-style="rowStyle"
      :cell-style="cellStyle"
      border
    />
  </div>
</template>

<script>
export default {
  name: "ContrastDetailNewTable",
  inject: {
    contrast: {
      default: () => ({}),
    },
    dictionary: {
      default: () => ({}),
    },
    arraryToStringItem: {
      default: () => ({}),
    },
    arraryToString: {
      default: () => ([]),
    },
    oldInfo: {
      default: "oldInfo",
    },
    newInfo: {
      default: "newInfo",
    },
    dictionaryBy: {
      default: "code",
    },
  },
  props: {
    data: {
      type: Object,
      default: () => {},
      required: true,
    },
  },
  data() {
    return {
      columnsRecord: Object.freeze([
        { label: "", prop: "title",width:100 },
        { label: "修改前", prop: "before" ,width:200 },
        { label: "修改后", prop: "after",width:200  },
      ]),
      configList: [],
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const newInfo = this.data[this.newInfo]
        ? JSON.parse(this.data[this.newInfo])
        : {};
      const oldInfo = this.data[this.oldInfo]
        ? JSON.parse(this.data[this.oldInfo])
        : {};
      this.getRecordList(newInfo, oldInfo,this.contrast,this.arraryToStringItem);
    },
    getRecordList(newInfo, oldInfo,contrast,arraryToStringItem) {
      Object.keys(this.dictionary).forEach((item) => {
        if (
          Object.prototype.toString.call(this.dictionary[item]) ===
          "[object String]"
        ) {
          //说明是字典
          newInfo[item] =
            this.$store.state.data[
              this.dictionaryBy == "code" ? "KV_MAP_CODE" : "KV_MAP"
            ][this.dictionary[item]][newInfo[item]];
          oldInfo[item] =
            this.$store.state.data[
              this.dictionaryBy == "code" ? "KV_MAP_CODE" : "KV_MAP"
            ][this.dictionary[item]][oldInfo[item]];
        } else {
          newInfo[item] = this.dictionary[item][newInfo[item]];
          oldInfo[item] = this.dictionary[item][oldInfo[item]];
        }
      });
      Object.keys(arraryToStringItem)?.forEach((item) => {
        newInfo[item] = newInfo[item]
          ?.map((item1) => item1[arraryToStringItem[item]])
          .join("\n");
        oldInfo[item] = oldInfo[item]
          ?.map((item1) => item1[arraryToStringItem[item]])
          .join("\n");
      });
      this.arraryToString?.forEach((item) => {
        newInfo[item] = newInfo[item]?.join(",");
        oldInfo[item] = oldInfo[item]?.join(",");
      });
      this.configList = Object.keys(contrast).map((key) => {
        return {
          title: contrast[key],
          before: oldInfo[key],
          after: newInfo[key],
          diff: oldInfo[key] == newInfo[key] ? false : true,
        };
      });
    },
    rowStyle({ row, rowIndex }) {
      if (row.diff) {
        return {
          color: "#cccccc",
        };
      }
    },
    cellStyle({ column, columnIndex }) {
      // 设置第一列的字体颜色为黑色
      if (columnIndex === 0) {
        return {
          color: "#363636",
        };
      }
    },
  },
};
</script>
