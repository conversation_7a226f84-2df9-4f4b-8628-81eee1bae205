<template>
  <div class="inline-block">
    <el-form-item
      prop="moreSearch"
      label="公司类型"
      v-for="(item, index) in formData"
      :key="index"
    >
      <el-select
        v-model="item.a"
        @change="onAChange(index)"
        clearable
        filterable
        class="mr-2"
      >
        <el-option
          v-for="item in $store.state.data.OPTION_MAP.select_name"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        ></el-option>
      </el-select>

      <el-select
        v-model="item.b"
        @change="updateParams"
        clearable
        filterable
        multiple
        collapse-tags
        class="mr-2"
      >
        <el-option
          v-for="item1 in item.bOptions"
          :key="item1.value"
          :value="item1.value"
          :label="item1.label"
        ></el-option>
      </el-select>
      <el-button
        type="text"
        @click="removePair(index)"
        v-show="formData.length != 1"
        >- 删除</el-button
      >
      <el-button type="text" @click="addPair">+ 添加 </el-button>
    </el-form-item>
  </div>
</template>

<script>
import { getSelectSysDictRefList } from "@/api/ref/ref";
export default {
  name: "MoreSearch",
  props: {
    params: {
      type: Object,
      required: true,
      default: () => {},
    },
    modelCode: {
      type: String,
      required: true,
      default: "",
    },
    byId: {
      default: "id",
    },
    needHandel: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 存储每对下拉框 A 和 B 的数据
      formData: [
        { a: null, b: [], bOptions: [] }, // 默认一对下拉框
      ],
    };
  },
  computed: {},
  watch: {
    "params.moreSearch": function (newVal) {
      console.log(newVal);
      if (newVal == undefined) {
        this.formData.forEach((item) => {
          item.a = null;
          item.b = [];
        });
      }
    },
  },
  mounted() {
    this.$set(this.params, "moreSearch", {});
  },
  methods: {
    // 添加一对下拉框
    addPair() {
      this.formData.push({ a: null, b: [], bOptions: [] });
    },
    // 删除一对下拉框
    removePair(index) {
      this.formData.splice(index, 1);
      this.updateParams(); // 更新 params
    },
    // 当A的选择变化时，更新B的选项并清空B的值
    onAChange(index) {
      this.formData[index].b = []; // 清空当前 B 的选中值
      this.fetchBOptions(index); // 重新加载B的选项
      this.updateParams(); // 更新 params
    },
    // 根据 A 的选中值动态加载 B 的选项
    async fetchBOptions(index) {
      let selectedA = this.formData[index].a;
      if(selectedA=='cust'){
        selectedA='0'
      }else if(selectedA=='partner'){
        selectedA='1'
      }else if(selectedA=='fund'){
        selectedA='2'
      }
      const bOptions = await getSelectSysDictRefList({
        unitType: selectedA,
        moduleTypeOfNewAuth: this.modelCode,
      });
      this.$set(this.formData, index, {
        ...this.formData[index],
        bOptions: bOptions,
      });
    },
    // 更新 params 参数
    updateParams() {
      this.params.moreSearch = {};
      // 遍历所有 formData，更新 params
      this.formData.forEach((item) => {
        //为兼容历史数据特殊处理键名
        if (item.a && item.b.length > 0) {
          // 确保 a 和 b 都有值才更新
          let key = item.a;
          if (this.needHandel) {
            // 根据键名进行转换
            if (key === "cust") {
              key = "0";
            } else if (key === "fund") {
              key = "2";
            } else if (key === "partner") {
              key = "1";
            } else if (key === "other") {
              key = "3";
            }
          }

          if (!this.params.moreSearch[key]) {
            this.$set(this.params.moreSearch, key, []);
          }
          // 合并 b 的选中值，确保没有重复
          item.b.forEach((bValue) => {
            if (!this.params.moreSearch[key].includes(bValue)) {
              this.params.moreSearch[key].push(bValue);
            }
          });
        }
      });
    },
  },
};
</script>
