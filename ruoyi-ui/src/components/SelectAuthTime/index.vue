<template>
  <div>
    <el-dialog
      title="选择授权期限"
      :visible.sync="dialogVisible"
      width="480px"
      :before-close="handleClose"
    >
      <div class="flex">
        <p class="font-bold">授权期限：</p>
        <div>
          <el-radio v-model="radio" label="1">永久有效</el-radio><br /><br />
          <el-radio v-model="radio" label="2">设置到期日</el-radio>
          <div class="mt-4" v-if="radio == 2">
            <el-date-picker v-model="date" type="date" placeholder="选择日期">
            </el-date-picker>
            <p class="mt-2">授权到期后，用户将自动失去权限</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button size="mini" type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "SelectAuthTime",
  data() {
    return {
      date: "",
      radio: "1",
      dialogVisible: true,
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    submit() {
      if (this.radio == 2 && !this.date) {
        this.$message.warning("请选择到期日");
        return;
      }
      this.$emit("submit",this.date);
    },
  },
};
</script>

<style>
</style>