<template>
  <div>
    <el-dialog
      title="选择人员"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="content">
        <div class="left">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px; width: 250px"
          />
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :filter-node-method="filterNode"
            ref="tree"
            :default-expand-all="false"
            @node-click="handleNodeClick"
          />
        </div>
        <div class="right">
          <div style="margin-bottom: 14px">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入账号"
              clearable
              size="small"
              style="width: 240px; margin-right: 10px"
            ></el-input>
            <el-input
              v-model="queryParams.phonenumber"
              placeholder="请输入手机号码"
              clearable
              size="small"
              style="width: 240px; margin-right: 10px"
            />
            <el-button type="primary" @click="search">搜索</el-button>
          </div>
          <el-table
            ref="multipleTable"
            v-loading="loading"
            @select="handleSelectionChange"
            :data="userList"
            row-key="userId"
            style="width: 100%"
          >
            <el-table-column
              :reserve-selection="true"
              type="selection"
              width="55"
            />
            <el-table-column prop="userName" label="账号" />
            <el-table-column prop="nickName" label="姓名" />
            <el-table-column label="公司" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <template
                  v-for="item in userPostListAll.filter(
                    (item) => item.userId === scope.row.userId
                  )"
                >
                  {{ item.companyShortName }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="部门" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <template
                  v-for="item in userPostListAll.filter(
                    (item) => item.userId === scope.row.userId
                  )"
                >
                  {{ item.deptName }}
                </template>
              </template>
            </el-table-column>

            <el-table-column label="岗位" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <template
                  v-for="item in userPostListAll.filter(
                    (item) => item.userId === scope.row.userId
                  )"
                >
                  {{ item.postName }}
                  {{ item.homePost === "0" ? "（主要）" : "" }}
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            :autoScroll="false"
            v-if="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { treeselect } from "@/api/system/dept";
import { queryUserInfoList } from "@/api/system/user";
import { optionselect, userPostSetList, postSetList } from "@/api/system/post";

export default {
  props: {
    selectType: Number,
  },
  data() {
    return {
      deptName: "",
      defaultProps: {
        children: "children",
        label: "label",
      },
      dialogVisible: true,
      selectList: [],
      deptOptions: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        phonenumber: null,
        deptId: null,
        status: "0",
      },
      userList: [],
      loading: false,
      total: 0,
      userPostListAll: [],
      bill_id: "",
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    this.getTreeselect();
  },
  methods: {
    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleSelectionChange(selection, row) {
      this.selectList = [];
      if (this.selectType != 3) {
        if (selection.length > 1) {
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(
            selection[selection.length - 1]
          );
        }
        this.selectList =
          [selection[selection.length - 1]] ==
          ("undefined" || undefined || null)
            ? []
            : [selection[selection.length - 1]];
      } else {
        this.selectList = selection;
      }

      console.log(this.selectList);
    },
    getList1() {},
    getList() {
      if(this.queryParams.userName){
        this.queryParams.deptId = null
      }
      queryUserInfoList(this.queryParams).then((response) => {
        this.userList = response.rows;
        this.total = response.total;
      });
      this.getUserPostSetListF();
    },
    /**查询用户岗位公司部门集合 */
    getUserPostSetListF() {
      userPostSetList().then((response) => {
        this.userPostListAll = response.data;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getTreeselect() {
      treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    handleClose() {
      this.$emit("close");
    },
    confirm() {
      if (this.selectList.length == 0 || !this.selectList[0]) {
        this.$message.warning("请选择");
        return;
      }
      this.$emit("confirm", this.selectList);
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  .left {
    width: 300px;
    flex-shrink: 0;
  }
  .right {
    flex: 1;
  }
}
/deep/ .el-table .cell {
  text-align: center;
}
/deep/ .el-table .el-table__header-wrapper .el-checkbox {
  display: none;
}
/deep/ .el-input__inner {
  height: 36px !important;
}
</style>
