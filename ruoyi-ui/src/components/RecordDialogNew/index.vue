<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="700px"
    >
      <div style="max-height: 70vh" class="overflow-y-auto">
        <MyTable :columns="columnsRecord" :source="recordList">
          <template #operate="{ record }">
            <el-button type="text" @click="openDetail(record)"
              >查看详情</el-button
            >
          </template>
        </MyTable>
        <pagination
          v-show="total > 20"
          :total="total"
          :page.sync="queryParams.pageNum"
          :page-sizes="[20, 50, 100]"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
      <ContrastDetailNew v-model="contrastDetailNew" :data="data" />
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { selectEditRecord2 } from "@/api/oa/voucharRules";

export default {
  name: "RecordDialogNew",
  mixins: [vModelMixin],
  props: {
    title: {
      type: String,
      default: "编辑记录",
    },
    columnsRecord: {
      type: Array,
      default: () => [
        { label: "编辑日期", prop: "editTime" },
        { label: "编辑人员", prop: "nickName" },
        { label: "操作", key: "operate" },
      ],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    params: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      data: {},
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      recordList: [],
      contrastDetailNew: false,
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handelOpen() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      this.getList();
    },
    async getList() {
      const params = {
        ...this.queryParams,
        applyType: this.params.applyType,
        oaApplyId: this.params.oaApplyId,
      };
      const { rows, total } = await selectEditRecord2(params);
      this.recordList = rows;
      this.total = total;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    openDetail(record) {
      if (!this.multiple) {
        this.contrastDetailNew = true;
      }
      this.$emit("openDetail", record);
      this.data = record;
    },
  },
};
</script>
