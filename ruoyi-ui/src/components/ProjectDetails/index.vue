<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      @open="handelOpen"
      :width="width"
    >
      <!-- <el-scrollbar> -->
        <div style="padding-right:5px">
          <slot>
            <div>项目名称: {{ params.projectName }}</div>
          </slot>
          <div class="mb-2">共{{ total }}笔</div>
          <MyTable :columns="columnsRecord" :source="recordList"> </MyTable>
          <pagination
            v-show="total > 10"
            :total="total"
            :page.sync="queryParams.pageNum"
            :page-sizes="[10,20, 50, 100]"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      <!-- </el-scrollbar> -->
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";

export default {
  name: "ProjectDetails",
  mixins: [vModelMixin],
  props: {
    width: {
      type: String,
      default: "750px",
    },
    title: {
      type: String,
      default: "查看明细",
    },
    columnsRecord: {
      type: Array,
      default: () => [],
      required: true,
    },
    params: {
      type: Object,
      required: true,
      default: () => {},
    },
    api: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      recordList: [],
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handelOpen() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    async getList() {
      const params = {
        ...this.queryParams,
        ...this.params,
      };
      const { rows, total } = await this.api(params);
      rows.forEach(item=>{
        const toSolit=["backAmt","receiveAmt","receiveCompensateAmt","actCompensateAmt","manaulCompensateAmt","totalAmt","addAmt","subtractAmt"];
        toSolit.forEach(item1=>{
          item[item1]=item[item1]&&item[item1].toLocaleString("zh-CN")
        })
      })
      this.recordList = rows;
      this.total = total;
    },
  },
};
</script>
