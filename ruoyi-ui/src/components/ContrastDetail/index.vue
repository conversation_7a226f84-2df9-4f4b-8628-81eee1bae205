<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="编辑内容"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="700px"
    >
      <div style="max-height: 80vh">
        <MyTable
          :columns="columnsRecord"
          :source="recordList"
          :row-style="rowStyle"
          :cell-style="cellStyle"
          border
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { getEditRecord } from "@/api/system/record";

export default {
  name: "ContrastDetail",
  mixins: [vModelMixin],
  inject: {
    contrast: {
      default: () => ({}),
    },
    dictionary: {
      default: () => ({}),
    },
    arraryToString: {
      default: () => ({}),
    },
  },
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      columnsRecord: Object.freeze([
        { label: "", prop: "title" },
        { label: "修改前", prop: "before" },
        { label: "修改后", prop: "after" },
      ]),
      recordList: [],
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handelOpen() {
      this.getList();
    },
    async getList() {
      const { data } = await getEditRecord(this.id);
      const newInfo = data?.newInfo ? JSON.parse(data.newInfo) : {};
      const oldInfo = data?.oldInfo ? JSON.parse(data.oldInfo) : {};
      this.getRecordList(newInfo, oldInfo);
    },
    getRecordList(newInfo, oldInfo) {
      Object.keys(this.dictionary).forEach((item) => {
        if (
          Object.prototype.toString.call(this.dictionary[item]) ===
          "[object String]"
        ) {
          //说明是字典
          newInfo[item] =
            this.$store.state.data.KV_MAP[this.dictionary[item]][newInfo[item]];
          oldInfo[item] =
            this.$store.state.data.KV_MAP[this.dictionary[item]][oldInfo[item]];
        } else {
          newInfo[item] = this.dictionary[item][newInfo[item]];
          oldInfo[item] = this.dictionary[item][oldInfo[item]];
        }
      });
      Object.keys(this.arraryToString).forEach((item) => {
        newInfo[item] = newInfo[item]
          ?.map((item1) => item1[this.arraryToString[item]])
          .join("\n");
        oldInfo[item] = oldInfo[item]
          ?.map((item1) => item1[this.arraryToString[item]])
          .join("\n");
      });
      this.recordList = Object.keys(this.contrast).map((key) => {
        return {
          title: this.contrast[key],
          before: oldInfo[key],
          after: newInfo[key],
          diff:oldInfo[key]==newInfo[key]?false:true
        };
      });
    },
    rowStyle({ row, rowIndex }) {
      if (row.diff) {
        return {
          color: '#cccccc' 
        };
      }
    },
    cellStyle({ column, columnIndex }) {
      // 设置第一列的字体颜色为黑色
      if (columnIndex === 0) {
        return {
          color: '#363636'
        };
      }
    }
  },
};
</script>
