// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}
.special-row {
  background: #f2f2f2 !important;
  color: #d9cfcf;
}
.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}
.tags-view-container .tags-view-wrapper .tags-view-item{
  font-size: 14px !important;
}
.el-input.is-disabled .el-input__inner{
  color: #333 !important;
}

.el-textarea.is-disabled .el-textarea__inner{
  color: #333;
}

.treeLine {
  .el-tree-node__content{
    padding-left: 0 !important;
  }
	.el-tree-node {
		position: relative;
		padding-left: 16px;
	}
	.el-tree-node__children {
		padding-left: 16px;
	}
	// 竖线
	.el-tree-node::before {
		content: '';
		height: 100%;
		width: 1px;
		position: absolute;
		left: -3px;
		top: -26px;
		border-width: 1px;
		border-left: 1px dashed #ccc;
	}
	.el-tree-node:last-child::before {
		height: 38px;
	}
	// 横线
	.el-tree-node::after {
		content: '';
		width: 24px;
		height: 20px;
		position: absolute;
		left: -3px;
		top: 12px;
		border-width: 1px;
		border-top: 1px dashed #ccc;
	}
	& > .el-tree-node::after {
		border-top: none;
	}
	& > .el-tree-node::before {
		border-left: none;
	}
	.el-tree-node__expand-icon {
		font-size: 16px;
		&.is-leaf {
			color: transparent;
		}
	}
}
