<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="选择所属公司"
      :visible.sync="innerValue"
      width="480px"
      @open="handleOpen"
    ><p style="color: #999;">请选择发起本流程所在的公司</p>
      <el-select
        v-model="unit"
        placeholder="请选择公司"
        style="width: 400px"
        filterable
      >
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.companyName"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button type="primary" @click="onSubmit">确定</el-button>

          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { newAuthorityCompany } from "@/api/directoryMation/directoryMationSupervise";
export default {
  mixins: [vModelMixin],
  props: {
    currentUnitId: {
      required: true,
      type: [String, Number],
    },
    tableUnitId: {
      required: true,
      type: [String, Number],
    },
    currentUnitName: {
      required: true,
      type: [String],
    },
    tableUnitName: {
      required: true,
      type: [String],
    },
  },
  data() {
    return {
      options: [],
      unit: undefined,
    };
  },

  mounted() {},
  methods: {
    async handleOpen() {
      this.unit = undefined;
      const { rows } = await newAuthorityCompany({
        AuthModuleEnumCode: "OALAUNCH",
      });
      this.options = rows;
    },
    onSubmit() {
      if (this.unit) {
        this.$emit("onSubmit", this.unit);
      } else {
        this.$message.error("请选择所属公司");
      }
    },
  },
};
</script>

<style lang="less" scoped>
</style>
