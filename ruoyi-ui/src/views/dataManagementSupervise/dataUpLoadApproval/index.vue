<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>信息检索</span
        ><el-tooltip
          class="item"
          effect="dark"
          content="包含界面的所有展示数据的内容，不包含资料文件或视频中的文字"
          placement="top-start"
          ><i class="el-icon-question relative right-1 cursor-pointer"></i>
        </el-tooltip>
        <el-input
          v-model="params.informationRetrieval"
          clearable=""
          placeholder="请输入关键字"
          style="width: 200px"
          @input="getList"
        ></el-input>
      </div>
      <div class="item">
        <span>资料名称</span>
        <el-input
          v-model="params.informationName"
          clearable=""
          placeholder="请输入资料名称"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>资料编号</span>
        <el-input
          v-model="params.informationCode"
          clearable=""
          placeholder="请输入资料编号"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>资料年度</span>
        <el-date-picker
          v-model="params.informationYear"
          type="year"
          value-format="yyyy"
          placeholder="选择资料年度"
        >
        </el-date-picker>
      </div>
      <div class="item">
        <span>合作公司</span>
        <el-input
          v-model="params.cooperationCompanyName"
          clearable=""
          placeholder="请输入合作公司"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>合作项目</span>
        <el-input
          v-model="params.cooperationProjectName"
          clearable=""
          placeholder="请输入合作项目"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>状态</span>
        <el-select v-model="params.auditState" style="width: 200px">
          <el-option label="审核通过" value="1"></el-option>
          <el-option label="审核不通过" value="3"></el-option>
          <el-option label="未审核" value="2"></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>资料所属月份</span>
        <el-select v-model="params.informationMonth" style="width: 200px">
          <el-option label="1月" value="1"></el-option>
          <el-option label="2月" value="2"></el-option>
          <el-option label="3月" value="3"></el-option>
          <el-option label="4月" value="4"></el-option>
          <el-option label="5月" value="5"></el-option>
          <el-option label="6月" value="6"></el-option>
          <el-option label="7月" value="7"></el-option>
          <el-option label="8月" value="8"></el-option>
          <el-option label="9月" value="9"></el-option>
          <el-option label="10月" value="10"></el-option>
          <el-option label="11月" value="11"></el-option>
          <el-option label="12月" value="12"></el-option>
        </el-select>
      </div>
    </div>
    <div class="mt-2 ml-4">
      <el-button type="primary" icon="el-icon-search" @click="search"
        >搜 索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="content">
      <div class="left">
        <el-input
          v-model="filterText"
          placeholder="请输入目录名称"
          style="width: 210px"
        ></el-input>
        <el-tree
          class="filter-tree"
           :class="{ 'has-data': leftTreeList && leftTreeList.length > 0 }"
          :data="leftTreeList"
          :props="defaultProps"
          :default-expand-all="false"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          ref="tree"
          node-key="id"
          :highlight-current="true"
        >
        </el-tree>
      </div>
      <div class="right">
        <div class="mb-5">
          您当前正在使用<span style="color: rgba(217, 0, 27, 0.803921568627451)"
            >监管报送</span
          >资料下载审批
        </div>
        <div class="header_btn" style="display: flex; align-items: center">
          <el-button
            v-hasPermi="['dataManagementSupervise:dataUpLoadApproval:export']"
            @click="handleExport"
            style="border-color: #ffc26c; background: #fff8e6; color: #ffc30d"
            icon="el-icon-download"
            size="mini"
            >导出列表</el-button
          >
          <el-button
            v-hasPermi="['dataManagementSupervise:dataUpLoadApproval:download']"
            @click="send('download')"
            style="border-color: #02a7f0; background: #81d3f8; color: #027db4"
            size="mini"
            >发起监管报送资料下载流程</el-button
          >
          <el-button
            v-hasPermi="['dataManagementSupervise:seals']"
            @click="send('seals')"
            style="border-color: #02a7f0; background: #81d3f8; color: #027db4"
            size="mini"
            >发起监管报送资料用印流程</el-button
          >
          <el-button @click="selectItemType = true" type="primary" size="mini"
            >已选择({{ multipleSelection.length }})条</el-button
          >
        </div>
        <el-table
          :data="tableData"
          style="width: 100%; margin-top: 16px; margin-left: 4px"
        >
          <el-table-column align="center" prop="date" width="55">
            <template slot="header" slot-scope="scope">
              <img
                v-if="multipleSelection.length == 0"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="tableData.length > 0 && allType"
                @click="selectionChange(scope.row, 'alldel')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
              <img
                v-show="multipleSelection.length > 0 && !allType"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_show.png')"
                alt=""
              />
            </template>
            <template slot-scope="scope">
              <img
                v-show="!scope.row.acttype"
                @click="selectionChange(scope.row, 'act')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="scope.row.acttype"
                @click="selectionChange(scope.row, 'del')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="xh"
            label="序号"
            width="50"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="informationCode"
            label="资料编号"
            width="180"
          >
            <template #default="{ row }">
              <span
                v-if="row.informationCode"
                v-html="
                  highlightKeyword(
                    row.informationCode,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            prop="informationName"
            min-width="350"
            label="资料/文件名称"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.informationSystemCode"
                v-html="
                  highlightKeyword(
                    scope.row.informationName,
                    params.informationRetrieval
                  )
                "
                style="color: #46a6ff; cursor: pointer"
                @click="see(scope.row)"
              ></div>
              <div v-else>{{ scope.row.informationName || "-" }}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="informationYear"
            label="资料年度"
            width="80"
          >
            <template #default="{ row }">
              <span
                v-if="row.informationYear"
                v-html="
                  highlightKeyword(
                    row.informationYear,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="informationMonth"
            label="资料所属月份"
            width="100"
          >
            <template #default="{ row }">
              <span
                v-if="row.informationMonth"
                v-html="
                  highlightKeyword(
                    row.informationMonthLabel,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueName"
            label="目录"
            width="120"
          >
            <template #default="{ row }">
              <span
                v-if="row.catalogueName"
                v-html="
                  highlightKeyword(
                    row.catalogueName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            label="状态"
            width="120"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.auditStateLabel,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="250"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProject"
                v-html="
                  highlightKeyword(
                    row.cooperationProjectName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="date"
            label="保管期限"
            width="140"
          >
            <template #default="{ row }">
              <span
                v-if="row.saveStartTime"
                v-html="
                  highlightKeyword(
                    row.saveStartTimeLabel,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>永久</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="createBy"
            label="创建人"
            width="120"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.createBy, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            width="220"
            show-overflow-tooltip=""
            prop="createTime"
            label="创建时间"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.createTime, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="params.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="params.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      :deTreeList="leftTreeList"
      @close="addItemType = false"
      :cooperationCompanyList="cooperationCompanyList"
      :cooperationProjectList="cooperationProjectList"
    />
    <PostAuthority
      :type="true"
      v-if="PostAuthorityType"
      @confirmPost="confirmPost"
      @close="PostAuthorityType = false"
    />
    <DepartmentAuthority
      @confimrDe="confimrDe"
      :type="true"
      v-if="DepartmentAuthorityType"
      @close="DepartmentAuthorityType = false"
    />
    <PersonnelAuthority
      @confrimPer="confrimPer"
      :type="true"
      v-if="PersonnelAuthorityType"
      @close="PersonnelAuthorityType = false"
    />
    <SelectItem
      :multipleSelection="multipleSelection"
      @confirm="confirmSelect"
      v-if="selectItemType"
      @close="selectItemType = false"
    />
    <!-- <SelectCompany
      v-if="selectCompanyType"
      @close="selectCompanyType = fasle"
      @submit="submitCompany"
    /> -->
    <DetailDialog
      :currentUnitId="currentUnitId"
      :currentUnitName="currentUnitName"
      :tableUnitId="tableUnitId"
      :tableUnitName="tableUnitName"
      v-model="selectCompanyType"
      @onSubmit="submitCompany"
    />
  </div>
</template>

  <script>
// import SelectCompany from "../../../components/SelectCompany/index.vue";
import DetailDialog from "./components/DetailDialog.vue";

import SelectItem from "./SelectItem.vue";
import addItem from "../inputData/addItem.vue";
import Cookies from "js-cookie";
//   import addItem from "./addItem.vue";
import PostAuthority from "../directoryMation/PostAuthority.vue";
import DepartmentAuthority from "../directoryMation/DepartmentAuthority.vue";
import PersonnelAuthority from "../directoryMation/PersonnelAuthority.vue";
import { getUserProfile } from "@/api/system/user";
import {
  treeselect,
  getTreeList,
  allList,
  information,
  authority,
  informationList,
  informationEdit,
  delsystemInformation,
  commitInformations,
  empowerInformations,
  startInformationFlow,
  insertCommitInformation,
  getDownloadFlow,
  getInformationUsedFlow,
} from "@/api/directoryMation/directoryMationSupervise";
import { allCompanyList } from "@/api/oa/processTemplate";
import { highlightKeyword } from "@/utils/index.js";
import { ziliaogongsi, ziliaoxiangmu } from "@/api/form/formdesign";

export default {
  name: "DataUpLoadApproval",
  components: {
    addItem,
    SelectItem,
    // SelectCompany,
    PostAuthority,
    DepartmentAuthority,
    PersonnelAuthority,
    DetailDialog,
  },
  data() {
    return {
      selectCompanyType: false,
      PersonnelAuthorityType: false,
      DepartmentAuthorityType: false,
      PostAuthorityType: false,
      seeType: false,
      editData: null,
      treeSelect: [],
      addItemType: false,
      defaultProps: {
        children: "informationCatalogueVOList",
        label: "catalogueName",
      },
      projects: [],
      leftTreeList: [],
      multipleSelection: [],
      allType: false,
      selectItemType: false,
      moveItemType: false,
      total: 0,
      params: {
        informationRetrieval: "",
        informationName: "",
        informationCode: "",
        informationYear: "",
        catalogueId: "",
        pageNum: 1,
        pageSize: 10,
        auditState: "",
      },
      tableData: [{ acttype: false }],
      selectList: [],
      time: [],
      filterText: "",
      treeData: [],
      currentUnitId: 0,
      currentUnitName: "",
      tableUnitId: 0,
      tableUnitName: "",
      highlightKeyword,
      auditStateObj: Object.freeze({
        1: "审核通过",
        2: "未审核",
        3: "审核不通过",
        4: "审核中",
      }),
      processType: "download",
      cooperationCompanyList: [],
      cooperationProjectList: [],
    };
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      var flag = newval.every((item) => {
        return item.acttype;
      });
      this.allType = flag ? true : false;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {},
  mounted() {
    this.getTreeList();
    this.getList();
    this.getUserProfile();
    this.getCooperationCompany();
    this.getCooperationProject();
  },
  methods: {
     async getCooperationCompany() {
      const { informationCompany } = await ziliaogongsi();
      this.cooperationCompanyList = informationCompany;
    },
    async getCooperationProject() {
      const { informationProject } = await ziliaoxiangmu();
      this.cooperationProjectList = informationProject;
    },
    async getUserProfile() {
      const { data } = await getUserProfile();
      this.currentUnitId = data.unit.unitId;
      this.currentUnitName = data.unit.unitName;
    },
    submitCompany(companyId) {
      const params = {
        companyId,
      };
      if (this.processType == "download") {
        getDownloadFlow(params).then((res) => {
          if (res.code == 200) {
            this.selectCompanyType = false;
            sessionStorage.setItem(
              "dataListSupervise",
              JSON.stringify(this.multipleSelection)
            );
            this.$router.push({
              path: "/oaWork/updateProcessForm",
              query: {
                templateId: res.templateId,
                classificationId: res.classificationId,
                companyId: res.companyId,
                themeTypeXzSupervise: true,
                count: res.count,
              },
            });
          }
        });
      } else {
        getInformationUsedFlow(params).then((res) => {
          if (res.code == 200) {
            this.selectCompanyType = false;
            sessionStorage.setItem(
              "dataListSupervise",
              JSON.stringify(this.multipleSelection)
            );
            this.$router.push({
              path: "/oaWork/updateProcessForm",
              query: {
                templateId: res.templateId,
                classificationId: res.classificationId,
                companyId: res.companyId,
                themeTypeYySupervise: true,
                count: res.count,
              },
            });
          }
        });
      }
    },
    send(type) {
      this.processType = type;
      if (this.multipleSelection.length == 0) {
        this.$message.warning("请选择数据");
        return;
      }
      const arr = [
        ...new Set(this.multipleSelection.map((item) => item.unitId)),
      ];
      const arrName = [
        ...new Set(this.multipleSelection.map((item) => item.unitName)),
      ];
      if (arr.length > 1) {
        this.$message.warning("请勾选同一公司的资料发起流程");
        return;
      }
      // if (arr[0] == this.currentUnitId) {
      //   this.submitCompany(arr[0]);
      // } else {
        this.tableUnitId = arr[0];
        this.tableUnitName = arrName[0];
        this.selectCompanyType = true;
      // }
    },
    confirmPost(e, v) {
      console.log(e);
      let ids = e.map((item) => item.postId);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 1,
          authorityType: 1,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
          startTime: this.$format(v[0], "yyyy-MM-dd HH:mm:ss"),
          endTime: this.$format(v[1], "yyyy-MM-dd HH:mm:ss"),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.PostAuthorityType = false;
        }
      });
    },
    confrimPer(e, v) {
      console.log(e);
      let ids = e.map((item) => item.userId);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 1,
          authorityType: 2,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
          startTime: this.$format(v[0], "yyyy-MM-dd HH:mm:ss"),
          endTime: this.$format(v[1], "yyyy-MM-dd HH:mm:ss"),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.PersonnelAuthorityType = false;
        }
      });
    },
    confimrDe(e, v) {
      console.log(e);
      let ids = e.map((item) => item.id);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 1,
          authorityType: 0,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
          startTime: this.$format(v[0], "yyyy-MM-dd HH:mm:ss"),
          endTime: this.$format(v[1], "yyyy-MM-dd HH:mm:ss"),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.DepartmentAuthorityType = false;
        }
      });
    },
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
    auth(v) {
      if (this.multipleSelection.length == 0) {
        this.$message.warning("请选择数据");
        return;
      }
      let list = this.multipleSelection.map((item) => item.id);
      empowerInformations(list).then((res) => {
        if (res.code == 200) {
          if (v == 0) {
            this.DepartmentAuthorityType = true;
          } else if (v == 1) {
            this.PostAuthorityType = true;
          } else {
            this.PersonnelAuthorityType = true;
          }
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    newData() {
      this.editData = null;
      this.seeType = false;
      this.addItemType = true;
    },
    see(v) {
      this.editData = { ...v };
      this.seeType = true;
      this.addItemType = true;
    },
    submitItem(e) {
      console.log(e);
      if (e.id) {
        commitInformations([e.id]).then((res) => {
          if (res.code == 200) {
            startInformationFlow().then((res2) => {
              if (res2.code == 200) {
                insertCommitInformation({ ...e, processId: res2.data }).then(
                  (res3) => {
                    if (res3.code == 200) {
                      this.$message.success("操作成功");
                      this.addItemType = false;
                      this.getList();
                    }
                  }
                );
              }
            });
          }
        });
      } else {
        startInformationFlow().then((res) => {
          if (res.code == 200) {
            insertCommitInformation({ ...e, processId: res.data }).then(
              (res) => {
                if (res.code == 200) {
                  this.$message.success("操作成功");
                  this.addItemType = false;
                  this.getList();
                }
              }
            );
          }
        });
      }
    },
    addItem(e) {
      console.log(e);
      if (e.id) {
        informationEdit({ ...e }).then((res) => {
          if (res.code == 200) {
            this.$message.success("修改成功");
            this.addItemType = false;
            this.getList();
          }
        });
      } else {
        information({ ...e }).then((res) => {
          if (res.code == 200) {
            this.$message.success("新建成功");
            this.addItemType = false;
            this.getList();
          }
        });
      }
    },
    edit(v) {
      this.seeType = false;
      this.editData = { ...v };
      this.addItemType = true;
    },
    del(v) {
      delsystemInformation(v.id).then((res) => {
        if (res.code == 200) {
          this.$message.success("删除成功");
          this.getList();
        }
      });
    },
    success() {
      this.moveItemType = false;
      this.multipleSelection = [];
      this.getList();
    },
    confirmSelect(v) {
      this.multipleSelection = [...v];
      this.tableData.forEach((item) => {
        item.acttype = false;
      });
      this.getArrEqual(this.tableData, this.multipleSelection);
      this.selectItemType = false;
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        informationName: "",
        informationCode: "",
        catalogueId: "",
        pageNum: 1,
        pageSize: 10,
        auditState: "",
      };
      this.getList();
    },
    dehandleNodeClick(data) {
      console.log(data);
      this.params.pertainDeptName = data.label;
      this.params.pertainDeptId = data.id;
      this.$refs.selectUpResId.blur();
    },
    addOneId(treeData, parentOneId) {
      treeData.forEach((node) => {
        node.oneId = node.parentId ? parentOneId : node.id;
        if (node.informationCatalogueVOList) {
          this.addOneId(node.informationCatalogueVOList, node.oneId);
        }
      });
    },
    handleNodeClick(data) {
      console.log(data);
      if (!this.currentOneId) {
        this.multipleSelection = [];
      }
      if (this.currentOneId && data.oneId != this.currentOneId) {
        this.multipleSelection = [];
      }
      this.currentOneId = data.oneId;
      this.params.catalogueId = data.id;
      this.getList();
    },
    getTreeList() {
      getTreeList().then((res) => {
        this.leftTreeList = res.data;
        this.addOneId(this.leftTreeList);
      });
      treeselect({ AuthModuleEnumCode: "JGINFORMATION" }).then((res) => {
        this.treeSelect = res.data;
      });
    },
    handleExport() {
      this.download(
        "/supervise/information/export",
        {
          ...this.params,
          ids: JSON.stringify(this.multipleSelection.map((item) => item.id)),
        },
        `资料下载审批列表.xlsx`
      );
    },
    getList() {
      let params = {
        ...this.params,
      };
      informationList({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.tableData.forEach((item, index) => {
            item.acttype = false;
            item.xh = (this.params.pageNum - 1) * 10 + index + 1;
            item.auditStateLabel = this.auditStateObj[item.auditState];
            item.saveStartTimeLabel = item.saveStartTime
              ? `${item.saveStartTime} - ${item.saveEndTime}`
              : "永久";
            item.informationMonthLabel = item.informationMonth
              ? item.informationMonth + "月"
              : "-";
          });
          this.total = res.total;
          if (this.multipleSelection.length > 0) {
            this.getArrEqual(this.tableData, this.multipleSelection);
          }
        }
      });
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].id === arr2[i].id) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    selectionChange(v, type) {
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.id == v.id) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.id == v.id) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.id == v.id) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].id == list[j].id) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].id == list[j].id) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.id] ? "" : (obj[next.id] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },
    toDetail(v) {
      getDetail(v.id).then((res) => {});
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.catalogueName.indexOf(value) !== -1;
    },
  },
};
</script>

  <style lang="less" scoped>
.content {
  display: flex;
  width: 100%;
  padding: 16px;
  .left {
    width: 250px;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #ccc;
    flex-shrink: 0;
    padding: 16px;
    box-sizing: border-box;
    ::v-deep .el-tree {
      &.has-data {
        display: inline-block;
      }
}
  }
  .right {
    width: calc(100% - 250px);
    padding-left: 12px;
    .el-button {
      height: 32px;
    }
  }
}
.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;
    span {
      margin-right: 9px;
    }
  }
}
.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}
.selsct {
  width: 14px;
  cursor: pointer;
}
/deep/ .el-select .el-input__inner {
  height: 36px;
}
</style>
