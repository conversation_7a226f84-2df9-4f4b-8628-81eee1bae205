<template>
  <div>
    <div class="search">
      <div class="item">
        <span>信息检索</span><el-tooltip class="item" effect="dark" content="包含界面的所有展示数据的内容，不包含资料文件或视频中的文字" placement="top-start"><i class="el-icon-question relative right-1 cursor-pointer"></i> </el-tooltip>
        <el-input
          v-model="queryParams.informationRetrieval"
          clearable=""
          placeholder="请输入关键字"
          style="width: 200px"
          @input="getList"
        ></el-input>
      </div>
      <div class="item">
        <span>所属公司</span>
        <el-select
          clearable=""
          placeholder="请选择所属公司"
          style="width: 250px"
          v-model="queryParams.unitId"
        >
          <el-option
            v-for="item in companyList"
            :key="item.id"
            :value="item.id"
            :label="item.companyShortName"
          ></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>合作公司</span>
        <el-input
          v-model="queryParams.cooperationCompanyName"
          clearable=""
          placeholder="请输入合作公司"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>合作项目</span>
        <el-input
          v-model="queryParams.cooperationProjectName"
          clearable=""
          placeholder="请输入合作项目"
          style="width: 200px"
        ></el-input>
      </div>

      <div class="item">
        <span>资料名称</span>
        <el-input
          clearable=""
          placeholder="请输入资料名称"
          style="width: 250px"
          v-model="queryParams.informationName"
        ></el-input>
      </div>
      <div class="item">
        <span>资料类型</span>
        <el-select
          clearable=""
          placeholder="请选择资料类型"
          style="width: 250px"
          v-model="queryParams.isTemporary"
        >
          <el-option label="临时上传资料" :value="0"></el-option>
          <el-option label="资料库" :value="1"></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>流程发起人</span>
        <el-input
          clearable=""
          placeholder="请输入流程发起人"
          style="width: 250px"
          v-model="queryParams.createBy"
        ></el-input>
      </div>
      <div class="item">
        <span>资料状态</span>
        <el-select
          v-model="queryParams.isAbandoned"
          placeholder="请选择资料状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.data_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </div>
      <div class="item">
        <span>流程发起时间</span>
        <el-date-picker
          v-model="time"
          @clear="clearTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
    </div>
    <div>
      <el-button type="primary" @click="search">搜 索</el-button>
      <el-button @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="mb-5 m-4">
      您当前正在使用<span style="color: rgba(217, 0, 27, 0.803921568627451)"
        >监管报送</span
      >资料发放统计功能
    </div>
    <div class="btn">
      <el-button
        v-hasPermi="['dataManagementSupervise:dataStatistics:export']"
        @click="handleExport"
        style="border-color: #ffc26c; background: #fff8e6; color: #ffc30d"
        icon="el-icon-download"
        size="mini"
        >导出列表</el-button
      >
      <el-button
        icon="el-icon-refresh"
        style="margin-right: 16px"
        @click="getList"
        >刷新</el-button
      >
    </div>
    <div style="padding: 0 16px">
      <virtual-scroll
        :data="virtualData"
        :item-size="48"
        key-prop="webId"
        @change="changeVirtualList"
        :virtualized="true"
      >
        <el-table
          border
          :data="tableData"
          ref="table"
          height="58vh"
          row-key="webId"
          :span-method="objectSpanMethod"
          :cell-style="{ padding: '0' }"
        >
          <el-table-column
            fixed="left"
            align="center"
            prop="orgName"
            label="所属公司"
            width="150px"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.orgName,
                    queryParams.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            min-width="350"
            label="资料/文件名称"
            prop="informationName"
          >
            <template #default="{ row }">
              <div
                v-if="row.informationSystemCode"
                v-html="
                  highlightKeyword(
                    row.informationName,
                    queryParams.informationRetrieval
                  )
                "
                size="mini"
                type="text"
                style="color: #1890ff; cursor: pointer"
                @click="see(row)"
              ></div>
              <div v-else>{{row.informationName}}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="isTemporary"
            width="120px"
            label="资料类型"
          >
            <template #default="{ row }">
              <div
                v-html="
                  highlightKeyword(
                    isTemporaryObj[row.isTemporary],
                    queryParams.informationRetrieval
                  )
                "
              ></div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="isAbandoned"
            width="100px"
            label="资料状态"
          >
            <template #default="{ row }">
              <div
                v-html="
                  highlightKeyword(
                    dict.label.data_status[row.isAbandoned],
                    queryParams.informationRetrieval
                  )
                "
              ></div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="250"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    queryParams.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProject"
                v-html="
                  highlightKeyword(
                    row.cooperationProjectName,
                    queryParams.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="type"
            label="资料流程"
            width="140"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.processType,
                    queryParams.informationRetrieval
                  )
                "
              ></span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            prop="provideDate"
            width="160px"
            label="流程发起时间"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.provideDate,
                    queryParams.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            prop="createBy"
            width="100px"
            label="流程发起人"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.createBy,
                    queryParams.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            prop="isUsed"
            width="100px"
            label="用印状态"
          >
            <template #default="{ row }">
              <div
                v-html="
                  highlightKeyword(
                    isUsedObj[row.isUsed],
                    queryParams.informationRetrieval
                  )
                "
              ></div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="remark"
            min-width="250px"
            label="备注"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.remark, queryParams.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="120px"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="mini"
                type="text"
                @click="seePro(row)"
                v-if="row.processId"
                >查看流程</el-button
              >
              <el-button size="mini" type="text" @click="handleDownload(row)"
                >下载</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </virtual-scroll>
    </div>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      @close="addItemType = false"
    />
  </div>
</template>

<script>
import VirtualScroll, { VirtualColumn } from "el-table-virtual-scroll";
import { downloadByUrl } from "@/api/oa/processTemplate";
import { statisticsList ,newAuthorityCompany} from "@/api/directoryMation/directoryMationSupervise";
import addItem from "../inputData/addItem.vue";
import { highlightKeyword } from "@/utils/index.js";

export default {
  components: {
    addItem,
    VirtualScroll: VirtualScroll,
    VirtualColumn: VirtualColumn,
  },
  dicts: ["data_status"],
  data() {
    return {
      seeType: false,
      addItemType: false,
      companyList: [],
      time: [],
      virtualData: [], // 真正的全部的数据
      tableData: [],
      queryParams: {
        isTemporary: "",
        unitId: "",
        informationName: "",
        informationRetrieval: "",
        user: "",
        createBy: "",
        isAbandoned: "",
        startTime: "",
        endTime: "",
      },
      editData: null,
      highlightKeyword,
      isTemporaryObj: Object.freeze({ 0: "临时上传资料", 1: "资料库" }),
      isUsedObj: Object.freeze({ 0: "已用印", 1: "可用印", 2: "不可用印" }),
    };
  },
  mounted() {
    this.allCompanyList();
    this.getList();
  },
  methods: {
    clearTime() {
      this.time = [];
    },
    handleExport() {
      this.download(
        "/supervise/information/exportReleaseStatistics",
        {
          ...this.params,
        },
        `资料发放统计.xlsx`
      );
    },
    handleDownload(file) {
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName;
      }
      const url = file.url || file.fileUrl; //图片的https链接
      downloadByUrl({
        url: url,
      }).then((res) => {
        let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
        const link = document.createElement("a"); //创建一个隐藏的a标签
        link.target = "_blank";
        link.href = href; //设置下载的url
        link.download = file.name; //设置下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(href); // 释放掉blob对象
      });
    },
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
    listLength(list) {
      let length = 0;
      list.forEach((item) => {
        if (item.subjectType == type) {
          length++;
        }
      });
      console.log(length);
      return length;
    },
    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        isTemporary: "",
        unitId: "",
        informationName: "",
        user: "",
        createBy: "",
        isAbandoned: "",
        startTime: "",
        endTime: "",
      };
      this.time = [];
      this.getList();
    },
    getList() {
      console.log(this.queryParams);
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.queryParams.endTime = this.$format(this.time[1], "yyyy-MM-dd");
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      statisticsList({ ...this.queryParams }).then((res) => {
        if (res.code == 200) {
          this.virtualData = res.rows.map((item, index) => {
            item.webId = index;
            item.processType= {2:'资料用印',3:"资料下载"}[item.processType]
            return item;
          });
        }
      });
    },
    changeVirtualList(virtualList) {
      requestAnimationFrame(() => {
        this.tableData = virtualList;
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let data = this.tableData; //拿到当前table中数据
      let cellValue = row[column.property]; //当前位置的值
      let SortArr = ["orgName"];
      if (cellValue && SortArr.includes(column.property)) {
        let prevRow = data[rowIndex - 1]; //获取到上一条数据
        let nextRow = data[rowIndex + 1]; //下一条数据
        if (prevRow && prevRow[column.property] === cellValue) {
          //当有上一条数据，并且和当前值相等时
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow[column.property] === cellValue) {
            //当有下一条数据并且和当前值相等时,获取新的下一条
            nextRow = data[++countRowspan + rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
      let SortArrId = ["informationName"];
      if (cellValue && SortArrId.includes(column.property)) {
        let prevRow = data[rowIndex - 1]; //获取到上一条数据
        let nextRow = data[rowIndex + 1]; //下一条数据
        if (prevRow && prevRow["id"] === row["id"]) {
          //当有上一条数据，并且informationId和当前值相等时
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow["id"] === row["id"]) {
            //当有下一条数据并且informationId和当前值相等时,获取新的下一条
            nextRow = data[++countRowspan + rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
      let SortArrOther = ["isAbandoned", "isTemporary"];
      if (cellValue && SortArrOther.includes(column.property)) {
        let prevRow = data[rowIndex - 1]; //获取到上一条数据
        let nextRow = data[rowIndex + 1]; //下一条数据
        if (
          prevRow &&
          prevRow[column.property] === cellValue &&
          prevRow["id"] === row["id"]
        ) {
          //当有上一条数据，并且和当前值相等时并且informationId和当前值相等时
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (
            nextRow &&
            nextRow[column.property] === cellValue &&
            nextRow["id"] === row["id"]
          ) {
            //当有下一条数据并且和当前值相等时并且informationId和当前值相等时,获取新的下一条
            nextRow = data[++countRowspan + rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    allCompanyList() {
      newAuthorityCompany({AuthModuleEnumCode:"JGINFORMATION"}).then((res) => {
        this.companyList = res.rows;
      });
    },
    seePro(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: v.processId,
          businessId: v.processId,
          myActiviteType: true,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.btn {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
}
.solid {
  width: 100%;
  height: 10px;
  background: #f8f8f9;
}
.search {
  padding: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    margin-right: 16px;
    display: flex;
    align-items: center;
  }
  span {
    margin-right: 9px;
  }
}
/deep/ .el-select __inner {
  height: 36px !important;
}
.el-button {
  height: 36px;
  margin-left: 16px;
}
</style>
