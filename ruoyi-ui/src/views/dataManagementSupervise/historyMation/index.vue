<template>
  <div>
    <div style="padding: 16px" class="header">
      <div>
        <span>信息检索</span><el-tooltip class="item" effect="dark" content="包含界面的所有展示数据的内容，不包含资料文件或视频中的文字" placement="top-start"><i class="el-icon-question relative right-1 cursor-pointer"></i> </el-tooltip>
        <el-input
          v-model="params.informationRetrieval"
          clearable=""
          placeholder="请输入关键字"
          style="width: 200px"
          @input="getList"
        ></el-input>
      </div>
      <div>
        <span>资料名称</span>
        <el-input
          v-model="params.informationName"
          clearable=""
          placeholder="请输入资料名称"
          style="width: 200px"
        ></el-input>
      </div>
      <div>
        <span>资料编号</span>
        <el-input
          v-model="params.informationCode"
          clearable=""
          placeholder="请输入资料编号"
          style="width: 200px"
        ></el-input>
      </div>
      <el-button type="primary" icon="el-icon-search" @click="search"
        >搜 索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <div class="mb-5">
        您当前正在使用<span style="color: rgba(217, 0, 27, 0.803921568627451)"
          >监管报送</span
        >历史信息记录
      </div>

      <!-- <el-tab-pane label="授权记录" name="first">
        <el-table
          v-if="activeName == 'first'"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column prop="xh" label="序号" width="60" />
          <el-table-column prop="informationCode" label="资料编码" />
          <el-table-column
            prop="informationName"
            label="资料/文件名称"
            min-width="220"
            align="center"
          >
            <template slot-scope="scope">
              <div
                style="color: #46a6ff; cursor: pointer"
                @click="see(scope.row)"
                v-html="
                  highlightKeyword(
                    scope.row.informationName,
                    params.informationRetrieval
                  )
                "
              ></div>
            </template>
          </el-table-column>
          <el-table-column prop="informationYear" label="资料年度">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationYear,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="catalogueName" label="目录"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.catalogueName,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="authorityState" label="授权状态"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.authorityState,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="informationSystemCode" label="系统资料编号" width="150px"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationSystemCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
           <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="140"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="140"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProjectName"
                v-html="
                  highlightKeyword(
                    row.cooperationProject,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span> </template
          ></el-table-column>
          <el-table-column prop="impowerName" label="授权人" width="150"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.impowerName, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="authorityTypeName" label="授权类型"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.authorityTypeName,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="impowerTime" label="授权日期" width="200"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.impowerTime, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column label="授权详情">
            <template slot-scope="scope">
              <el-button type="text" @click="see(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane> -->
      <el-tab-pane label="过期资料" name="second" v-if="checkPermi(['dataManagementSupervise:historyMation:overdue'])">
        <el-table
          v-if="activeName == 'second'"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column prop="xh" label="序号" width="60" />
          <el-table-column prop="informationCode" label="资料编码" />
          <el-table-column
            prop="informationName"
            label="资料/文件名称"
            min-width="350"
            align="center"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.informationSystemCode"
                v-html="
                  highlightKeyword(
                    scope.row.informationName,
                    params.informationRetrieval
                  )
                "
                style="color: #46a6ff; cursor: pointer"
                @click="see(scope.row)"
              ></div>
              <div v-else>{{scope.row.informationName}}</div>
            </template>
          </el-table-column>

          <el-table-column prop="informationYear" label="资料年度"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationYear,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="catalogueName" label="目录"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.catalogueName,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="date" label="保管状态">
            <template #default="{}">
              <span
                v-html="highlightKeyword('已过期', params.informationRetrieval)"
              ></span> </template
          ></el-table-column>
                   <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="250"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProject"
                v-html="
                  highlightKeyword(
                    row.cooperationProjectName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span> </template
          ></el-table-column>
          <el-table-column prop="informationSystemCode" label="系统资料编号" width="200">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationSystemCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="date" label="保管期限" width="200">
            <template slot-scope="scope">
              <span
                v-if="scope.row.saveStartTime"
                v-html="
                  highlightKeyword(
                    `${scope.row.saveStartTime} - ${scope.row.saveEndTime}`,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>永久</span>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="创建人">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.createBy, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="editTime(scope.row)"
                >延长期限</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="审核记录" name="third" v-if="checkPermi(['dataManagementSupervise:historyMation:examine'])">
        <el-table
          v-if="activeName == 'third'"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column prop="xh" label="序号" width="60" />
          <el-table-column prop="informationCode" label="资料编码">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            prop="informationName"
            label="资料/文件名称"
            min-width="350"
            align="center"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.informationSystemCode"
                v-html="
                  highlightKeyword(
                    scope.row.informationName,
                    params.informationRetrieval
                  )
                "
                style="color: #46a6ff; cursor: pointer"
                @click="see(scope.row)"
              ></div>
              <div v-else>{{scope.row.informationName}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="informationYear" label="资料年度"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationYear,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="catalogueName" label="目录"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.catalogueName,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="date" label="审核状态">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.auditStateLabel,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
                   <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="250"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProject"
                v-html="
                  highlightKeyword(
                    row.cooperationProjectName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span> </template
          ></el-table-column>
          <el-table-column prop="informationSystemCode" label="系统资料编号" width="200">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationSystemCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="date" label="保管期限">
            <template slot-scope="scope">
              <span
                v-if="scope.row.saveStartTime"
                v-html="
                  highlightKeyword(
                    `${scope.row.saveStartTime} - ${scope.row.saveEndTime}`,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>永久</span>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="发起人" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="seeProcess(scope.row)"
                >查看流程</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="废弃资料" name="four" v-if="checkPermi(['dataManagementSupervise:historyMation:abandoned'])">
        <el-table
          v-if="activeName == 'four'"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column prop="xh" label="序号" width="60" />
          <el-table-column prop="informationCode" label="资料编码">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            prop="informationName"
            label="资料/文件名称"
            min-width="350"
            align="center"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.informationSystemCode"
                v-html="
                  highlightKeyword(
                    scope.row.informationName,
                    params.informationRetrieval
                  )
                "
                style="color: #46a6ff; cursor: pointer"
                @click="see(scope.row)"
              ></div>
              <div v-else>{{scope.row.informationName}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="informationYear" label="资料年度">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationYear,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>

          <el-table-column prop="date" label="审核状态">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.auditStateLabel,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="informationSystemCode" label="系统资料编号" width="200">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.informationSystemCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
                   <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="250"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProject"
                v-html="
                  highlightKeyword(
                    row.cooperationProjectName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span> </template
          ></el-table-column>
          <el-table-column prop="createBy" label="创建人" width="200">
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.createBy, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column prop="createTime" label="上传时间" width="200" />
          <el-table-column prop="date" label="保管期限">
            <template slot-scope="scope">
              <span
                v-if="scope.row.saveStartTime"
                v-html="
                  highlightKeyword(
                    `${scope.row.saveStartTime} - ${scope.row.saveEndTime}`,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-else>永久</span>
            </template>
          </el-table-column>

          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="dataRecovery(scope.row)"
                >资料恢复</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="params.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="params.pageSize"
      @pagination="getList"
    />
    <addItem
      :editData="editData"
      :editTimeType="editTimeType"
      @save="addItem"
      :seeType="seeType"
      :uploadType="true"
      v-if="addItemType"
      @close="addItemType = false"
    />
  </div>
</template>

<script>
import addItem from "../inputData/addItem.vue";
import {
  getAuthorizationRecord,
  getExpireList,
  informationEdit,
  allList,
  abandonedList,
  unabandonedInformation,
} from "@/api/directoryMation/directoryMationSupervise";
import { highlightKeyword } from "@/utils/index.js";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  components: {
    addItem,
  },
  data() {
    return {
      editData: null,
      seeType: false,
      editTimeType: false,
      addItemType: false,
      total: 0,
      activeName: "",
      params: {
        pageSize: 10,
        pageNum: 1,
        informationRetrieval: "",
        informationCode: "",
        informationName: "",
      },
      tableData: [],
      highlightKeyword,
      auditStateObj: Object.freeze({
        1: "审核通过",
        2: "未审核",
        3: "审核不通过",
        4: "审核中",
      }),
      checkPermi,
    };
  },
  mounted() {
    this.getActiveName();
    this.getList();
  },
  methods: {
    getActiveName(){
      if(checkPermi(["dataManagementSupervise:historyMation:overdue"])){
        this.activeName='second';
        return;
      }
      if(checkPermi(["dataManagementSupervise:historyMation:examine"])){
        this.activeName='third';
        return;
      }
      if(checkPermi(["dataManagementSupervise:historyMation:abandoned"])){
        this.activeName='four';
        return;
      }
    },
    addItem(e) {
      informationEdit({ ...e }).then((res) => {
        if (res.code == 200) {
          this.$message.success("修改成功");
          this.addItemType = false;
          this.getList();
        }
      });
    },
    editTime(v) {
      this.editData = { ...v };
      this.seeType = true;
      this.editTimeType = true;
      this.addItemType = true;
    },
    seeProcess(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: v.processId,
          businessId: v.processId,
          myActiviteType: true,
        },
      });
    },
    see(v) {
      this.editData = { ...v };
      this.editTimeType = false;
      this.seeType = true;
      this.addItemType = true;
    },
    getList() {
      if (this.activeName == "first") {
        getAuthorizationRecord({ ...this.params }).then((res) => {
          if (res.code == 200) {
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item.xh = (this.params.pageNum - 1) * 10 + index + 1;
            });
            this.total = res.total;
          }
        });
      } else if (this.activeName == "second") {
        getExpireList({ ...this.params }).then((res) => {
          if (res.code == 200) {
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item.xh = (this.params.pageNum - 1) * 10 + index + 1;
            });
            this.total = res.total;
          }
        });
      } else if (this.activeName == "third") {
        allList({ ...this.params, submitState: 1 }).then((res) => {
          if (res.code == 200) {
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item.xh = (this.params.pageNum - 1) * 10 + index + 1;
              item.auditStateLabel = this.auditStateObj[item.auditState];
            });
            this.total = res.total;
          }
        });
      } else if (this.activeName == "four") {
        abandonedList({ ...this.params }).then((res) => {
          if (res.code == 200) {
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item.xh = (this.params.pageNum - 1) * 10 + index + 1;
              item.auditStateLabel = this.auditStateObj[item.auditState];
            });
            this.total = res.total;
          }
        });
      }
    },
    dataRecovery(row) {
      this.$confirm(`确认恢复【${row.informationName}】吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = [row.id];
          unabandonedInformation(params).then(async (res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消恢复",
          });
        });
    },
    handleClick() {
      this.getList();
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        pageSize: 10,
        pageNum: 1,
        informationCode: "",
        informationName: "",
      };
      this.getList();
    },
  },
};
</script>

<style lang="less" scoped>
.header {
  display: flex;
  div {
    margin-right: 16px;
    span {
      margin-right: 9px;
    }
  }
}
/deep/ .el-tabs__nav-scroll {
  padding-left: 16px;
}
/deep/ .el-tabs__content {
  padding: 16px !important;
}
</style>
