<template>
  <div>
    <el-dialog
      :title="'已选择' + tableData.length + '条'"
      :visible.sync="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <el-table
        :data="tableData"
        style="width: 100%; margin-top: 16px; margin-left: 4px"
      >
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="informationCode"
          label="资料编号"
          width="180"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="informationName"
          label="资料名称"
          width="350"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="see(scope.row)">{{
              scope.row.informationName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="informationYear"
          label="资料年度"
          width="120"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="catalogueName"
          label="目录"
          width="220"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="date"
          label="状态"
          width="140"
        >
          <template slot-scope="scope">
            {{
              scope.row.auditState == 1
                ? "审核通过"
                : scope.row.auditState == 2
                ? "未审核"
                : scope.row.auditState == 3
                ? "审核不通过"
                : "审核中"
            }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="informationSystemCode"
          label="系统资料编号"
          width="140"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="pertainDeptName"
          label="保管期限"
          width="120"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.saveStartTime"
              >{{ scope.row.saveStartTime }} - {{ scope.row.saveEndTime }}</span
            >
            <span v-else>永久</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="createBy"
          label="创建人"
          width="120"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="createTime"
          label="创建时间"
          width="150"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button @click="del(scope.row)" type="text" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      @close="addItemType = false"
    />
  </div>
</template>

<script>
import addItem from "./addItem.vue";

export default {
  props: {
    multipleSelection: Array,
  },
  components: {
    addItem,
  },
  data() {
    return {
      dialogVisible: true,
      tableData: [],
      seeType: false,
      editData: null,
      addItemType: false,
    };
  },
  mounted() {
    this.tableData = JSON.parse(JSON.stringify(this.multipleSelection));
    console.log(this.tableData);
  },
  methods: {
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
    submit() {
      this.$emit("confirm", this.tableData);
    },
    del(v) {
      this.tableData.map((item, index) => {
        if (item.id == v.id) {
          this.tableData.splice(index, 1);
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>