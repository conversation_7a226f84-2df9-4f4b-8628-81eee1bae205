<template>
  <div>
    <el-dialog
      :title="
        editData && editData.id && !seeType
          ? '修改监管报送资料'
          : !editData && !seeType
          ? '新增监管报送资料'
          : '查看资料详情'
      "
      :visible.sync="dialogVisible"
      width="750px"
      :before-close="handleClose"
    >
      <div class="flex">
        <div class="item">
          <span><i>*</i>资料名称</span>
          <el-input
            type="text"
            :disabled="seeType"
            v-model="params.informationName"
            placeholder="请输入资料名称"
            style="width: 220px"
          ></el-input>
        </div>
        <div class="item">
          <span>系统资料编号</span>
          <el-input
            type="text"
            v-model="params.informationSystemCode"
            disabled
            placeholder="保存后自动生成"
            style="width: 220px"
          ></el-input>
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span><i>*</i>目录</span>
          <el-select
            v-model="params.catalogueId"
            clearable
            @clear="delml"
            :disabled="seeType"
            style="width: 220px"
            placeholder="请选择目录"
            ref="selectUpResId1"
          >
            <el-option
              hidden
              :value="params.catalogueId"
              :label="params.catalogueName"
            >
            </el-option>
            <el-tree
              :data="deTreeList"
              :props="defaultProps"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </el-select>
        </div>
        <div class="item">
          <span>资料编号</span>
          <el-input
            type="text"
            :disabled="seeType"
            @keyup.native="btKeyUp"
            v-model="params.informationCode"
            placeholder="资料编号"
            style="width: 220px"
          ></el-input>
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span><i>*</i>资料年度</span>
          <el-date-picker
            :disabled="seeType"
            style="width: 220px"
            v-model="params.informationYear"
            type="year"
            placeholder="选择年"
          >
          </el-date-picker>
        </div>
        <div class="item">
          <span>资料所属月份</span>
          <el-select
            :disabled="seeType"
            style="width: 220px"
            v-model="params.informationMonth"
            type="year"
            placeholder="请输入/选择资料所属月份"
          >
            <el-option label="1月" value="1"></el-option>
            <el-option label="2月" value="3"></el-option>
            <el-option label="3月" value="2"></el-option>
            <el-option label="4月" value="4"></el-option>
            <el-option label="5月" value="5"></el-option>
            <el-option label="6月" value="6"></el-option>
            <el-option label="7月" value="7"></el-option>
            <el-option label="8月" value="8"></el-option>
            <el-option label="9月" value="9"></el-option>
            <el-option label="10月" value="10"></el-option>
            <el-option label="11月" value="11"></el-option>
            <el-option label="12月" value="12"></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span>合作公司</span>
          <el-select
            type="text"
            v-model="params.cooperationCompany"
            :disabled="seeType"
            placeholder="请输入合作公司"
            style="width: 220px"
            filterable
            clearable
          >
            <el-option
              :value="item.id"
              :label="item.companyName"
              v-for="(item, index) in cooperationCompanyList"
              :key="index"
            >
            </el-option>
          </el-select>
        </div>
        <div class="item">
          <span>合作项目</span>
          <el-select
            :disabled="seeType"
            v-model="params.cooperationProject"
            placeholder="请输入合作项目"
            style="width: 220px"
            filterable
            clearable
          >
            <el-option
              :value="item.id"
              :label="item.projectName"
              v-for="(item, index) in cooperationProjectList"
              :key="index"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="flex">
        <div class="item" style="position: relative">
          <span><i>*</i>是否永久保存</span>
          <el-select
            type="text"
            :disabled="seeType"
            v-model="params.saveFlag"
            @change="changeFlag"
            placeholder="请选择"
            style="width: 220px"
          >
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </div>
        <div class="item">
          <span>保管期限</span>
          <el-date-picker
            @change="change"
            v-model="time"
            :disabled="(params.saveFlag == 1 || seeType) && !editTimeType"
            type="daterange"
            style="width: 220px"
            range-separator="至"
            :picker-options="pickerOptions"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="flex">
        <div
          class="item"
          style="position: relative; display: flex; align-items: center"
        >
          <span><i>*</i>附件上传</span>
          <el-upload
            :disabled="seeType"
            :headers="upload.headers"
            :action="upload.url"
            :on-success="handleFileSuccess"
            :show-file-list="false"
            :before-upload="beforeUpload"
          >
            <el-button :disabled="seeType"
              ><i class="el-icon-upload2"></i>上传附件</el-button
            >
          </el-upload>
          <i
            v-if="uploadType"
            style="margin-left: 12px; color: #409eff; cursor: pointer"
            @click="handleDownload(params)"
            >{{ params.fileName }}</i
          >
          <i v-else style="margin-left: 12px">{{ params.fileName }}</i>
          <i
            v-show="
              params.fileName.endsWith('.pdf') ||
              params.fileName.endsWith('.jpg') ||
              params.fileName.endsWith('.png') ||
              params.fileName.endsWith('.gif') ||
              params.fileName.endsWith('.jpeg')
            "
            style="margin-left: 12px; color: #409eff; cursor: pointer"
            @click="handlePreview(params)"
            >预览</i
          >
        </div>
      </div>
      <div class="flex">
        <div class="item" style="display: flex">
          <span style="flex-shrink: 0">备注</span>
          <el-input
            type="textarea"
            :disabled="seeType"
            :rows="2"
            style="width: 590px"
            placeholder="请输入内容"
            v-model="params.remark"
          >
          </el-input>
        </div>
        <el-image
          ref="previewImg"
          v-show="false"
          :src="photoUrl"
          :preview-src-list="imagePreviewUrls"
        />
      </div>
      <!-- <el-table v-if="seeType" border :data="tableData" style="width: 100%">
        <el-table-column
          align="center"
          width="120"
          prop="authorityTypeName"
          label=""
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="authorityDetails"
          label="已授权内容"
        >
        </el-table-column>
        <el-table-column align="center" prop="address" label="授权期限">
          <template slot-scope="scope">
            {{ scope.row.startTime }} - {{ scope.row.endTime }}
          </template>
        </el-table-column>
      </el-table> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" v-if="!seeType" @click="save"
          >保 存</el-button
        >
        <el-button type="primary" v-if="editTimeType" @click="save"
          >保 存</el-button
        >
        <!-- <el-button type="primary" v-if="!seeType" @click="submit"
          >提 交</el-button
        > -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAuthorityToDet } from "@/api/directoryMation/directoryMationSupervise";
import { getToken } from "@/utils/auth";
import { downloadByUrl } from "@/api/oa/processTemplate";
import privew from "@/mixin/privew";
export default {
  mixins: [privew],
  props: {
    deTreeList: Array,
    cooperationCompanyList: Array,
    cooperationProjectList: Array,
    editData: Object,
    catalogueName: String,
    catalogueId: Number,
    seeType: Boolean,
    editTimeType: Boolean,
    uploadType: Boolean,
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      tableData: [],
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/information/uploadFile",
        //当前步骤id
      },
      time: [],
      dialogVisible: true,
      params: {
        catalogueId: "",
        catalogueName: "",
        remark: "",
        cooperationCompany: "",
        cooperationProject: "",
        informationName: "",
        informationYear: "",
        informationCode: "",
        saveFlag: "",
        saveStartTime: "",
        saveEndTime: "",
        fileUrl: "",
        fileName: "",
      },
      defaultProps: {
        children: "informationCatalogueVOList",
        label: "catalogueName",
      },
    };
  },
  mounted() {
    if (this.catalogueId) {
      console.log(this.catalogueId);
      this.params.catalogueId = this.catalogueId;
      this.params.catalogueName = this.catalogueName;
    }
    if (this.editData?.id || this.editData?.informationId) {
      this.params = this.editData;
      if (this.params.saveStartTime) {
        this.time = [this.params.saveStartTime, this.params.saveEndTime];
      }
    }
    // if (this.seeType) {
    //   let params = {
    //     billType: 1,
    //     billId: this.editData.id || this.editData.informationId,
    //   };
    //   getAuthorityToDet({ ...params }).then((res) => {
    //     if (res.code == 200) {
    //       this.tableData = res.rows;
    //     }
    //   });
    // }
  },
  methods: {
    handleDownload(file) {
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName;
      }
      const url = file.url || file.fileUrl; //图片的https链接
      downloadByUrl({
        url: url,
      }).then((res) => {
        let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
        const link = document.createElement("a"); //创建一个隐藏的a标签
        link.target = "_blank";
        link.href = href; //设置下载的url
        link.download = file.name; //设置下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(href); // 释放掉blob对象
      });
    },
    delml() {
      this.params.catalogueId = "";
      this.params.catalogueName = "";
    },
    btKeyUp(e) {
      //没有显示‘破折号’和 '.' 需要的可以手动加上
      this.params.informationCode = e.target.value.replace(
        /[\u4e00-\u9fa5/\s+/]/g,
        ""
      );
      console.log(this.form.wechat_num);
    },
    submit() {
      if (this.params.saveFlag !== "0" && this.params.saveFlag !== "1") {
        this.$message.warning("请选择是否永久保存");
        return;
      }
      if (this.params.saveFlag === "0" && this.time.length == 0) {
        this.$message.warning("请选择保管期限");
        return;
      }
      if (!this.params.informationName) {
        this.$message.warning("请输入资料名称");
        return;
      }
      if (!this.params.catalogueId) {
        this.$message.warning("请选择目录");
        return;
      }
      if (!this.params.informationYear) {
        this.$message.warning("请选择资料年度");
        return;
      }
      if (!this.params.fileUrl) {
        this.$message.warning("请上传附件");
        return;
      }
      if (this.time.length > 0) {
        this.params.saveStartTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.params.saveEndTime = this.$format(this.time[1], "yyyy-MM-dd");
      }
      this.params.informationYear = this.$format(
        this.params.informationYear,
        "yyyy"
      );
      this.$emit("submit", this.params);
    },
    changeFlag(e) {
      console.log(e);
      if (e) {
        this.time = [];
        this.params.saveStartTime = "";
        this.params.saveEndTime = "";
      }
    },
    change(value) {
      this.params.saveFlag = value ? "0" : "1";
    },
    save() {
      if (this.params.saveFlag !== "0" && this.params.saveFlag !== "1") {
        this.$message.warning("请选择是否永久保存");
        return;
      }
      if (this.params.saveFlag === "0" && this.time.length == 0) {
        this.$message.warning("请选择保管期限");
        return;
      }
      if (!this.params.informationName) {
        this.$message.warning("请输入资料名称");
        return;
      }
      if (!this.params.catalogueId) {
        this.$message.warning("请选择目录");
        return;
      }
      if (!this.params.informationYear) {
        this.$message.warning("请选择资料年度");
        return;
      }
      if (!this.params.fileUrl) {
        this.$message.warning("请上传附件");
        return;
      }
      if (this.time.length > 0) {
        this.params.saveStartTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.params.saveEndTime = this.$format(this.time[1], "yyyy-MM-dd");
      }
      this.params.informationYear = this.$format(
        this.params.informationYear,
        "yyyy"
      );
      this.$emit("save", this.params);
    },
    handleNodeClick(data) {
      console.log(data);
      this.params.unitId = data.unitId;
      this.params.catalogueId = data.id;
      this.params.catalogueName = data.catalogueName;
      this.params.orgName = data.orgName;
      this.$refs.selectUpResId1.blur();
    },
    handleClose() {
      this.$emit("close");
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      console.log(response, file, fileList);
      this.params.fileName = response.name;
      this.params.fileUrl = response.url;
    },
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error("上传文件大小不能为 0 MB");
        return false;
      } else if (fileSize < maxSize) {
        this.uploadData = {}; //上传携带的参数名
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  span {
    margin-right: 9px;
    display: inline-block;
    width: 100px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
</style>
