<template>
  <div>
    <el-dialog
      title="资料用印"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="btns">
        <span style="margin-right: 12px"
          >已选择({{ selectList.length }})条</span
        >
        <el-button type="primary" @click="send">资料用印</el-button>
      </div>
      <el-table
        @selection-change="handleSelectionChange"
        :data="tableData"
        ref="table"
        style="
          width: 100%;
          margin-top: 16px;
          border: 1px solid #e6e6e6;
          border-bottom: none;
        "
      >
        <el-table-column
          type="selection"
          align="center"
          :selectable="selectable"
          width="55"
        >
        </el-table-column>
        <el-table-column width="80" align="center" label="序号">
          <template slot-scope="scope">
            {{ scope.row.xh }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="informationCode"
          width="140"
          label="资料编号"
        />
        <el-table-column
          align="center"
          width="350"
          prop="informationName"
          label="资料名称"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.isTemporary != 0"
              type="text"
              @click="see(scope.row)"
              >{{ scope.row.informationName }}</el-button
            >
            <span v-else>{{ scope.row.informationName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="140"
          prop="informationYear"
          label="资料年度"
        />
        <el-table-column align="center" width="140" prop="" label="资料类型">
          <template slot-scope="scope">
            {{ scope.row.isTemporary == 0 ? "临时文件" : "资料库" }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="140"
          prop="informationSystemCode"
          label="系统资料编号"
        />
        <el-table-column
          align="center"
          width="140"
          prop="createBy"
          label="资料创建人"
        />
        <el-table-column align="center" prop="" label="用印状态">
          <template slot-scope="scope">
            {{
              scope.row.isUsed == 1
                ? "未用印"
                : scope.row.isUsed == 2
                ? "不可用印"
                : "已用印"
            }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="140"
          prop=""
          label="资料下载审核时间"
        >
          <template>
            {{ itemData.auditTime }}
          </template>
        </el-table-column>
        <el-table-column
          prop="date"
          label="资料下载"
          fixed="right"
          show-overflow-tooltip=""
          min-width="300"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleDownload(scope.row)">{{
              scope.row.fileName
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer"> </span>
    </el-dialog>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      :deTreeList="leftTreeList"
      @close="addItemType = false"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="selectCompanyType = fasle"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import SelectCompany from "../../../components/SelectCompany/index.vue";

import addItem from "../inputData/addItem.vue";
import { InfomationListSupervise, downloadByUrl } from "@/api/oa/processTemplate";
import {
  startInformationUseFlow,
  batchInformationUseProcess,
  getInformationUsedFlow,
} from "@/api/directoryMation/directoryMationSupervise";
export default {
  components: {
    addItem,
    SelectCompany,
  },
  props: {
    itemData: Object,
    usedCompanyId: [Number,String],
  },
  data() {
    return {
      selectCompanyType: false,
      dialogVisible: true,
      num: 0,
      tableData: [],
      selectList: [],
      editData: null,
      seeType: false,
      addItemType: false,
    };
  },
  mounted() {
    InfomationListSupervise({ processId: this.itemData.processId }).then((res) => {
      if (res.code == 200) {
        this.tableData = res.rows;
        this.tableData.forEach((item, index) => {
          item.xh = index + 1;
          item.user=this.itemData.user
        });
      }
    });
  },
  methods: {
    selectable(row, index) {
      if (row.isUsed != 1) {
        return false;
      } else {
        return true;
      }
    },
    handleDownload(file) {
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName;
      }
      const url = file.url || file.fileUrl; //图片的https链接
      downloadByUrl({
        url: url,
      }).then((res) => {
        let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
        const link = document.createElement("a"); //创建一个隐藏的a标签
        link.target = "_blank";
        link.href = href; //设置下载的url
        link.download = file.name; //设置下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(href); // 释放掉blob对象
      });
    },

    see(v) {
      this.editData = { ...v };
      this.seeType = true;
      this.addItemType = true;
    },
    submitCompany(e) {
      getInformationUsedFlow({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          sessionStorage.setItem("dataListSupervise", JSON.stringify(this.selectList));
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              themeTypeYySupervise: true,
            },
          });
        }
      });
    },
    send() {
      if (this.selectList.length == 0) {
        this.selectCompanyType = true;
        // this.submitCompany(this.tableData[0]);
        return;
      }
      let arr = [];
      this.selectList.forEach((item) => {
        if (item.isUsed == 0) {
          arr.push(item);
        }
      });
      if (arr.length > 0) {
        let data = arr.map((item) => item.informationName);
        this.$message.warning(`${data}已用印不可提交`);
        return;
      }
      let names = this.selectList.map(
        (item) => "【" + item.informationName + "】"
      );
      console.log(this.itemData);
      this.$confirm(`请您确定${names}是否发起用印`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(this.usedCompanyId);
          console.log(this.tableData[0]);
          const params=this.usedCompanyId?{
            unitId:this.usedCompanyId
          }:this.tableData[0]
          // this.submitCompany(params);
          this.selectCompanyType = true;
          // startInformationUseFlow(
          //   `${this.itemData.user}${this.$format(
          //     new Date().getTime(),
          //     "yyyy-MM-dd"
          //   )}提供资料`
          // ).then((res) => {
          //   if (res.code == 200) {
          //     let list = this.selectList.map((item) => item.id);
          //     batchInformationUseProcess({
          //       processId: res.data,
          //       infoIds: list,
          //     }).then((res) => {
          //       if (res.code == 200) {
          //         this.$message.success("操作成功");
          //         this.handleClose();
          //       }
          //     });
          //   }
          // });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleClose() {
      this.$emit("close");
    },
    confirm() {},
    handleSelectionChange(e) {
      console.log(e);
      this.selectList = [...e];
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-dialog__body {
  padding: 4px 16px !important;
}
</style>
