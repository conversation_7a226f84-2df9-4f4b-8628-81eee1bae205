<template>
  <el-dialog
    title="部门权限"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
  >
    <el-checkbox
      style="margin: 0 0 12px 24px"
      @change="change"
      v-model="checked"
      >全选</el-checkbox
    >
    <el-tree
      :data="treeData"
      show-checkbox
      node-key="id"
      :props="defaultProps"
      ref="tree"
      @check="handleCheckChange"
    ></el-tree>
    <div
      v-if="type"
      style="display: flex; align-items: center; margin-top: 16px"
    >
      <span style="margin-right: 12px"
        >授权期限</span
      >
      <el-date-picker
        v-model="time"
        style="width: 300px"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      >
      </el-date-picker>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  treeselect,
  informationCatalogueList,
} from "@/api/directoryMation/directoryMationSupervise";
export default {
  props: {
    multipleSelection: Array,
    type: Boolean,
    auDeptIds:Array,
    DstartTime:String,
    DendTime:String,
  },
  data() {
    return {
      time: [],
      selectList: [],
      checked: false,
      dialogVisible: true,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  mounted() {
    if(this.DstartTime){
      console.log(this.DstartTime,'----');
      this.time = [this.DstartTime,this.DendTime]
    }
    this.getTree();
  },
  methods: {
    submit() {
      // if (this.type && this.time.length == 0) {
      //   this.$message.warning("请选择授权期限");
      //   return
      // }
      this.selectList = this.$refs.tree.getCheckedNodes();
      this.$emit("confimrDe", this.selectList, this.time);
    },
    change(e) {
      this.$refs.tree.setCheckedNodes(e ? this.treeData : []);
      this.selectList = this.$refs.tree.getCheckedNodes();
    },
    handleCheckChange() {
      this.selectList = this.$refs.tree.getCheckedNodes();
      console.log(this.$refs.tree.getCheckedNodes());
    },
    getTree() {
      treeselect().then((res) => {
        if (res.code == 200) {
          this.treeData = res.data;
          if(this.auDeptIds.length>0){
            this.$refs.tree.setCheckedKeys(this.auDeptIds); 
          }
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>