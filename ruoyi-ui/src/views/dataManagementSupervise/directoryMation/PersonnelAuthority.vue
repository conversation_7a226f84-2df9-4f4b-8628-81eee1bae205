<template>
  <div>
    <el-dialog
      title="人员权限"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <div>
        <el-input
          placeholder="请输入人员名称"
          v-model="params.nickName"
          style="width: 200px"
        ></el-input>
        <el-button style="margin-left: 12px" type="primary" @click="getList"
          >搜 索</el-button
        >
        <el-button style="margin-left: 12px" @click="reset">重 置</el-button>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        row-key="userId"
        max-height="500px"
        style="width: 100%; margin-top: 16px"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" prop="date" width="55">
          <template slot="header" slot-scope="scope">
            <img
              v-if="multipleSelection.length == 0"
              @click="selectionChange(scope.row, 'allact')"
              class="selsct"
              :src="require('@/assets/images/omo_none.png')"
              alt=""
            />
            <img
              v-show="tableData.length > 0 && allType"
              @click="selectionChange(scope.row, 'alldel')"
              class="selsct"
              :src="require('@/assets/images/omo_act.png')"
              alt=""
            />
            <img
              v-show="multipleSelection.length > 0 && !allType"
              @click="selectionChange(scope.row, 'allact')"
              class="selsct"
              :src="require('@/assets/images/omo_show.png')"
              alt=""
            />
          </template>
          <template slot-scope="scope">
            <img
              v-show="!scope.row.acttype"
              @click="selectionChange(scope.row, 'act')"
              class="selsct"
              :src="require('@/assets/images/omo_none.png')"
              alt=""
            />
            <img
              v-show="scope.row.acttype"
              @click="selectionChange(scope.row, 'del')"
              class="selsct"
              :src="require('@/assets/images/omo_act.png')"
              alt=""
            />
          </template>
        </el-table-column>
        <el-table-column label="人员名称" prop="nickName" />
        <el-table-column label="部门" prop="deptName" />
        <el-table-column label="邮箱" prop="email" />
      </el-table>
      <div
        v-if="type"
        style="display: flex; align-items: center; margin-top: 16px"
      >
        <span style="margin-right: 12px"
          >授权期限</span
        >
        <el-date-picker
          v-model="time"
          style="width: 300px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

  <script>
import { getUserAuthorizationList } from "@/api/directoryMation/directoryMationSupervise";
export default {
  props: {
    auUserIds: Array,
    type: Boolean,
    AstartTime: String,
    AendTime: String,
  },
  data() {
    return {
      multipleSelection: [],
      time: [],
      allType:false,
      selectList: [],
      dialogVisible: true,
      tableData: [],
      params: {
        nickName: "",
      },
    };
  },
  mounted() {
    if (this.AstartTime) {
      console.log(this.AstartTime, "----");
      this.time = [this.AstartTime, this.AendTime];
    }
    this.getList();
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      if (newval && newval.length > 0) {
        var flag = newval.every((item) => {
          return item.acttype;
        });
        this.allType = flag ? true : false;
      }
    },
  },
  methods: {
    selectionChange(v, type) {
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.userId == v.userId) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.userId == v.userId) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.userId == v.userId) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].userId == list[j].userId) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].userId == list[j].userId) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.userId]
                ? ""
                : (obj[next.userId] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].userId === arr2[i].userId) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    reset() {
      this.params.nickName = "";
      this.getList();
    },
    getList() {
      getUserAuthorizationList(this.params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data;
          this.tableData.forEach((item, index) => {
            item.acttype = false;
          });
          if(this.auUserIds.length>0){
            this.tableData.forEach(item=>{
              this.auUserIds.forEach(i=>{
                if(item.userId==i){
                  this.multipleSelection.push(item)
                }
              });
            });
          }
          if (this.multipleSelection.length > 0) {
            this.getArrEqual(this.tableData, this.multipleSelection);
          }
        }
      });
    },
    handleSelectionChange(e) {
      console.log(e);
      this.selectList = e;
    },
    submit() {
      // if (this.type && this.time.length == 0) {
      //   this.$message.warning("请选择授权期限");
      //   return;
      // }
      this.$emit("confrimPer", this.multipleSelection, this.time);
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

  <style lang="less" scoped>
.selsct {
  width: 14px;
  cursor: pointer;
}
</style>
