<template>
  <div>
    <el-dialog
      title="岗位权限"
      :visible.sync="dialogVisible"
      width="1400px"
      :before-close="handleClose"
    >
      <div style="display: flex; margin-bottom: 16px; align-items: center">
        <div style="margin-right: 16px">
          <span>岗位名称：</span>
          <el-input
            v-model="params.postName"
            style="width: 200px"
            placeholder="请输入"
          ></el-input>
        </div>
        <div>
          <span>岗位编码：</span>
          <el-input
            v-model="params.postCode"
            style="width: 200px"
            placeholder="请输入"
          ></el-input>
        </div>
        <el-button style="margin-left: 10px" @click="search" type="primary"
          >搜索</el-button
        >
        <el-button style="margin-left: 10px" @click="reset">重置</el-button>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" prop="date" width="55">
          <template slot="header" slot-scope="scope">
            <img
              v-if="multipleSelection.length == 0"
              @click="selectionChange(scope.row, 'allact')"
              class="selsct"
              :src="require('@/assets/images/omo_none.png')"
              alt=""
            />
            <img
              v-show="tableData.length > 0 && allType"
              @click="selectionChange(scope.row, 'alldel')"
              class="selsct"
              :src="require('@/assets/images/omo_act.png')"
              alt=""
            />
            <img
              v-show="multipleSelection.length > 0 && !allType"
              @click="selectionChange(scope.row, 'allact')"
              class="selsct"
              :src="require('@/assets/images/omo_show.png')"
              alt=""
            />
          </template>
          <template slot-scope="scope">
            <img
              v-show="!scope.row.acttype"
              @click="selectionChange(scope.row, 'act')"
              class="selsct"
              :src="require('@/assets/images/omo_none.png')"
              alt=""
            />
            <img
              v-show="scope.row.acttype"
              @click="selectionChange(scope.row, 'del')"
              class="selsct"
              :src="require('@/assets/images/omo_act.png')"
              alt=""
            />
          </template>
        </el-table-column>
        <el-table-column label="岗位编码" prop="postCode" width="100px"/>
        <el-table-column label="岗位名称" prop="postName" width="140px"/>
        <el-table-column label="所属公司" width="100px" prop="unitShortName"/>
        <el-table-column label="所属部门" prop="deptName" width="100px"/>
        <el-table-column label="人员"  min-width="600px" align="center" prop="sysUserListName" />
      </el-table>
      <div
        v-if="type"
        style="display: flex; align-items: center; margin-top: 16px"
      >
        <span style="margin-right: 12px"
          >授权期限</span
        >
        <el-date-picker
          v-model="time"
          style="width: 300px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="params.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="params.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPostAuthorizationList } from "@/api/directoryMation/directoryMationSupervise";
export default {
  props: {
    type: Boolean,
    auPostIds: Array,
    PstartTime:String,
    PendTime:String,
    
  },
  data() {
    return {
      total: 0,
      time: [],
      params: {
        pageNum: 1,
        pageSize: 10,
        postName: "",
        postCode: "",
      },
      dialogVisible: true,
      tableData: [],
      selectList: [],
      multipleSelection: [],
      allType: false,
    };
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      var flag = newval.every((item) => {
        return item.acttype;
      });
      this.allType = flag ? true : false;
    },
  },
  mounted() {
    if(this.PstartTime){
      console.log(this.PstartTime,'----');
      this.time = [this.PstartTime,this.PendTime]
    }
    if (this.auPostIds) {
      this.multipleSelection = [];
      getPostAuthorizationList().then((res) => {
        if (res.code == 200) {
          res.rows.forEach((item) => {
            this.auPostIds.forEach((i) => {
              if (item.postId == i) {
                this.multipleSelection.push(item);
              }
            });
          });
          this.getList();
        }
      });
    }else{
      this.getList();
    }
    
  },
  methods: {
    reset() {
      this.params = {
        pageNum: 1,
        pageSize: 10,
        postName: "",
        postCode: "",
      };
      this.getList();
    },
    getList() {
      getPostAuthorizationList(this.params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.total = res.total;
          this.tableData.forEach((item, index) => {
            item.acttype = false;

            item.sysUserListName=item.sysUserList?.map(item1=>item1.nickName).join('、')||'——';
          });
          if (this.multipleSelection.length > 0) {
            this.getArrEqual(this.tableData, this.multipleSelection);
          }
        }
      });
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    selectionChange(v, type) {
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.postId == v.postId) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.postId == v.postId) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.postId == v.postId) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].postId == list[j].postId) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].postId == list[j].postId) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.postId]
                ? ""
                : (obj[next.postId] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].postId === arr2[i].postId) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    submit() {
      // if (this.type && this.time.length == 0) {
      //   this.$message.warning("请选择授权期限");
      //   return;
      // }
      this.$emit("confirmPost", this.multipleSelection, this.time);
    },
    handleSelectionChange(e) {
      this.selectList = e;
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.selsct {
  width: 14px;
  cursor: pointer;
}
</style>