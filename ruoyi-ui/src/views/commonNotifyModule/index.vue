<template>
  <div style="padding: 16px">
    <h1 style="text-align: center; font-weight: bold; font-size: 28px">
      {{ data.notifyMsg }}
    </h1>
    <div class="msg" style="display: flex; justify-content: center">
      <div>{{ data.notifyModule }}</div>
      <div>发起人：{{ data.createBy }}</div>
      <div>发起时间：{{ data.createTime }}</div>
    </div>
    <div class="solid"></div>
    <component :is="componentName" :data="data" @submit="submit"></component>
    <div style="margin-top: 40px; text-align: center">
      <el-button type="primary" @click="submit">确 认</el-button>
      <el-button @click="$router.back()">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { confirmNotify } from "@/api/checkWork/monthlyRemind";
import MonthlyRemind from "./components/monthlyRemind.vue";
import OverTimeRemind from "./components/overTimeRemind.vue";
import LeaveRemind from "./components/leaveRemind.vue";
import BonusPenaltyRemind from "./components/bonusPenaltyRemind.vue";
import GoErrandRemind from "./components/goErrandRemind.vue";

export default {
  name: "CommonNotifyModule",
  data() {
    return {
      componentName: null,
      componentNameObj: Object.freeze({
        月报评审: "MonthlyRemind",
        加班申请: "OverTimeRemind",
        请假申请: "LeaveRemind",
        取消加班申请: "OverTimeRemind",
        取消请假申请: "LeaveRemind",
        '取消奖励/惩罚': "BonusPenaltyRemind",
        '取消出差': "GoErrandRemind",
      }),
      data:JSON.parse(this.$route.query.row),
    };
  },
  components: { MonthlyRemind, OverTimeRemind,LeaveRemind ,BonusPenaltyRemind,GoErrandRemind},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.componentName = this.componentNameObj[this.data.notifyModule];
    },
    submit() {
      confirmNotify({ id: this.data.id }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          const obj = { path: this.$route.path };
          this.$tab.closePage(obj);
          this.$router.back();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.msg {
  div {
    margin-right: 30px;
    font-size: 16px;
  }
}
.solid {
  width: 100%;
  height: 1px;
  background: #ccc;
  margin: 20px 0;
}
</style>
