<template>
  <div style="font-size: 18px">
    <div>{{ contentData.title }}</div>
    <div
      v-if="
        contentData.askLeaveSlaveList &&
        contentData.askLeaveSlaveList.length == 1
      "
    >
      <div
        v-for="(item, index) in voidContentLeaveList"
        :key="index"
        style="display: flex"
      >
        <div>{{ item.label }}:</div>
        <div class="text-black">
          【{{ contentData.askLeaveSlaveList[0][item.value] }}】
        </div>
      </div>
    </div>
    <div v-else>
      <div
        v-for="(item, index) in contentData.askLeaveSlaveList"
        :key="index"
        class="mb-2"
      >
        <div
          v-for="(item1, index1) in voidContentLeaveListMultiple"
          :key="index1"
          style="display: flex"
        >
          <div>{{ item1.label }}{{ index + 1 }}:</div>
          <div class="text-black">【{{ item[item1.value] }}】</div>
        </div>
      </div>
      <div
        v-for="(item, index) in voidContentLeaveListMultipleLast"
        :key="index + 'last'"
        style="display: flex"
      >
        <div>{{ item.label }}:</div>
        <div class="text-black">【{{ contentData[item.value] }}】</div>
      </div>
    </div>
    <div class="mt-2">
      <div v-show="data.notifyModule == '请假申请'" class="flex">
        <div>取消原因:</div>
        <div class="mr-1" v-show="contentData.voidReason">
          【 <span style="color: red">{{ contentData.voidReason }}</span> 】
        </div>
      </div>
      <div v-show="data.notifyModule == '取消请假申请'" class="flex">
        <div v-if="contentData.handleState == '2'">该申请已被拒绝，拒绝原因为：</div>
        <div v-else>该申请已被同意</div>
        <div class="mr-1" v-show="contentData.refuseReason">
          【 <span style="color: red">{{ contentData.refuseReason }}</span> 】
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { askHandleLeave } from "@/api/checkWork/leave";
export default {
  name: "LeaveRemind",
  data() {
    return {
      voidContentLeaveList: Object.freeze([
        { label: "请假时间", value: "time" },
        { label: "假种", value: "leaveType" },
        { label: "请假理由", value: "reason" },
        { label: "请假时长", value: "times" },
      ]),
      voidContentLeaveListMultiple: Object.freeze([
        { label: "假种", value: "leaveType" },
        { label: "请假时间", value: "time" },
        { label: "请假时长", value: "times" },
      ]),
      voidContentLeaveListMultipleLast: Object.freeze([
        { label: "请假理由", value: "reason" },
        { label: "请假时长合计", value: "totalTimes" },
      ]),
      data: JSON.parse(this.$route.query.row),
      contentData: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { data } = await askHandleLeave(this.data.correlationId);
      data.title = `【${data.nickName}】 于${
        this.data.createTime
      }取消了一条审核状态为 【${
        this.$store.state.data.KV_MAP.check_work_approve_status[data.state]
      }】 的请假申请:`;
      data.totalTimes =
        data.askLeaveSlaveList
          .map((item) => item.times)
          .reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
          ) + "天";
      data.askLeaveSlaveList.forEach((item) => {
        item.time = `${item.startTime} ${item.startTimePeriod} 至 ${item.endTime} ${item.endTimePeriod}`;
        item.times = item.times + "天";
        item.reason = data.reason;
        item.leaveType = this.$store.state.data.KV_MAP.check_work_pseudotype[item.leaveType];
      });
      data.leaveType = data.askLeaveSlaveList[0].leaveType;
      this.contentData = data;
    },
  },
};
</script>
