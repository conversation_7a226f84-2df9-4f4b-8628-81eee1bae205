<template>
  <div style="font-size: 18px">
    <div>{{ contentData.title }}</div>
    <div
      v-for="(item1, index1) in showList"
      :key="index1"
      style="display: flex"
    >
      <div>{{ item1.label }}:</div>
      <div class="text-black">【{{ contentData[item1.value] }}】</div>
    </div>

    <div class="mt-2">
      <div class="flex">
        <div v-if="contentData.handleState == '2'">
         {{isSelf? '该申请已被拒绝，拒绝原因为':'取消原因'}}
        </div>
        <div v-else>该申请已被同意</div>
        <div class="mr-1" v-show="contentData.refuseReason">
          【 <span style="color: red">{{ contentData.refuseReason }}</span> 】
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { punishmentHandleId } from "@/api/checkWork/bonusPenalty";
export default {
  name: "BonusPenaltyRemind",
  data() {
    return {
      showListReward: Object.freeze([
        { label: "人员姓名", value: "nickName" },
        { label: "奖惩单号", value: "rewardPunishmentCode" },
        { label: "奖惩类型", value: "type" },
        { label: "奖惩金额", value: "amount" },
        { label: "奖惩事由", value: "reason" },
        { label: "审核状态", value: "status" },
      ]),
      showListPunishment: Object.freeze([
        { label: "人员姓名", value: "nickName" },
        { label: "奖惩单号", value: "rewardPunishmentCode" },
        { label: "奖惩类型", value: "type" },
        { label: "奖励物品", value: "itemName" },
        { label: "物品数量", value: "itemNum" },
        { label: "奖惩事由", value: "reason" },
        { label: "审核状态", value: "status" },
      ]),
      showList: [],
      data: JSON.parse(this.$route.query.row),
      contentData: {},
      isSelf:false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { data } = await punishmentHandleId(this.data.correlationId);
      this.showList =
        (data.measure==1)? this.showListReward : this.showListPunishment;
      if (data.userId == this.data.disposeUser) {
        this.isSelf = true;
        data.title = `【${data.nickName}】 于${this.data.createTime}申请取消了一条关于您的奖惩申请`;
        this.showList=this.showList.filter(item=>item.value!='nickName')
      } else {
        data.title = `【${data.nickName}】 于${
          this.data.createTime
        }取消了一条审核状态为 【${
          this.$store.state.data.KV_MAP.check_work_approve_status[data.status]
        }】 的奖惩申请:`;
      }
      data.type=this.$store.state.data.KV_MAP.bonus_penalty_type[data.type]
      data.status=this.$store.state.data.KV_MAP.check_work_approve_status[data.status]
      this.contentData = data;
    },
  },
};
</script>
  