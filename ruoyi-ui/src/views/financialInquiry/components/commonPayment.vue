<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="项目名称" class="form-item" prop="projectIds">
        <el-select
          multiple
          filterable
          v-model="queryParams.projectIds"
          placeholder="请选择项目名称"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.projectList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="担保公司" class="form-item" prop="guaranteeIds">
        <el-select
          v-model="queryParams.guaranteeIds"
          multiple
          filterable
          placeholder="请选择担保公司"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.custNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产方" prop="assetIds">
        <el-select
          v-model="queryParams.assetIds"
          placeholder="请选择担保公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.partnerNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金方" prop="fundIds">
        <el-select
          v-model="queryParams.fundIds"
          placeholder="请选择资金方"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.fundNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="其他公司" prop="others">
        <el-select
          v-model="queryParams.others"
          placeholder="请选择其他公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.otherNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="账套" prop="accountsSetsIds">
        <el-select
          v-model="queryParams.accountsSetsIds"
          placeholder="请选择账套"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in accountSetsList"
            :key="index"
            :label="item.companyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-model="queryParams.time"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <MoreSearch modelCode="DATAREPORT" :params="queryParams" v-show="showMoreSearch"></MoreSearch> 
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
         <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
            >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
          >
      </el-form-item>
    </el-form>
    <ShowMoney :moneyList="totalPaymentList" :money="totalPayment" />
    <!-- <el-button
      v-hasPermi="hasPermiAdd"
      type="primary"
      class="mb-2"
      size="mini"
      @click="add('add')"
      >手工录入</el-button
    > -->
    <el-button
      v-hasPermi="hasPermiExport"
      type="warning"
      plain
      class="mb-2"
      icon="el-icon-download"
      size="mini"
      @click="handleExports"
      >导出</el-button
    >
    <MyTable :columns="columns" :source="configList" :queryParams="queryParams">
      <!-- <slot> -->
      <template
        v-if="columnsHeader.receiveCompensateAmt"
        #h_receiveCompensateAmt="{ record }"
      >
        收到代偿款
        <el-tooltip
          class="item"
          effect="dark"
          :content="columnsHeader.receiveCompensateAmt"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template
        v-if="columnsHeader.actCompensateAmt"
        #h_actCompensateAmt="{ record }"
      >
        实际代偿款
        <el-tooltip
          class="item"
          effect="dark"
          :content="columnsHeader.actCompensateAmt"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template v-if="columnsHeader.balanceAmt" #h_balanceAmt="{ record }">
        余额
        <el-tooltip
          class="item"
          effect="dark"
          :content="columnsHeader.balanceAmt"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template
        v-if="columnsHeader.miCompensateAmt"
        #h_miCompensateAmt="{ record }"
      >
        手工录入代偿款
        <el-tooltip
          class="item"
          effect="dark"
          :content="columnsHeader.miCompensateAmt"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template
        v-if="columnsHeader.accumCompensateAmt"
        #h_accumCompensateAmt="{ record }"
      >
        累计代偿款
        <el-tooltip
          class="item"
          effect="dark"
          :content="columnsHeader.accumCompensateAmt"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>

      <template #opertion="{ record }">
        <div>
          <el-button type="text" @click="viewProjectDetail(record)"
            >查看明细</el-button
          >
          <el-button
            v-hasPermi="hasPermiAdd"
            type="text"
            @click="add('edit', record)"
            >手工录入</el-button
          >
        </div>
      </template>
      <!-- </slot> -->
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <ProjectDetails
      :params="paramsProjectDetails"
      v-model="openProjectDetails"
      :columnsRecord="columnsProjectDetails"
      :api="detailApi"
    />
    <ManualInput
      :projectListTable="projectListTable"
      :dataInfo="dataInfo"
      :title="manualInputTitle"
      v-model="openManualInput"
      @sure="manualInputSure"
    />
  </div>
</template>

<script>
import {
  accountSets,
} from "@/api/financialInquiry/president";
import {
  compensateList,
  compensateExport,
  compensateDetail,
  getmManaulInputList,
  addmManaulInput,
} from "@/api/financialInquiry/operations";
import XEUtils from "xe-utils";
import ShowMoney from "./showMoney";
import ShowHeader from "./showHeader";
import companyForm from "@/views/financialInquiry/mixin/companyForm";
import exportTable from "@/views/financialInquiry/mixin/exportTable";

export default {
  name: "CommonPayment",
  components: { ShowMoney, ShowHeader },
  mixins: [companyForm, exportTable],
  props: {
    typePay: {
      type: String,
      default: "",
    },
    header: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    hasPermiAdd: {
      type: Array,
      default: () => [],
    },
    hasPermiExport: {
      type: Array,
      default: () => [],
    },
    columnsHeader: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    otherQueryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showMoreSearch:false,
      totalPaymentList: [
        { label: "收到代偿款合计", value: "compensationReceived" },
        { label: "实际代偿款合计", value: "compensationAmount" },
        { label: "余额合计", value: "compensatoryPayments" },
        { label: "累计代偿款合计", value: "accumulatedCompensation" },
      ],
      accountSetsList: [],
      totalPayment: {},
      columnsExport: [],
      detailApi: null,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectIds: undefined,
        guaranteeIds: undefined,
        assetIds: undefined,
        fundIds: undefined,
        others: undefined,
        accountsSetsIds: undefined,
        time: [
          `${new Date().getFullYear()}-01-01`,
          XEUtils.toDateString(new Date(), "yyyy-MM-dd"),
        ],
      },
      configList: [],
      total: 0,
      openProjectDetails: false,
      paramsProjectDetails: {},
      openManualInput: false,
      projectListTable: [],
      dataInfo: {},
      manualInputTitle: "",
      columnsProjectDetails: Object.freeze([
        { label: "记账日期", prop: "dataDay" },
        {
          label: "收到代偿款",
          prop: "receiveCompensateAmt",
        },
        {
          label: "实际代偿款",
          prop: "actCompensateAmt",
        },
        {
          label: "手工录入代偿款",
          prop: "manaulCompensateAmt",
        },
      ]),
      pickerOptions: Object.freeze({
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      }),
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getCompanyList();
      this.getAllAccountSetsList();
      this.getList();
      // this.getProjectListTable();
      if (this.typePay == "compensationpayment") {
        this.detailApi = compensateDetail;
      }
    },

    async getAllAccountSetsList() {
      const {data} = await accountSets();
      this.accountSetsList = [...data];
    },
    getMoney(map) {
      this.totalPayment = {
        compensationReceived:
          map.sumReceiveCompensateAmt.toLocaleString("zh-CN"),
        compensationAmount: map.sumActCompensateAmt.toLocaleString("zh-CN"),
        compensatoryPayments: map.sumBalanceAmt.toLocaleString("zh-CN"),
        accumulatedCompensation:
          map.sumAccumCompensateAmt.toLocaleString("zh-CN"),
      };
    },

    async getList() {
      const typeApi = {
        compensationpayment: compensateList,
      };
      const { rows, total, map } = await typeApi[this.typePay]({
        ...this.getParams(),
        ...this.otherQueryParams,
      });
      this.configList = this.handlerConfigList(rows);
      this.total = total;
      this.getMoney(map);
    },
    getParams(exports = false) {
      const params = XEUtils.clone(this.queryParams);
      if (params.time) {
        params.startTime = params.time[0]?.replace(/-/g, ".");
        params.endTime = params.time[1]?.replace(/-/g, ".");
      }
      if (!exports) {
        const toSting = [
          "projectIds",
          "guaranteeIds",
          "assetIds",
          "fundIds",
          "others",
          "accountsSetsIds",
        ];
        toSting.forEach((item) => {
          params[item] = params[item]?.join() || undefined;
        });
      }
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      delete params.time;
      return params;
    },
    handlerConfigList(rows) {
      const toQarr = [
        "receiveCompensateAmt",
        "actCompensateAmt",
        "initialAmt",
        "miCompensateAmt",
        "accumCompensateAmt",
        "balanceAmt",
      ];
      return rows.map((item, index) => {
        toQarr.forEach((item1) => {
          item[item1] = item[item1].toLocaleString("zh-CN");
        });

        return item;
      });
    },
    async getProjectListTable() {
      const { rows } = await compensateList();
      this.projectListTable = rows;
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.moreSearch=undefined
      this.handleQuery();
    },
    async add(type, record) {
      // if (type == "add") {
      //   this.manualInputTitle = "手工录入代偿款数据";
      //   this.dataInfo = {};
      // } else if (type == "edit") {
        this.manualInputTitle = "编辑手工录入的代偿款数据";
        if (this.typePay == "compensationpayment") {
          const params={
            projectId:record.projectId,
            stsIdentify:'A02',
            startTime:this.queryParams.time?this.queryParams.time[0]?.replace(/-/g, "."):undefined,
            endTime:this.queryParams.time?this.queryParams.time[1]?.replace(/-/g, "."):undefined,
          }
          const {rows} = await getmManaulInputList(params);
          this.dataInfo = {tableList:rows,projectName:record.projectName,projectId:record.projectId};
        }
      // }
      this.openManualInput = true;
    },
    async manualInputSure(value){
      const params=XEUtils.clone(value,true);
      params.forEach(item=>{
        item.stsIdentify="A02";
        item.actCompensateAmt=item.debitAmount.replace(/,/g, '');
      })
      await addmManaulInput({list:params,projectId:this.dataInfo.projectId});
      this.$message.success("操作成功");
      this.getList();

    },
    async handleExports() {
      this.columnsExport = this.columns.filter(
        (item) => item.key != "opertion"
      );
      const params = XEUtils.clone(this.getParams(true), true);
      delete params.pageNum;
      delete params.pageSize;
      if (this.typePay == "compensationpayment") {
        const data = await compensateExport(params);
        this.handleExport(data, "代偿款");
      }
    },
    viewProjectDetail(value) {
      this.paramsProjectDetails = {
        projectId: value.projectId,
        projectName: value.projectName,
        accountsSetsIds: this.queryParams.accountsSetsIds,
        startTime:
          this.queryParams.time && this.queryParams.time[0]?.replace(/-/g, "."),
        endTime:
          this.queryParams.time && this.queryParams.time[1]?.replace(/-/g, "."),
      };
      this.openProjectDetails = true;
    },
  },
};
</script>
