<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="项目名称" class="form-item" prop="projectIds">
        <el-select
          multiple
          filterable
          v-model="queryParams.projectIds"
          placeholder="请选择项目名称"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.projectList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="担保公司" class="form-item" prop="guaranteeIds">
        <el-select
          v-model="queryParams.guaranteeIds"
          multiple
          filterable
          placeholder="请选择担保公司"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.custNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产方" prop="assetIds">
        <el-select
          v-model="queryParams.assetIds"
          placeholder="请选择担保公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.partnerNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金方" prop="fundIds">
        <el-select
          v-model="queryParams.fundIds"
          placeholder="请选择资金方"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.fundNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="其他公司" prop="others">
        <el-select
          v-model="queryParams.others"
          placeholder="请选择其他公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.otherNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间维度" prop="timeType">
        <el-select
          v-model="queryParams.timeType"
          placeholder="请选择时间维度"
          @change="handleTime"
        >
          <el-option
            v-for="(item, index) in timeDimensionList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-if="queryParams.timeType == 'month'"
          v-model="queryParams.time"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
          @change="handleQuery"
        >
        </el-date-picker>
        <div v-if="queryParams.timeType == 'year'">
          <el-date-picker
            v-model="queryParams.time[0]"
            type="year"
            placeholder="开始年份"
            :pickerOptions="pickerOptionsStart"
            value-format="yyyy"
            @change="handleQuery"
          >
          </el-date-picker>
          <span class="mx-1">至</span>
          <el-date-picker
            v-model="queryParams.time[1]"
            type="year"
            placeholder="结束年份"
            :pickerOptions="pickerOptionsEnd"
            value-format="yyyy"
            @change="handleQuery"
          >
          </el-date-picker>
        </div>
      </el-form-item>
      <MoreSearch modelCode="DATAREPORT" :params="queryParams" v-show="showMoreSearch"></MoreSearch>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
         <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
            >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
          >
      </el-form-item>
    </el-form>
    <el-tabs
      v-model="queryParams.stsIdentify"
      type="card"
      @tab-click="getTableEcharts"
    >
      <el-tab-pane
        v-for="(item, index) in dataPermissionsList"
        :key="index"
        :label="item.label"
        :name="item.name"
      >
      </el-tab-pane>
    </el-tabs>
    <ShowMoney :moneyList="totalIncomeList" :money="totalIncome" />
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="数据表格" name="table">
        <el-button
          v-show="type == 'ceo'"
          v-hasPermi="['fc:projectIncome:export']"
          type="warning"
          plain
          class="mb-2"
          icon="el-icon-download"
          size="mini"
          @click="handleExports"
          >导出</el-button
        >
        <el-button
          v-show="type=='operation'"
          v-hasPermi="['fc:operation:projectIncome:export']"
          type="warning"
          plain
          class="mb-2"
          icon="el-icon-download"
          size="mini"
          @click="handleExports"
          >导出</el-button
        >
        <MyTable
          :key="tableKey"
          :columns="columns"
          :source="configList"
          :queryParams="queryParams"
          @cell-click="cellClick"
          :cell-style="cellStyle"
        >
        </MyTable>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="图表" name="echarts">
        <MyEchart
          :opationDate="opationDate"
          style="width: 80vw; height: 400px"
        ></MyEchart>
      </el-tab-pane>
    </el-tabs>
    <DetailDialog
      v-model="dialog"
      :type="queryParams.stsIdentify"
      :configList="configListItem"
    />
  </div>
</template>

<script>
import {
  projectIncomeList,
  projectIncomeDetail,
  projectIncomeExport,
} from "@/api/financialInquiry/president";
import ShowMoney from "./showMoney";
import ShowHeader from "./showHeader";
import DetailDialog from "./DetailDialog";
import form from "@/views/financialInquiry/mixin/form";
import companyForm from "@/views/financialInquiry/mixin/companyForm";
import exportTable from "@/views/financialInquiry/mixin/exportTable";
import { clone } from "xe-utils";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
export default {
  name: "CommonRevenue",
  components: { ShowMoney, ShowHeader, DetailDialog },
  mixins: [form, companyForm, exportTable],
  props: {
    header: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    type: {
      type: String,
      required: true,
      default: "ceo",
    },
  },

  data() {
    return {
      tableKey: 0,
      totalIncomeList: [{ label: "合计", value: "totalIncome" }],
      totalIncome: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectIds: undefined,
        guaranteeIds: undefined,
        assetIds: undefined,
        fundIds: undefined,
        others: undefined,
        timeType: "month",
        time: [],
        stsIdentify: "",
      },
      dataPermissionsList: [],
      activeName: "table",
      configList: [],
      total: 0,
      opationDate: {},
      columns: [],
      monthColums: [],
      timeDimensionList: Object.freeze([
        { label: "按月", value: "month" },
        { label: "按年", value: "year" },
      ]),
      dialog: false,
      configListItem: [],
      showMoreSearch:false
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getCompanyList();
      this.getDataPermissionsList();
      this.getDefaultMonth(12);
      this.getTableEcharts();
    },
    async getTableEcharts() {
      await this.getList();
      this.getOpationDate();
    },

    async getDataPermissionsList() {
      if (this.type == "ceo") {
        this.dataPermissionsList = [
          {
            label: "收入",
            name: "00",
            permi: ["president:projectRevenue:income"],
          },
          // {
          //   label: "毛利",
          //   name: "01",
          //   permi: ["president:projectRevenue:profit"],
          // },
          {
            label: "净毛利",
            name: "02",
            permi: ["president:projectRevenue:netProfit"],
          },
        ];
      }else if(this.type=="operation"){
        this.dataPermissionsList = [
          {
            label: "收入",
            name: "00",
            permi: ["operation:president:projectRevenue:income"],
          },
          // {
          //   label: "毛利",
          //   name: "01",
          //   permi: ["operation:president:projectRevenue:profit"],
          // },
          {
            label: "净毛利",
            name: "02",
            permi: ["operation:president:projectRevenue:netProfit"],
          },
        ];
      }
      this.dataPermissionsList = this.dataPermissionsList.filter((item) =>
        checkPermi(item.permi)
      );
      this.queryParams.stsIdentify = this.dataPermissionsList[0]?.name;
    },

    getParams(exports = false) {
      const params = clone(this.queryParams);
      if(params.time){
        params.startTime = params.time[0]?.replace(/-/g, ".");
        params.endTime = params.time[1]?.replace(/-/g, ".");
      }
      if (!exports) {
        const toSting = [
          "projectIds",
          "guaranteeIds",
          "assetIds",
          "fundIds",
          "others",
        ];
        toSting.forEach((item) => {
          params[item] = params[item]?.join() || undefined;
        });
      }
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      delete params.time;
      return params;
    },
    getMoney(map) {
      this.totalIncome = {
        totalIncome: map.sumTotalAmt.toLocaleString("zh-CN"),
      };
    },
    getColumns(rows) {
      const startColumns = [
        { label: "项目名称", prop: "projectName", width: 250,fixed:'left' },
        {
          label: "合计",
          prop: "totalAmt",
          width: 150,

          type: ["01", "02"].includes(this.queryParams.stsIdentify)
            ? "add"
            : "",
          fixed: "left",
        },
      ];
      const endColumns = [];
      this.monthColums = [];
      Object.keys(rows[0]?.amtMap || {}).forEach((item) => {
        this.monthColums.push({
          label: item,
          prop: item,
          width: 150,
          type: ["01", "02"].includes(this.queryParams.stsIdentify)
            ? "add"
            : "",
        });
      });
      this.columns = startColumns.concat(this.monthColums).concat(endColumns);
    },
    async getList() {
      const apiUrl = {
        ceo: projectIncomeList,
        operation: projectIncomeList,
      };
      const { rows, total, map } = await apiUrl[this.type](this.getParams());
      this.total = total;
      this.configList = this.handlerConfigList(rows);
      this.getMoney(map);
      this.tableKey++;
      this.getColumns(rows);
    },
    handleTime(value) {
      const obj = {
        month: () => {
          this.getDefaultMonth(12);
        },
        year: () => {
          this.getDefaultYear();
        },
      };
      obj[value]();
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getTableEcharts();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.moreSearch=undefined
      this.handleQuery();
    },
    formatDates(dateString) {
      return dateString
        .replace(/(\d{4})年(\d{2})月/, "$1-$2")
        .replace(/(\d{4})年/, "$1");
    },
    async cellClick(row, column) {
      if (column.type === "add") {
        const params = {
          stsIdentify: this.queryParams.stsIdentify,
          time: this.formatDates(column.property),
          projectId: row.projectId,
          timeType: this.queryParams.timeType,
        };
        const apiUrl = {
          ceo: projectIncomeDetail,
          operation: projectIncomeDetail,
        };
        const { resultFee, fee1, fee2, fee3, fee4 } = await apiUrl[this.type](
          params
        );
        this.configListItem = [{ resultFee, fee1, fee2, fee3, fee4 }];
        const toThousandth = ["resultFee", "fee1", "fee2", "fee3", "fee4"];
        this.configListItem = this.configListItem.map((item, index) => {
          toThousandth.forEach((item1) => {
            item[item1] = item[item1]?.toLocaleString("zh-CN");
          });
          return item;
        });
        this.dialog = true;
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.type === "add") {
        return { cursor: "pointer", color: "rgb(24, 144, 255)" };
      } else {
        return "";
      }
    },
    async handleExports() {
      const params = clone(this.getParams(true), true);
      delete params.pageNum;
      delete params.pageSize;
      const apiUrl = {
        ceo: projectIncomeExport,
        operation: projectIncomeExport,
      };
      const rows = await apiUrl[this.type](params);
      const exportData = this.handlerConfigList(rows);
      this.handleExport(exportData, "项目收入");
    },
    getOpationDate() {
      const xData = this.monthColums.map((item) => item.prop);
      const legendData = this.configList.map((item) => item.projectName);
      const series = this.configList.map((item) => {
        return {
          name: item.projectName,
          type: "line",
          data: Object.values(item?.amtMap || {}),
        };
      });
      this.opationDate = {
        tooltip: {
          trigger: "axis",
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          data: legendData,
          right: "10%",
        },
        xAxis: [
          {
            type: "category",
            data: xData,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "金额",
          },
        ],
        series,
      };
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep .pagination-container {
  padding: 0px !important;
}
</style>
