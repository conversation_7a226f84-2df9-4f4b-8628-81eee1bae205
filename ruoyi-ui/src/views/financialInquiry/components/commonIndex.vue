<template>
  <div class="p-5">
    <div class="w-4/5 m-auto">
      <div class="my-2" style="color: #999999">
        <slot></slot>
      </div>
      <MyTable :columns="columns" :source="configList" :showIndex="true" border>
        <template #dataIndicators="{ record }">
          <el-button type="text" @click="goDetail(record)">{{
            record.dataIndicators
          }}</el-button>
        </template>
        <template #explain="{ record }">
          <div class="mb-4">
            <div v-for="(item, index) in record.explainTop" :key="index">
              {{ item }}
            </div>
          </div>
          <div
            v-for="(item, index) in record.explainBottom"
            :key="index"
            class="flex"
          >
            <div class="font-bold mr-1">{{ item.title }}</div>
            <div>{{ item.content }}</div>
          </div>
        </template>
      </MyTable>
    </div>
  </div>
</template>

<script>
export default {
  name: "CommonIndex",
  props: {
    configList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      columns: Object.freeze([
        { label: "数据指标", key: "dataIndicators", width: "200" },
        { label: "说明", key: "explain", minWidth: "300" },
      ]),
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {},
    goDetail(value) {
      this.$router.push(value.path);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table__header-wrapper {
  .has-gutter {
    th:first-child {
      div {
        display: none !important;
      }
    }
  }
}
</style>