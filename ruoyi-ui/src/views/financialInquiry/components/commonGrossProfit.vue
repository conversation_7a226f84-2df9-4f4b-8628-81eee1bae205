<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="项目名称" class="form-item" prop="projectName">
        <el-select
          multiple
          filterable
          v-model="queryParams.projectName"
          placeholder="请选择项目名称"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in projectList"
            :key="index"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="担保公司" class="form-item" prop="custNo">
        <el-select
          v-model="queryParams.custNo"
          multiple
          filterable
          placeholder="请选择担保公司"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in companyList.custNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产方" prop="partnerNo">
        <el-select
          v-model="queryParams.partnerNo"
          placeholder="请选择担保公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in companyList.partnerNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金方" prop="fundNo">
        <el-select
          v-model="queryParams.fundNo"
          placeholder="请选择资金方"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in companyList.fundNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="其他公司" prop="otherNo">
        <el-select
          v-model="queryParams.otherNo"
          placeholder="请选择其他公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in companyList.otherNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-model="queryParams.time"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <ShowMoney :moneyList="totalGrossList" :money="totalGross" />
    <el-button
      type="warning"
      plain
      class="mb-2"
      icon="el-icon-download"
      size="mini"
      @click="handleExport"
      >导出</el-button
    >
    <MyTable :columns="columns" :source="configList" :queryParams="queryParams">
      <template #h_jingshouru="{ record }">
        净毛利
        <el-tooltip class="item" effect="dark" placement="top-start">
          <div slot="content" v-html="tableContent"></div>
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template #h_ticheng="{ record }">
        项目提成
        <el-tooltip
          class="item"
          effect="dark"
          content="取自薪资系统中项目提成"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template #h_maolilv="{ record }">
        项目毛利率
        <el-tooltip
          class="item"
          effect="dark"
          content="项目毛利率 = （净毛利 - 项目提成） ÷ 净毛利"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>

      <template #opertion="{ record }">
        <el-button type="text" @click="viewProjectDetail(record)"
          >查看明细</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <ProjectDetails
      width="800px"
      :params="paramsProjectDetails"
      v-model="openProjectDetails"
      :columnsRecord="columnsProjectDetails"
    />
  </div>
</template>

<script>
import { querySelectList } from "@/api/oa/deploy";
import { projectParameter } from "@/api/businessInformation/productInformation";
import XEUtils from "xe-utils";
import ShowHeader from "./showHeader";
import ShowMoney from "./showMoney";
import companyForm from "@/views/financialInquiry/mixin/companyForm";

export default {
  name: "CommonGrossProfit",
  components: { ShowHeader, ShowMoney },
  mixins: [companyForm],

  data() {
    return {
      projectList: [],
      totalGrossList: [
        { label: "净毛利合计", value: "totalGross" },
        { label: "项目提成合计", value: "totalGross" },
        { label: "平均项目毛利率", value: "totalGross" },
      ],
      totalGross: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: undefined,
        custNo: undefined,
        partnerNo: undefined,
        fundNo: undefined,
        otherNo: undefined,

        time: [
          `${new Date().getFullYear()}-01-01`,
          XEUtils.toDateString(new Date(), "yyyy-MM-dd"),
        ],
      },

      configList: [],
      total: 0,
      tableContent:
        "净毛利 = 毛利 -  (主营业务成本-项目名称，取借方发生额）<br>毛利 = 收入 - （应付账款2-项目名称-单位名称，取借方发生额）-（应付账款3-项目名称-单位名称，取借方发生额）<br>收入 = 预收账款-项目名称，取贷方发生额",
      openProjectDetails: false,
      paramsProjectDetails: {},
      header: Object.freeze({
        title: "各项目毛利率",
        content: [
          "数据来源：智慧财务系统。展示统计时间范围内项目的收入与毛利率情况",
        ],
      }),
      columns: Object.freeze([
        { label: "项目名称", prop: "projectName", width: "600px" },

        {
          label: "净毛利",
          prop: "jingshouru",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "项目提成",
          prop: "ticheng",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "项目毛利率",
          prop: "maolilv",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "操作",
          key: "opertion",
          width: "100px",
        },
      ]),
      columnsProjectDetails: Object.freeze([
        { label: "记账日期", prop: "operTime" },
        {
          label: "收入",
          prop: "shoudao",
        },
        {
          label: "平台技术服务费",
          prop: "jingshouru",
        },
        {
          label: "第三方技术服务费",
          prop: "xinxi",
        },
        {
          label: "应付信息费",
          prop: "maoshouru",
        },
        {
          label: "项目提成",
          prop: "ticheng",
        },
      ]),
      pickerOptions: Object.freeze({
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      }),
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectList();
      this.getCompanyList();
      this.getList();
      this.getMoney();
    },

    async getProjectList() {
      const { rows } = await querySelectList({ isEnable: "Y" });
      this.projectList = rows;
    },
    getMoney() {
      this.totalGross = {
        totalGross: "23,456,789.00",
      };
    },
    async getList() {
      const { rows, total } = await projectParameter(this.queryParams);
      this.configList = rows;
      this.total = total;
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleExport() {
      this.download(
        "personnel/onboarding/export",
        {
          ...this.queryParams,
        },
        `各项目毛利率.xlsx`
      );
    },
    viewProjectDetail(value) {
      this.paramsProjectDetails = {
        relationId: value.dictCode,
        functionNode: "COMPANYBUSINESSTYPE",
        title: "COMPANY",
        projectName: "COMPANY",
      };
      this.openProjectDetails = true;
    },
  },
};
</script>
