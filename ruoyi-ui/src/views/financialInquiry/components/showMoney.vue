<template>
  <div class="h-36">
    <div class="h-3" style="background: rgb(248, 248, 249)"></div>
    <div class="flex h-28">
      <div
        v-for="(item, index) in moneyList"
        :key="index"
        class="w-64 py-6 "
      >
        <div class="flex flex-col h-full justify-between relative">
          <div style="color: #999999">{{ item.label }}</div>
          <div class="text-xl font-medium text-black">
            {{ money[item.value] }}
          </div>
          <div v-show="index!=moneyList.length-1" class="absolute right-4  h-full top-0 z-10 "  style="width:1px;background:rgb(224, 231, 237)"></div>
        </div>
        
      </div>
    </div>
    <div class="h-3" style="background: rgb(248, 248, 249)"></div>
  </div>
</template>

<script>
export default {
  name: "showMoney",
  props: {
    moneyList: {
      type: Array,
      required: true,
      default: () => [],
    },
    money: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss" scoped>

</style>