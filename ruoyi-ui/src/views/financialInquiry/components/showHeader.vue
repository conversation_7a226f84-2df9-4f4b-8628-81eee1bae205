<template>
  <div class="mb-5">
    <div class="text-xl font-bold mb-2">{{header.title}}</div>
    <div style="color: #999999" v-for="(item,index) in header.content">
      {{item}}
    </div>
  </div>
</template>

<script>
export default {
  name: "showHeader",
  props: {
    header: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
</style>