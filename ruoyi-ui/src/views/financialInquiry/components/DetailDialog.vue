<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title=""
      :visible.sync="innerValue"
      width="680px"
      @open="handleOpen"
    >
      <div>{{ title[type] }}</div>
      <MyTable :columns="columns" :source="configList"> </MyTable>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button @click="innerValue = false">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";

export default {
  mixins: [vModelMixin],
  props: {
    type: {
      type: String,
      required: true,
      default: "grossProfit",
    },
    configList: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      title: Object.freeze({
        '01':
          "毛利 = 银行账户实收金额 - 平台技术服务费 - 第三方技术服务费",
        '02':
          "净毛利 = 项目通道费收入-应付渠道信息费",
      }),
      columns:[],
      columnsInit: Object.freeze({
        '01': [
          { label: "毛利 =", prop: "resultFee",align:'right' ,width:'80'},
          { label: "银行账户实收金额 -", prop: "fee1",align:'right',width:'140' },
          { label: "平台技术服务费 -", prop: "fee2" ,align:'right',width:'130'},
          { label: "第三方技术服务费", prop: "fee3" ,align:'right',width:'130'},
        ],
        '02': [
          { label: "净毛利 =", prop: "resultFee",width:'80' },
          { label: "项目通道费收入 -", prop: "fee1",align:'right',width:'140' },
          { label: "应付渠道信息费", prop: "fee4" ,align:'right',width:'130'},
        ],
      }),
    };
  },

  mounted() {

  },
  methods: {
    handleOpen(){
      this.init();
    },
    async init() {
      this.columns=this.columnsInit[this.type];
    },
  },
};
</script>


