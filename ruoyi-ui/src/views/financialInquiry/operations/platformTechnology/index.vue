<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="项目名称" class="form-item" prop="projectIds">
        <el-select
          multiple
          filterable
          v-model="queryParams.projectIds"
          placeholder="请选择项目名称"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.projectList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="担保公司" class="form-item" prop="guaranteeIds">
        <el-select
          v-model="queryParams.guaranteeIds"
          multiple
          filterable
          placeholder="请选择担保公司"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.custNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产方" prop="assetIds">
        <el-select
          v-model="queryParams.assetIds"
          placeholder="请选择担保公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.partnerNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金方" prop="fundIds">
        <el-select
          v-model="queryParams.fundIds"
          placeholder="请选择资金方"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.fundNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="其他公司" prop="others">
        <el-select
          v-model="queryParams.others"
          placeholder="请选择其他公司"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyList.otherNoList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="收款单位" prop="beneficiarys">
        <el-select
          v-model="queryParams.beneficiarys"
          placeholder="请选择收款单位"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in beneficiaryList"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-model="queryParams.time"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <MoreSearch modelCode="DATAREPORT" :params="queryParams" v-show="showMoreSearch"></MoreSearch>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
         <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
            >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
          >
      </el-form-item>
    </el-form>
    <ShowMoney :moneyList="totalPlatformList" :money="totalPlatform" />
    <el-button
      v-hasPermi="['fc:platformTecService:export']"
      type="warning"
      plain
      class="mb-2"
      icon="el-icon-download"
      size="mini"
      @click="handleExports"
      >导出</el-button
    >
    <MyTable :columns="columns" :source="configList" :queryParams="queryParams">
      <template #h_totalAmt="{ record }">
        合计
        <el-tooltip
          class="item"
          effect="dark"
          content="平台技术服务费 = 科目余额表中的（应付账款2-项目名称-单位名称）借方发生额"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>

      <template #opertion="{ record }">
        <el-button type="text" @click="viewProjectDetail(record)"
          >查看明细</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <ProjectDetails
      :params="paramsProjectDetails"
      v-model="openProjectDetails"
      :columnsRecord="columnsProjectDetails"
      :api="platformTecServiceDetail"
    />
  </div>
</template>

<script>
import {
  platformTecServiceList,
  platformTecServiceExport,
  platformTecServiceDetail,
  getbeneficiary,
} from "@/api/financialInquiry/operations";
import config from "./components/config";
import XEUtils from "xe-utils";
import ShowMoney from "../../components/showMoney";
import ShowHeader from "../../components/showHeader";
import companyForm from "@/views/financialInquiry/mixin/companyForm";
import exportTable from "@/views/financialInquiry/mixin/exportTable";
import form from "@/views/financialInquiry/mixin/form";

export default {
  name: "PlatformTechnology",
  components: { ShowMoney, ShowHeader },
  mixins: [companyForm, exportTable,form],

  data() {
    return {
      ...config,
      columnsExport: [],
      totalPlatformList: [{ label: "金额合计", value: "totalPlatform" }],
      totalPlatform: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectIds: undefined,
        guaranteeIds: undefined,
        assetIds: undefined,
        fundIds: undefined,
        others: undefined,
        beneficiarys: undefined,
        time: [
          `${new Date().getFullYear()}-01-01`,
          XEUtils.toDateString(new Date(), "yyyy-MM-dd"),
        ],
      },
      beneficiaryList: [],
      configList: [],
      total: 0,
      openProjectDetails: false,
      paramsProjectDetails: {},
      platformTecServiceDetail,
      showMoreSearch:false
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      
      this.getbBeneficiaryList();
      this.getCompanyList();
      this.getList();
    },
    async getbBeneficiaryList() {
      const data = await getbeneficiary({ type: 1 });
      this.beneficiaryList=data;
    },
    getMoney(map) {
      this.totalPlatform = {
        totalPlatform: map.sumTotalAmt.toLocaleString("zh-CN"),
      };
    },
    getColumns(rows) {
      const startColumns = [
        { label: "项目名称", prop: "projectName",width: 300,fixed:'left'},
        { label: "合计", prop: "totalAmt",isHSlot: true, width: 150,fixed:'left' },
      ];
      const endColumns = [
        { label: "操作", key: "opertion",width: "100px", },
      ];
      this.monthColums = [];
      Object.keys(rows[0]?.amtMap || {}).forEach((item) => {
        this.monthColums.push({
          label: item,
          prop: item,
          width: 150,
        });
      });
      this.columns = startColumns.concat(this.monthColums).concat(endColumns);
    },
    async getList() {
      const { rows, total, map } = await platformTecServiceList(
        this.getParams()
      );
      this.configList = this.handlerConfigList(rows);
      this.total = total;
      this.getColumns(rows);
      this.getMoney(map);
    },
    getParams(exports = false) {
      const params = XEUtils.clone(this.queryParams);
      if (params.time) {
        params.startTime = params.time[0]?.replace(/-/g, ".");
        params.endTime = params.time[1]?.replace(/-/g, ".");
      }
      if (!exports) {
        const toSting = [
          "projectIds",
          "guaranteeIds",
          "assetIds",
          "fundIds",
          "others",
          "beneficiarys",
        ];
        toSting.forEach((item) => {
          params[item] = params[item]?.join() || undefined;
        });
      }
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      delete params.time;
      return params;
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.moreSearch=undefined
      this.handleQuery();
    },
    async handleExports() {
      const params = XEUtils.clone(this.getParams(true), true);
      delete params.pageNum;
      delete params.pageSize;
      const rows = await platformTecServiceExport(params);
      const exportData = this.handlerConfigList(rows);
      this.columnsExport = this.columns.filter(
        (item) => item.key != "opertion"
      );
      this.handleExport(exportData, "平台技术服务费");
    },
    viewProjectDetail(value) {
      this.paramsProjectDetails = {
        projectId: value.projectId,
        projectName: value.projectName,
        beneficiarys: this.queryParams.beneficiarys,
        startTime:
          this.queryParams.time && this.queryParams.time[0]?.replace(/-/g, "."),
        endTime:
          this.queryParams.time && this.queryParams.time[1]?.replace(/-/g, "."),
      };
      this.openProjectDetails = true;
    },
  },
};
</script>
