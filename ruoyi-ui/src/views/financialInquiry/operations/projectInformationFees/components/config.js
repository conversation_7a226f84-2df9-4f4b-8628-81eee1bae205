export default {
  header: Object.freeze({
    title: "应付项目信息费明细",
    content: [
      "数据来源：智慧财务系统-科目余额表。按渠道方展示应付信息费与明细",

    ],
  }),
  columns: Object.freeze([
    { label: "渠道方名称", prop: "channelName",width: "200px", },

    {
      label: "期初金额",
      prop: "initialAmt",
      width: "200px",
    },
    {
      label: "增加",
      prop: "addAmt",
      isHSlot: true,
      width: "200px",
    },
    {
      label: "减少",
      prop: "subtractAmt",
      isHSlot: true,
      width: "200px",
    },
    {
      label: "余额",
      prop: "balanceAmt",
      isHSlot: true,
      width: "200px",
    },

    {
      label: "操作",
      key: "opertion",
      width: "100px",
    },
  ]),
  columnsProjectDetails: Object.freeze([
    { label: "记账日期", prop: "dataDay" },
    {
      label: "项目名称",
      prop: "projectName",
      width:'160px'
    },
    {
      label: "增加",
      prop: "addAmt",
    },
    {
      label: "减少",
      prop: "subtractAmt",
    },
  ]),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  }),
};
