<template>
  <commonPayment :header="header" :columns="columns" :columnsHeader="columnsHeader" :hasPermiAdd="hasPermiAdd" :hasPermiExport="hasPermiExport" typePay="compensationpayment"> </commonPayment>
</template>

<script>
import commonPayment from "@/views/financialInquiry/components/commonPayment";
export default {
  name: "CompensationPayment",
  components: { commonPayment },

  data() {
    return {
      hasPermiAdd:["fc:manaulInput:add"],
      hasPermiExport:["fc:compensate:export"],
      header: Object.freeze({
        title: "代偿款",
        content: [
          "数据来源：智慧财务系统-科目余额表",
          "收到代偿款：科目余额表中的  (负债类-收到代偿款)",
          "实际代偿款：科目余额表中的  (资产类-实际代偿款)",
          "余额 = 期初金额 + 收到代偿款 - 实际代偿款",
          "累计代偿款：截止统计期末历史所有实际代偿款与手动录入代偿款之和",
        ],
      }),
      columns: Object.freeze([
        { label: "项目名称", prop: "projectName", minWidth: "500px" },
        {
          label: "期初金额",
          prop: "initialAmt",
          width: "200px",
        },
        {
          label: "收到代偿款",
          prop: "receiveCompensateAmt",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "实际代偿款",
          prop: "actCompensateAmt",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "余额",
          prop: "balanceAmt",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "手工录入代偿款",
          prop: "miCompensateAmt",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "累计代偿款",
          prop: "accumCompensateAmt",
          isHSlot: true,
          width: "200px",
        },

        {
          label: "操作",
          key: "opertion",
          width: "200px",
        },
      ]),
      columnsHeader:Object.freeze({
        receiveCompensateAmt:'收到代偿款 = 科目余额表中的（负债类-收到代偿款-项目名称）',
        actCompensateAmt:'实际代偿款 = 科目余额表中的（资产类-实际代偿款-项目名称）',
        balanceAmt:'余额 = 上期末余额 + 收到代偿款 - 实际代偿款',
        miCompensateAmt:'通过 [手工录入] 方式录入的代偿款金额合计',
        accumCompensateAmt:'截止统计期末，历史所有实际代偿款与手动录入代偿款之和',
      })
    };
  },

  created() {},
  methods: {},
};
</script>
