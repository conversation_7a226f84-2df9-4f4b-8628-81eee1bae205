export default {
  header: Object.freeze({
    title: "平台方保证金",
    content: [
      "数据来源：智慧财务系统-科目余额表。按项目维度展示平台方保证金信息",
      "收到保证金：科目余额表中的 (负债类-存入保证金-项目名称)，贷方发生额",
      "退回保证金：科目余额表中的 (负债类-存入保证金-项目名称)，借方发生额",
      "余额 = 期初金额 + 收到保证金 - 退回保证金",
    ],
  }),
  columns: Object.freeze([
    { label: "项目名称", prop: "projectName" ,width: "400px",},
    {
      label: "期初金额",
      prop: "initialAmt",
      width: "200px",
    },
    {
      label: "收到保证金",
      prop: "receiveAmt",
      isHSlot: true,
      width: "200px",
    },
    {
      label: "退回保证金",
      prop: "backAmt",
      isHSlot: true,
      width: "200px",
    },
    {
      label: "余额",
      prop: "balanceAmt",
      isHSlot: true,
      width: "200px",
    },
    {
      label: "操作",
      key: "opertion",
      width: "100px",
    },
  ]),
  columnsProjectDetails: Object.freeze([
    { label: "记账日期", prop: "dataDay" },
    {
      label: "收到保证金",
      prop: "receiveAmt",
    },
    {
      label: "退回保证金",
      prop: "backAmt",
    },
  ]),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  }),
};
