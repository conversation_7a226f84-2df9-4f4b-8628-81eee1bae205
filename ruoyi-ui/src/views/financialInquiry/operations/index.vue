<template>
  <CommonIndex :configList="configList">
    运营部定制报表，点击名称查看详情
  </CommonIndex>
</template>

<script>
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import CommonIndex from "../components/commonIndex";
export default {
  name: "Operations",
  components: { CommonIndex },
  data() {
    return {
      configList:[],
      configListInit: Object.freeze([
        {
          dataIndicators: "平台方保证金",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按项目维度展示平台方保证金",
          ],
          explainBottom: [
            {
              title: "收到保证金:",
              content: "科目余额表中的 (负债-存入保证金)，贷方发生额",
            },
            {
              title: "退回保证金:",
              content:
                "科目余额表中的 (负债类-存入保证金-项目名称)，借方发生额",
            },
            {
              title: "余额=",
              content: "期初金额 + 收到保证金 - 退回保证金",
            },
          ],
          path: "/financialInquiryOther/margin",
          permi: ["fc:platformMargin"],
        },
        {
          dataIndicators: "资金方保证金",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按项目维度展示资金方保证金",
          ],
          explainBottom: [
            {
              title: "收到保证金:",
              content: "科目余额表中的 (资产类-银行存款-账户名-保证金账户-项目名称)借方发生额 + (资产类-存出保证金-项目名称)借方发生额",
            },
            {
              title: "退回保证金:",
              content:
                "科目余额表中的 (资产类-银行存款-账户名-保证金账户-项目名称)贷方发生额 + (资产类-存出保证金-项目名称)贷方发生额",
            },
            {
              title: "余额=",
              content: "期初金额 + 收到保证金 - 退回保证金",
            },
          ],
          path: "/financialInquiryOther/fundingMargin",
          permi: ["fc:fundMargin"],
        },
        {
          dataIndicators: "代偿款",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按项目维度展示代偿款",
          ],
          explainBottom: [
            {
              title: "收到代偿款:",
              content: "科目余额表中的  (负债类-收到代偿款)",
            },
            {
              title: "实际代偿款:",
              content: "科目余额表中的  (资产类-实际代偿款)",
            },
            {
              title: "代偿款余额=",
              content: "期初金额 + 收到代偿款 - 实际代偿款",
            },
            {
              title: "累计代偿款=",
              content: "历史所有实际代偿款与手动录入代偿款之和",
            },
          ],
          path: "/financialInquiryOther/compensationPayment",
          permi: ["fc:compensate"],
        },
        {
          dataIndicators: "项目收入",
          explainTop: [
            "数据来源：智慧财务系统。按项目分别展示单位时间内项目的收入毛利等情况",
          ],
           explainBottom: [
            {
              title: "收入=",
              content: "银行账户实收金额",
            },
            {
              title: "毛利=",
              content: "银行账户实收金额 - 平台技术服务费 - 第三方技术服务费",
            },
            {
              title: "净毛利=",
              content: "银行账户实收金额 - 平台技术服务费 - 第三方技术服务费 - 应付渠道信息费",
            },
          ],
          path: "/financialInquiryOther/channelFee",
          permi: ["fc:operation:projectIncome:list"],
        },
        {
          dataIndicators: "平台技术服务费",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按项目维度展示平台技术服务费",
          ],
          explainBottom: [
            {
              title: "平台技术服务费:",
              content: "科目余额表中的（应付账款2-项目名称-单位名称）借方发生额",
            },
          ],
          path: "/financialInquiryOther/platformTechnology",
          permi: ["fc:platformTecService"],
        },
        {
          dataIndicators: "第三方技术服务费",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按项目维度展示第三方技术服务费",
          ],
          explainBottom: [
            {
              title: "第三方技术服务费:",
              content: "科目余额表中的 （应付账款3-项目名称-单位名称）借方发生额",
            },
          ],
          path: "/financialInquiryOther/thirdPartyTechnology",
          permi: ["fc:thirdTecService"],
        },
        {
          dataIndicators: "线下还款",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按项目维度展示线下还款金额",
          ],
          explainBottom: [
            {
              title: "收到线下还款:",
              content: "科目余额表中的  (负债类-其他应付款贷方发生额)",
            },
            {
              title: "支付线下还款:",
              content: "科目余额表中的  (负债类-其他应付款借方发生额)",
            },
          ],
          path: "/financialInquiryOther/offlineRepayment",
          permi: ["fc:offlineRepay"],
        },
        {
          dataIndicators: "应付项目信息费明细",
          explainTop: [
            "数据来源：智慧财务系统-科目余额表。按渠道方维度展示应付信息费与明细",
          ],

          path: "/financialInquiryOther/projectInformationFees",
          permi: ["fc:payableInfoFee"],
        },
      ]),
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
       this.configList = this.configListInit.filter((item) =>
        checkPermi(item.permi)
      );
    },
  },
};
</script>
