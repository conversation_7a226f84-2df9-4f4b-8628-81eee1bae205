<template>
  <CommonRevenue :header="header" type="ceo"></CommonRevenue>
</template>

<script>
import CommonRevenue from "../../components/commonRevenue";
export default {
  name: "ProjectRevenue",
  components: { CommonRevenue },

  data() {
    return {
      header: Object.freeze({
        title: "项目收入",
        content: [
          "数据来源：智慧财务系统。按项目分别展示单位时间内项目的收入毛利等情况",
          "收入 = 项目通道费收入（数据取自主营业务收入-项目名称-贷方发生额）",
          "净毛利 = 项目通道费收入-应付渠道信息费",
        ],
      }),
    };
  },
  created() {},
  methods: {},
};
</script>
