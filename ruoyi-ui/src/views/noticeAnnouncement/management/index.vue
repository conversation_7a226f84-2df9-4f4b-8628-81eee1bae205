<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          @click="goAdd"
          v-hasPermi="['noticeAnnouncement:management:add']"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >新建</el-button
        >
      </div>
    </div>
    <el-tabs
      v-model="queryParams.publishStatus"
      type="card"
      @tab-click="handleClick"
    >
      <el-tab-pane label="已发布公告" name="1"></el-tab-pane>
      <el-tab-pane label="未发布公告" name="0"></el-tab-pane>
    </el-tabs>
    <MyTable
      ref="table"
      :showIndex="true"
      :columns="columns"
      :source="configList"
    >
      <template #noticeName="{ record }">
        <el-button type="text" @click="goView(record)">{{
          record.noticeName
        }}</el-button>
      </template>
      <template #operate="{ record }">
        <div v-show="queryParams.publishStatus == '1'">
          <el-button
            type="text"
            v-show="record.isHeader == 0"
            @click="cahngStatus(record, 'isHeaderTrue')"
            >置顶</el-button
          >
          <el-button
            type="text"
            v-show="record.isHeader == 1"
            @click="cahngStatus(record, 'isHeaderFalse')"
            >取消置顶</el-button
          >
          <el-button
            type="text"
            v-show="record.isEmphasis == 0"
            @click="cahngStatus(record, 'isEmphasisTrue')"
            >设为重点</el-button
          >
          <el-button
            type="text"
            v-show="record.isEmphasis == 1"
            @click="cahngStatus(record, 'isEmphasisFalse')"
            >取消重点</el-button
          >
          <el-button type="text" @click="cahngPublish(record, 0)"
            >撤回</el-button
          >
        </div>

        <div v-show="queryParams.publishStatus == '0'">
          <el-button  v-hasPermi="['noticeAnnouncement:management:update']" type="text" @click="goUpdate(record)">修改</el-button>
          <el-button type="text" @click="cahngPublish(record, 1)"
            >发布</el-button
          >
          <el-button
            v-hasPermi="['noticeAnnouncement:management:delete']"
            type="text"
            style="color: #f56c6c"
            @click="handleDelete(record)"
            >删除</el-button
          >
        </div>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import {
  getNoticeMainNoticeList,
  updateNoticeMainNotice,
  updatePublishRevocation,
  deleteNotice,
  getDataManageList
} from "@/api/notice/management";
import { systemDataManage, systemDataManageList } from "@/api/notice/dataSet";
import { renameField } from "@/utils";
import config from "./components/config";
export default {
  name: "noticeManagement",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        createTime: undefined,
        noticeType: undefined,
        noticeName: undefined,
        publishStatus: "1",
      },
      total: 0,
      configList: [],
      flatData: [],
    };
  },

  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getSystemDataManageList();
      this.getTypes();
      this.getList();
      this.getColumns();
    },
    async getSystemDataManageList() {
      let data = await systemDataManageList();
      this.flatData = XEUtils.toTreeArray(data.rows, {
        children: "fPiattaformas", // 指定子节点字段名
        clear: true,
      });
    },
    async getList() {
      const { rows, total } = await getNoticeMainNoticeList(this.queryParams);
      rows.forEach((item) => {
        this.flatData.forEach((item1) => {
          if (item1.id == item.noticeType) {
            item.noticeTypeName = item1.dataName;
          }
        });
      });
      this.configList = rows;
      this.total = total;
    },
    async getTypes() {
      let { rows } = await getDataManageList({ firstDataCode: 'TZGGLB' });
      rows.forEach((rootNode) =>
        renameField(rootNode, "fPiattaformas", "children")
      );
      rows.forEach((rootNode) => renameField(rootNode, "dataName", "label"));
      this.formColumns[1].options = rows[0]?.children;;
    },
    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getColumns() {
      if (this.queryParams.publishStatus == "0") {
        this.columns = this.columnsInit.filter(
          (item) => item.prop != "version"
        );
      } else {
        this.columns = XEUtils.clone(this.columnsInit);
      }
    },
    handleClick() {
      this.getColumns();
      this.handleQuery();
    },
    async cahngStatus(record, type) {
      const isHeaderObj = {
        isHeaderTrue: 1,
        isHeaderFalse: 0,
      };
      const isEmphasisObj = {
        isEmphasisTrue: 1,
        isEmphasisFalse: 0,
      };
      const isHeader = isHeaderObj[type];
      const isEmphasis = isEmphasisObj[type];
      const params = {
        isHeader,
        isEmphasis,
        id: record.id,
      };
      await updateNoticeMainNotice(params);
      this.$modal.msgSuccess("操作成功");
      this.getList();
    },
    async cahngPublish(record, type) {
      await updatePublishRevocation({ ...record, publishStatus: type });
      this.$modal.msgSuccess("操作成功");
      this.getList();
    },
    goAdd() {
      this.$router.push({
        path: `/noticeAnnouncementOther/management/add`,
        query: {
          title: "新增通知公告",
        },
      });
    },
    goUpdate(row) {
      this.$router.push({
        path: `/noticeAnnouncementOther/management/${row.id}`,
        query: {
          title: "修改通知公告",
        },
      });
    },
    goView(row) {
      this.$router.push({
        path: `/noticeListDetail/${row.id}`,
        query: {
          title: "通知公告详情",
        },
      });
    },

    handleDelete(row) {
      const ids = row.id;
      const idNames = row.noticeName;
      this.$modal
        .confirm(
          `只有在未发布公告的内容才可进行删除，并且需要弹框提醒确定要删除【${idNames}】吗？`
        )
        .then(function () {
          return deleteNotice(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
