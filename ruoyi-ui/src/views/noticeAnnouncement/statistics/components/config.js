export default {
  formColumns: Object.freeze([
    {
      label: "创建日期",
      prop: "sxCreateTime",
      type: "datePicker",
      dateType: "date",
      placeholder: "请选择创建日期",
    },
    {
      label: "类型",
      prop: "noticeType",
      type: "treeselect",
      options: [],
      filterable: true,
      placeholder: "请选择类型",
    },
    {
      label: "通知名称",
      prop: "noticeName",
      type: "input",
      placeholder: "请输入通知名称",
    },
  ]),

  columns: Object.freeze([
    {
      label: "通知名称",
      key: "noticeName",
      minWidth: "150px",
    },
    { label: "类型", prop: "noticeTypeLabel", minWidth: "100px" },
    { label: "创建人", prop: "createNickName", minWidth: "100px" },
    { label: "所属公司", prop: "companyShortName", minWidth: "100px" },
    { label: "创建时间", prop: "createTime", minWidth: "100px" },
    { label: "操作", key: "operate", width: "120px" },
  ]),
  columnsDetail: Object.freeze([
    {
      label: "阅读人",
      prop: "nickName",
      minWidth: "120px",
    },
    { label: "阅读时间", prop: "createTime", minWidth: "150px" },
    { label: "部门", prop: "deptName", minWidth: "120px" },
    { label: "所属公司", prop: "companyShortName", minWidth: "120px" },
    { label: "版本", prop: "version", minWidth: "100px" },
    { label: "客户端IP", prop: "operIp", minWidth: "120px" },
  ]),
};
