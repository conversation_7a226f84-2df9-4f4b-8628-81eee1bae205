<template>
  <div class="p-5">
    <div class="text-center pt-8 pb-2 relative" style="border: 1px solid #ccc">
      <div class="text-3xl font-black text-black">{{ content.noticeName }}</div>
      <div class="text-base w-full text-center">
        类型
        <span class="font-black text-black ml-2">{{
          content.noticeTypeName
        }}</span
        ><span
          style="
            width: 1px;
            height: 20px;
            background: #ccc;
            display: inline-block;
          "
          class="mx-6 mt-4"
        ></span>
        创建人
        <span class="font-black text-black ml-2">{{
          content.createrNickName
        }}</span
        ><span
          style="
            width: 1px;
            height: 20px;
            background: #ccc;
            display: inline-block;
          "
          class="mx-6 mt-4"
        ></span>
        创建时间
        <span class="font-black text-black ml-2">{{ content.createTime }}</span>
      </div>
      <div class="absolute right-2 top-2 cursor-pointer" @click="cancel">
        <i class="el-icon-close"></i>
      </div>
    </div>
    <Editor
      ref="myEditor"
      minHeight="500"
      :readOnly="true"
      :value="content.content"
    ></Editor>
    <div
      class="pl-4 pb-4 flex items-center"
      style="border: 1px solid #ccc; border-top: none"
    >
      <div v-for="(item, index) in files" :key="index">
        <span class="mr-4">{{ item.fileName }}</span>
        <el-button
          type="text"
          v-show="
            item.fileName.endsWith('.jpg') ||
            item.fileName.endsWith('.jpeg') ||
            item.fileName.endsWith('.png') ||
            item.fileName.endsWith('.gif') ||
            item.fileName.endsWith('.pdf')
          "
          @click="handlePreview(item)"
          >查看</el-button
        >
        <el-button type="text" @click="handleDownload(item)">下载</el-button>
      </div>
    </div>
    <el-tabs
      v-model="queryParams.rdType"
      type="card"
      @tab-click="handleQuery"
      class="mt-4"
    >
      <el-tab-pane
        label="阅读记录"
        name="1"
        v-if="checkPermi(['noticeAnnouncement:statistics:view'])"
      ></el-tab-pane>
      <el-tab-pane
        label="下载记录"
        name="2"
        v-if="checkPermi(['noticeAnnouncement:statistics:download'])"
      ></el-tab-pane>
    </el-tabs>
    <el-button
      type="warning"
      plain
      icon="el-icon-download"
      size="mini"
      class="my-2"
      @click="handleExport"
      >导出</el-button
    >
    <MyTable
      ref="table"
      :showIndex="true"
      :columns="columnsDetail"
      :source="configList"
    >
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "./config";
import privew from "@/mixin/privew";
import { readDownloadHistoryHistoryList } from "@/api/notice/statistics";
import { noticeMainNotice, systemDataManageList } from "@/api/notice/dataSet";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  name: "noticeStatisticsDetail",
  mixins: [privew],

  data() {
    return {
      ...config,
      content: {},
      files: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        rdType: "1",
        noticeId: undefined,
      },
      configList: [],
      total: 0,
      checkPermi,
    };
  },

  mounted() {
    this.getActiveName();
    this.getInitForm();
    this.getList();
  },
  methods: {
    getActiveName() {
      if (checkPermi(["noticeAnnouncement:statistics:view"])) {
        this.queryParams.rdType = "1";
        return;
      }
      if (checkPermi(["noticeAnnouncement:statistics:download"])) {
        this.queryParams.rdType = "2";
        return;
      }
    },
    async getInitForm() {
      this.queryParams.noticeId = this.$route.params.id;
      let { rows } = await systemDataManageList();
      const flatData = XEUtils.toTreeArray(rows, {
        children: "fPiattaformas", // 指定子节点字段名
        clear: true,
      });
      const { data } = await noticeMainNotice(this.$route.params.id);
      flatData.forEach((item1) => {
        if (item1.id == data.noticeType) {
          data.noticeTypeName = item1.dataName;
        }
      });
      this.content = data;
      this.files = data.noticesFileList;
    },
    async getList() {
      const { rows, total } = await readDownloadHistoryHistoryList(
        this.queryParams
      );
      this.configList = rows;
      this.total = total;
    },
    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleExport() {
      const exportUrl = {
        1: "readDownloadHistory/history/readExport",
        2: "readDownloadHistory/history/downloadExport",
      };
      let params = XEUtils.clone(this.queryParams);
      delete params.pageSize;
      delete params.pageNum;
      this.download(
        exportUrl[this.queryParams.rdType],
        params,
        `${this.queryParams.rdType == "1" ? "阅读记录" : "下载记录"}.xlsx`,
        "get"
      );
    },
    cancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ql-toolbar {
  display: none;
}
::v-deep .ql-container {
  border-bottom: none;
}
</style>