<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <MyTable
      ref="table"
      :showIndex="true"
      :columns="columns"
      :source="configList"
    >
      <template #noticeName="{ record }">
        <el-button type="text" @click="goView(record, 'view')">{{
          record.noticeName
        }}</el-button>
      </template>
      <template #operate="{ record }">
        <el-button @click="goView(record, 'view')" type="text"
          >查看阅读记录</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNumber"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getNoticeMainNoticeIndexNotice } from "@/api/notice/statistics";
import { systemDataManage } from "@/api/notice/dataSet";
import { renameField } from "@/utils";

import config from "./components/config";
export default {
  name: "Statistics",

  data() {
    return {
      ...config,
      queryParams: {
        pageNumber: 1,
        pageSize: 10,
        createTime: undefined,
        noticeType: undefined,
        noticeName: undefined,
      },
      total: 0,
      configList: [],
      flatData: [],
    };
  },

  created() {
    this.init();
  },
  methods: {
    async init() {
      this.getTypes();
      this.getList();
    },
  
    async getList() {
      const params=XEUtils.clone(this.queryParams,true);
      delete params.pageNum;
      const { rows, total } = await getNoticeMainNoticeIndexNotice(params);
     
      this.configList = rows;
      this.total = total;
    },
    async getTypes() {
      const { rows } = await systemDataManage({ dataCode: null });
      rows.forEach((rootNode) =>
        renameField(rootNode, "fPiattaformas", "children")
      );
      rows.forEach((rootNode) => renameField(rootNode, "dataName", "label"));
      this.formColumns[1].options = rows;
    },
    handleQuery(value) {
      this.queryParams.pageNumber = 1;
      this.getList();
    },

    goView(row, type) {
      this.$router.push({
        path: `/noticeAnnouncementOther/statistics/${row.id}`,
        query: {
          title: "查看阅读记录",
        },
      });
    },
  },
};
</script>
