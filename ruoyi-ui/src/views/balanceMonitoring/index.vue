<template>
  <div class="p-5">
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
      <template #totalMargin="{ record }">
        <el-button type="text" @click="openDialog(record)">{{
          record.totalMargin 
        }}</el-button>
      </template>
      <template #occupancyRatioStr="{ record }">
        <div
          :style="{ color: record.warningFlag ==1 ? 'red' : '' }"
        >
          {{ record.occupancyRatioStr }}
        </div>
      </template>

      <template #operate="{ record }">
        <el-button v-hasPermi="['balanceMonitoring:add']" type="text" @click="handleAdd(record)"
          >追加账户保证金</el-button
        >
        <el-button v-hasPermi="['balanceMonitoring:update']" type="text" @click="handleUpdate(record)"
          >预警修改</el-button
        >
      </template>
    </MyTable>
    <DetailDialog v-model="open" :form="form" />
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
  
  <script>
import XEUtils from "xe-utils";
import DetailDialog from "./components/DetailDialog.vue";
import { getMonitorList } from "@/api/balanceMonitoring";
import config from "./components/config";
export default {
  name: "BalanceMonitoring",
  components: { DetailDialog },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      configList: [],
      open: false,
      form: {},
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    async getList() {
      const { rows, total } = await getMonitorList(this.queryParams);
      this.configList = this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(row) {
      const newRow = XEUtils.clone(row, true);
      const addUnit = [
        "availableLoanAmount",
        "unappropriatedAmount",
        "takenUpAmount",
        "loanBalance",
        "totalMargin",
        "yesterdayLentActualAmount",
        "monthLentActualAmount",
      ];
      newRow.forEach((item) => {
        addUnit.forEach((item1) => {
          item[item1] = item[item1]?.toLocaleString("zh-CN");
        });
      });
      return newRow;
    },
    openDialog(record) {
      this.form = record;
      this.open = true;
    },
    handleAdd(record) {
      this.$router.push({
        path: "balanceMonitoringOther/marginCalls",
        query: { ...record },
      });
    },
    handleUpdate(record) {
      this.$router.push({
        path: "balanceMonitoringOther/warningModification",
        query: { ...record },
      });
    },
  },
};
</script>
  <style lang="scss" scoped>
</style>
  