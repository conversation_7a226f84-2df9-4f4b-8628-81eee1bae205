<template>
  <div class="app-container">
    <div class="form-container">
      <h2>预警修改</h2>
      <div class="form-item">
        <div class="label font-bold">项目名称：</div>
        <div class="content">
          {{ params.projectName }}
        </div>
      </div>

      <h2>超过以下值时开始预警：</h2>
      <div class="form-item">
        <div class="label font-bold">机构保证金占用比例（%）：</div>
        <div class="content">
          <el-input
            style="width: 300px"
            v-model.trim="params.ratioMax"
            placeholder="请输入比例"
            @input="changeInput($event, 'ratioMax')"
            @blur="handleBlur"
          />
          <div style="font-size:12px;color:red">说明：机构保证金占用比例=（当前在贷余额*保证金比例）/保证金总金额 *100%</div>
        </div>
      </div>

      <div class="button-group">
        <el-button @click="back" size="mini">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          size="mini"
          :disabled="!params.ratioMax"
          >确认修改</el-button
        >
      </div>
    </div>
  </div>
</template>
  
<script>
import { monitorUpdateWarning, onitorGetRate } from "@/api/balanceMonitoring";
import { decimal, floatMultiply } from "@/utils";
export default {
  name: "WarningModification",
  data() {
    return {
      params: {
        projectName: "",
        ratioMax: "",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      // 获取项目信息
      // 这里应该添加获取项目信息的API调用
      this.getParams();
    },
    async getParams() {
      this.params.ratioMax =
        String(floatMultiply(100, this.$route.query.ratioMax)) || "80";
      this.params.projectName = this.$route.query.projectName;
      this.params.projectCode = this.$route.query.projectId;
    },
    changeInput(value, itemValue) {
      this.params[itemValue] = decimal(value);
    },
    handleBlur() {
      // 处理失焦事件，自动补充小数点后两位
      if (!this.params.ratioMax) {
        return;
      }
      // 如果未输入小数点，自动补充.00
      if (!String(this.params.ratioMax).includes(".")) {
        this.params.ratioMax = this.params.ratioMax + ".00";
      } else {
        // 如果有小数点，确保有两位小数
        const parts = this.params.ratioMax.split(".");
        if (parts.length > 1) {
          // 如果小数部分少于2位，补充到2位
          if (parts[1].length === 0) {
            this.params.ratioMax = parts[0] + ".00";
          } else if (parts[1].length === 1) {
            this.params.ratioMax = parts[0] + "." + parts[1] + "0";
          }
        }
      }
    },
    back() {
      const obj = { path: this.$route.path };
      this.$tab.closePage(obj);
      this.$router.push({ path: "/balanceMonitoring" });
    },
    async submitForm() {
      await monitorUpdateWarning(this.params);
      this.$message.success("预警阈值修改成功！");
      this.back();
    },
  },
};
</script>

<style lang="less" scoped>
.app-container {
  padding: 20px;
  .form-container {
    max-width: 700px;
    margin: 0 auto;
    background-color: #fff;
    padding: 30px;

    h2 {
      margin-bottom: 20px;
      font-weight: bold;
    }

    .form-item {
      display: flex;
      margin-bottom: 20px;

      .label {
        width: 220px;
        text-align: right;
        padding-right: 2px;
        line-height: 40px;
      }

      .content {
        flex: 1;
        line-height: 40px;
        .input-tip {
          margin-top: 10px;
          line-height: 1.5;
        }
      }
    }
    .section-title {
      font-weight: bold;
      margin: 20px 0;
    }
  }
  .button-group {
    width: 180px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    margin-top: 40px;
  }
}
</style>