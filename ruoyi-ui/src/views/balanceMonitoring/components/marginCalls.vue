<template>
  <div class="app-container">
    <div class="form-container">
      <MyForm
        v-model="params"
        label-width="220px"
        :columns="formColumns"
        formType="form"
        :rules="rules"
      >
        <template #marginRate>
          <el-form-item label="保证金比例%:" prop="marginRati">
            <el-input
              style="width: 300px"
              @input="changeInput($event, 'marginRate')"
              v-model.trim="params.marginRate"
              placeholder="请输入保证金比例"
            />
          </el-form-item>
        </template>
        <template #operateAmount>
          <el-form-item
            label="追加保证金金额(元):"
            prop="operateAmount"
            :rules="{
              required: true,
              message: '追加保证金金额不为空',
              trigger: 'change',
            }"
          >
            <el-input
              style="width: 300px"
              @input="changeInput($event, 'operateAmount')"
              @focus="focus"
              @blur="blur"
              v-model.trim="params.operateAmount"
              placeholder="请输入追加保证金金额"
            />
          </el-form-item>
        </template>
      </MyForm>
      <div class="button-group">
        <el-button @click="back" size="mini">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          size="mini"
          :disabled="!params.operateAmount||!params.realityDate"
          >确认修改</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { monitorMarginAdd } from "@/api/balanceMonitoring";
import { decimal, floatAdd ,floatMultiply} from "@/utils";
export default {
  name: "MarginCalls",
  data() {
    return {
      params: {
        projectName: "",
        marginRate: "",
        originalAmount: "",
        operateAmount: "",
        updatedAmount: "",
        realityDate:undefined,
        productCode:undefined
      },
      isBlur:false,
      rules:Object.freeze({
        realityDate: [
            { required: true, message: '请选择保证金实际追加时间', trigger: 'change' },
          ],
      }),
      formColumns: [
        {
          label: "项目名称:",
          prop: "projectName",
          type: "divText",
          span: 24,
        },
        {
          label: "",
          prop: "marginRate",
          type: "slot",
          span: 24,
          slotName: "marginRate",
        },
        {
          label: "当前账户金额(元):",
          prop: "originalAmountShow",
          type: "divText",
          span: 24,
        },
        {
          label: "",
          prop: "operateAmount",
          type: "slot",
          span: 24,
          slotName: "operateAmount",
        },
        {
          label: "追加后账户总金额(元):",
          prop: "updatedAmountShow",
          type: "divText",
          span: 24,
        },
        {
          label: "保证金实际追加时间:",
          prop: "realityDate",
          type: "datePicker",
          span: 24,
        }
      ],
    };
  },
  created() {
    this.init();
  },
  watch: {
    "params.operateAmount": {
      handler(val) {
        if (val&&!this.isBlur) {
          this.calculateTotalAmount();
        }
      },
    },
  },
  methods: {
    init() {
      // 获取项目信息和当前账户金额
      this.getParams();
    },
    getParams() {
      this.params.projectName = this.$route.query.projectName;
      this.params.projectCode = this.$route.query.projectId;
      this.params.productCode = this.$route.query.productCode;
      this.params.marginRate = floatMultiply(100,this.$route.query.marginRate)  || "7";
      this.params.originalAmount = this.$route.query.totalMargin.replace(/,/g, '');
      this.params.originalAmountShow = this.$route.query.totalMargin;
    },
    changeInput(value, itemValue) {
      this.params[itemValue] = decimal(value);
    },
    focus(){
      this.isBlur=false;
      this.params.operateAmount=this.params.operateAmount.replace(/,/g, '');
    },
    blur(){
      console.log(this.params.operateAmount.toLocaleString("zh-CN"),123)
      this.isBlur=true;
      this.params.operateAmount=this.params.operateAmount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    },
    calculateTotalAmount() {
      const current = this.params.originalAmount || "0";
      const add = this.params.operateAmount || "0";
      this.params.updatedAmount = floatAdd(current, add);
      this.params.updatedAmountShow = floatAdd(current, add).toLocaleString("zh-CN");
    },
    back() {
      const obj = { path: this.$route.path };
      this.$tab.closePage(obj);
      this.$router.push({ path: "/balanceMonitoring" });
    },
    async submitForm() {
      // 提交表单
      const params={...this.params};
      params.operateAmount=params.operateAmount.replace(/,/g, '');
      await monitorMarginAdd(params);
      this.$message.success("账户保证金修改成功！");
      this.back();
    },
  },
};
</script>

<style lang="less" scoped>
.app-container {
  padding: 20px;
  .form-container {
    max-width: 1000px;
    margin: 0 auto;
    background-color: #fff;
    padding: 30px;
    ::v-deep .el-form{
      margin-left:100px;
    }
  }
  .button-group {
    width: 200px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    margin-top: 20px;
  }
}
</style>
