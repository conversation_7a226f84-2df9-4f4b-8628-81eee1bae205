<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="操作日志"
      :visible.sync="innerValue"
      width="1050px"
      @close="handleClose"
      @open="handleOpen"
    >
      <div>
        <MyTable
          :columns="columnsDialog"
          :showIndex="true"
          :source="configList"
          :queryParams="queryParams"
        >
        </MyTable>
        <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { getMonitorMarginList } from "@/api/balanceMonitoring";

export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: undefined,
      },
      configList: [],
      total: 0,
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
      this.queryParams.projectCode = this.form.projectId;
      this.getList();
    },

    async getList() {
      const { rows, total } = await getMonitorMarginList(this.queryParams);
      this.configList = this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(row) {
      const newRow = XEUtils.clone(row, true);
      const addUnit = ["originalAmount", "operateAmount", "updatedAmount"];
      newRow.forEach((item) => {
        addUnit.forEach((item1) => {
          if (item1 == "operateAmount") {
            item[item1] = `+${item[item1]?.toLocaleString("zh-CN")}`;
          } else {
            item[item1] = item[item1]?.toLocaleString("zh-CN");
          }
        });
      });
      return newRow;
    },
    handleClose() {},
  },
};
</script>
