<template>
  <div class="py-5 pb-28">
    <ViewDetail v-if="type == 'view'" :form="detailForm" :id="id"  @addUpdateCallBack="addUpdateCallBack" @update="updateDetail"/>
    <AddUpdate v-else :form="detailForm" :type="type" :id="id" @addUpdateCallBack="addUpdateCallBack"/>
  </div>
</template>

<script>
import AddUpdate from "./components/addUpdate";
import ViewDetail from "./components/viewDetail";
import {
  getCompanyDetail
} from "@/api/businessInformation/companyInformation";
export default {
  name: "companyInformationDetail",
  components: { AddUpdate, ViewDetail },
  props: {
    id: {
      type: [String,Number],
      required: true,
    },
    type: {
      type: [String,Number],
      required: true,
    },
  },

  data() {
    return {
      detailForm: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getDetailForm();
    },
    async getDetailForm() {
      if (this.type != "add") {
        const { data } = await getCompanyDetail(this.id);
        this.detailForm = data;
      }else{
        this.detailForm={};
      }
    },
    addUpdateCallBack(){
      this.$emit('addUpdateCallBack')
    },
    updateDetail(e){
      this.$emit("update",this.id)
    }
  },
};
</script>
