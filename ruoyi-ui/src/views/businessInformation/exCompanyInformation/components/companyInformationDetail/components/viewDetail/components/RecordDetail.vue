<template>
  <div>
    <el-dialog
      append-to-body
      title="编辑内容"
      :before-close="handleClose"
      :visible.sync="innerValue"
      width="900px"
    >
      <div style="max-height: 80vh">
        <div class="data_content">
          <div class="table">
            <div class="left">
              <div style="background: #e4e4e4"></div>
              <div>公司名称</div>
              <div>公司简称</div>
              <div>公司编码</div>
              <div>公司类型</div>
              <div>支持业务类型</div>
              <div>启用状态</div>
              <div>内部公司</div>
            </div>
            <div class="center">
              <div style="background: #e4e4e4">修改前</div>
              <div>{{ oldData && oldData.companyName }}</div>
              <div>{{ oldData && oldData.companyShortName }}</div>
              <div>{{ oldData.companyCode }}</div>
              <div>
                <span
                  v-if="
                    oldData &&
                    oldData.companyTypeMappingList &&
                    oldData.companyTypeMappingList.length > 0
                  "
                  ><span
                    v-for="item in oldData.companyTypeMappingList"
                    :key="item"
                    >{{ item.dictLabel }},</span
                  ></span
                >
              </div>
              <div>
                <span
                  v-if="
                    oldData &&
                    oldData.companyBusinessTypeMappingList &&
                    oldData.companyBusinessTypeMappingList.length > 0
                  "
                  ><span
                    v-for="item in oldData.companyBusinessTypeMappingList"
                    :key="item"
                    >{{ item.dictLabel }},</span
                  ></span
                >
              </div>
              <div>
                <div v-if="oldData">
                  {{ oldData.status == 0 ? "正常" : "停用" }}
                </div>
              </div>
              <div>
                <div v-if="oldData">
                  {{ oldData.isInside == 0 ? "否" : "是" }}
                </div>
              </div>
            </div>
            <div class="right">
              <div style="background: #e4e4e4">修改后</div>
              <div>{{ newData.companyName }}</div>
              <div>{{ newData.companyShortName }}</div>
              <div>{{ oldData.companyCode }}</div>
              <div>
                <span
                  v-if="
                    newData.companyTypeMappingList &&
                    newData.companyTypeMappingList.length > 0
                  "
                  ><span
                    v-for="item in newData.companyTypeMappingList"
                    :key="item"
                    >{{ item.dictLabel }},</span
                  ></span
                >
              </div>
              <div>
                <span
                  v-if="
                    newData.companyBusinessTypeMappingList &&
                    newData.companyBusinessTypeMappingList.length > 0
                  "
                  ><span
                    v-for="item in newData.companyBusinessTypeMappingList"
                    :key="item"
                    >{{ item.dictLabel }},</span
                  ></span
                >
              </div>
              <div>{{ newData.status == 0 ? "正常" : "停用" }}</div>
              <div>{{ newData.isInside == 0 ? "否" : "是" }}</div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="handleClose" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
    
    <script>
export default {
  dicts: ["system_product"],
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      innerValue: true,
      oldData: null,
      newData: null,
    };
  },
  watch: {},
  mounted() {
    console.log(this.data);
    this.oldData = this.data.oaApplyRecordsOldData
      ? JSON.parse(this.data.oaApplyRecordsOldData)
      : null;
    this.newData = this.data.oaApplyRecordsNewData
      ? JSON.parse(this.data.oaApplyRecordsNewData)
      : null;
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
  <style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
  }
  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    .left,
    .center,
    .right {
      width: 35%;
      > div {
        height: 30px;
        width: 100%;
        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
      }
    }
    .left div {
      font-weight: bold;
    }
    .left {
      width: 30%;
    }
  }
}
</style>
    