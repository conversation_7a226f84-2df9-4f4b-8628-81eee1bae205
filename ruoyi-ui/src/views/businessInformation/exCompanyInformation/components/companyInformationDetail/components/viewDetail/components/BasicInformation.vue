<template>
  <div>
    <el-form ref="form" :model="myForm" label-width="100px" class="content">
      <el-row>
        <el-col :span="12">
          <el-form-item label="公司名称:">
            <div>{{ myForm.companyName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司类型:">
            <div class="flex flex-wrap">
              <div
                v-for="(item, index) in myForm.companyTypeMappingList"
                :key="index"
                class="mr-3 mb-3 border border-solid rounded px-2 h-6 leading-6 relative top-2"
                :style="{
                  borderColor:item?
                  unitTypeListColor[item.dictLabel] || unitTypeListColor['其他']:'',
                  backgroundColor:item?
                  unitTypeListBackColor[item.dictLabel] ||
                  unitTypeListBackColor['其他']:'',
                }"
              >
                {{ item && item.dictLabel }}
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="公司简称:">
            {{ myForm.companyShortName
            }}<el-tooltip
              class="item"
              effect="dark"
              content="创建项目时，公司简称将作为项目名称的一部分每个项目名称由[担保公司]简称+[资产方]简称+[资金方]简称3部分组成"
              placement="top-start"
            >
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司编码:">
            <div>{{ myForm.companyCode }}</div>
          </el-form-item>
        </el-col>
       
      </el-row>
      <el-row>
         <el-col :span="12">
          <el-form-item label="支持业务类型:">
            <div class="flex flex-wrap">
              <div
                v-for="(item, index) in myForm.companyBusinessTypeMappingList"
                :key="index"
                class="mr-3 mb-3 border border-solid rounded px-2  h-6 leading-6 relative top-2"
                 style="border-color:#CCCCCC;backgroundColor:#F2F2F2;font-size:13px"
              >
                {{ item.dictLabel }}
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否内部公司:">
            {{ isInternalListEditObj[myForm.isInside] }}
            <el-tooltip
              class="item"
              effect="dark"
              content="内部公司可以用于创建组织架构,OA流程分类,财务模块账套等"
              placement="top-start"
            >
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
         <el-col :span="24">
          <el-form-item label="启用状态:">
            <dict-tag :options="statusList" :value="myForm.status" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <div class="p-2 font-bold" style="background: rgba(242, 242, 242, 1)">
        合作项目
      </div>
      <div class="py-3">系统中已创建的包含本公司的项目:{{ total }}</div>
      <MyTable
        class="w-1/3"
        :columns="columnsBasicInformation"
        :source="configList"
        :showIndex="true"
        :queryParams="queryParams"
        :show-header='false'	
      >
        <template #operate="{ record }">
          <el-button type="text" @click="handleView(record)">查看</el-button>
        </template>
      </MyTable>
      <pagination
        v-show="total > 20"
        :total="total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <div>
      <div class="p-2 font-bold mt-5" style="background: rgba(242, 242, 242, 1)">
        合作公司
      </div>
      <div class="py-3">系统中已与本公司共同创建项目的其他类型公司数量</div>
      <div>
        <div
          v-for="(item, index) in cooperativeCompanies"
          :key="index"
          class="mb-2"
        >
          {{ item.label }}
          <span class="font-bold">{{
            cooperativeCompaniesObj[item.value]
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "@/views/businessInformation/companyInformation/components/config";
import {
  getProjectByComPanyId,
  getCooperationUnit,
} from "@/api/businessInformation/companyInformation";
export default {
  name: "BasicInformation",
  props: {
    id: {
      type: [String, Number],
      required: true,
      default: "",
    },
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      myForm: {},
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      configList: [],
      cooperativeCompanies: [
        { label: "已合作担保公司:", value: "custNum" },
        { label: "已合作资产方:", value: "partnerNum" },
        { label: "已合作资金方:", value: "fundNum" },
      ],
      cooperativeCompaniesObj: {
        custNum: "",
        partnerNum: "",
        fundNum: "",
      },
    };
  },
  watch: {
    form: {
      handler(val) {
        this.myForm = XEUtils.clone(val, true);
      },
      immediate: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {

      this.getList();
      this.getCooperationUnit();
    },
    async getList() {
      const {rows,total}= await getProjectByComPanyId({...this.queryParams,id:this.id})
      this.configList = rows;
      this.total = total;
    },
    async getCooperationUnit(){
       const data= await getCooperationUnit({id:this.id})
       this.cooperativeCompaniesObj=data;
    },
    handleView(row) {
      this.$router.push({path:'/businessInformation/projectDeploy',query:{id:row.id}})
    },
  },
};
</script>
<style lang="scss" scoped>
.content{
  ::v-deep .el-row{
    margin-bottom: 0px;
  }
}
</style>
