<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="说明"
      :visible.sync="innerValue"
      width="650px"
    >
      <div>
        <div class="mb-2">
          公司是组成项目的要素，创建项目前必须先创建组成它的公司
        </div>
        <div>
          通常项目由资金方+资产方+担保公司3种类型的公司组成
        </div>
        <div class="mb-2">项目名称自动生成： [担保公司]简称 + [资产方]简称 + [资金方]简称</div>
        <div>
          部分特殊项目按以下规则组成
        </div>
        <div>产品分类为 [保函类] 的项目，由内部担保公司、资产方、受益人/其代表组成</div>
        <div>产品分类为 [不良出表类] 的项目，由内部担保公司、资金方或外部担保公司（二选一）组成</div>
        <div>产品分类为 [分保业务类] 的项目，由内部担保公司、外部担保公司组成</div>
        <div class="mb-2">产品分类为 [金融科技类] 的项目，由内部担保公司或内部科技公司（二选一）、资产方、资金方、外部担保公司组成</div>
        <div>一个公司可同时属于多个公司类型</div>
        <div>如果不存在所需的公司类型，或支持业务类型，请联系技术部进行添加</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {},
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>
