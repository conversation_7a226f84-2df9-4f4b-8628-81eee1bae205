const chineseRegex = /^[\u4e00-\u9fa5]{2,10}$/;
export default {
  columns: Object.freeze([
    { label: "业务类型", key: "dictLabel",minWidth: "200" },
    { label: "顺序号", prop: "dictSort", minWidth: "80" },
    { label: "已支持的项目", prop: "projectCount", width: "100" },
    { label: "已支持的公司", prop: "companyCount", width: "100" },
    { label: "说明", prop: "remark", minWidth: "700" },
    {
      label: "操作",
      key: "operate",
      fixed: "right",
      align: "left",
      width: "200",
    },
  ]),
  companyTabList:[
    {dictLabel:'担保公司',dictCode:''},
    {dictLabel:'资产方',dictCode:''},
    {dictLabel:'资金方',dictCode:''},
  ],
  tabListInit: [
    { label: "支持的项目",value:'project'},
    { label: "支持的担保公司",value:'guarantee'},
    { label: "支持的资产方公司",value:'assetSide'},
    { label: "支持的资金方公司",value:'funder'},
  ],
  columnsViewProject: Object.freeze([{ prop: "projectName" }]),
  columnsViewCompany: Object.freeze([{ prop: "companyName" }]),
  columnsRecord: Object.freeze([
    { label: "时间", prop: "date" },
    { label: "操作人", prop: "user" },
    { label: "操作类型", prop: "type" },
  ]),
  rules: Object.freeze({
    dictLabel: [
      {
        required: true,
        pattern: chineseRegex,
        message: "请按规则输入业务类型",
        trigger: "change",
      },
    ],
  }),
};
