<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="删除业务类型"
      :visible.sync="innerValue"
      width="350px"
      @open="handlerOpen"
    >
      <div v-if="hasRole" style="color:red">
        <div v-show="cooperationProjectNum==0">
          是否删除此业务类型？
        </div>
        <div v-show="cooperationProjectNum!=0||projectCount!=0" >
          <div v-show="cooperationProjectNum!=0">本业务类型下公司数量不为0 ，无法删除</div>
          <div v-show="projectCount!=0">本业务类型下项目数量不为0 ，无法删除</div>
          <div>请将属于该类型的所有公司修改为其他类型后再进行删除</div>
        </div>
      </div>
      <div v-else>
        您没有权限删除业务类型，请联系业务部总监进行操作
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        <el-button v-show="hasRole&&cooperationProjectNum==0&&projectCount==0" @click="onSubmit" class="ml-3" type="primary" >确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import vModelMixin from "@/mixin/v-model";
import {
deleteCompanyBusinessType
} from "@/api/businessInformation/companyInformation";
export default {
  mixins: [vModelMixin],
   props: {
    cooperationProjectNum: {
      type: [String,Number],
      required: true,
      default: 0,
    },
    projectCount: {
      type: [String,Number],
      required: true,
      default: 0,
    },
     id: {
      type: [Number,String],
      required: true,
    },
  },
  data() {
    return {
      hasRole:false,
    };
  },
  watch: {},
  mounted() {},
  methods: {
    checkPermi,
    async handlerOpen(){
      this.hasRole=this.checkPermi(["companyType:delet"]);
    },
    async onSubmit(){
      await deleteCompanyBusinessType(this.id);
      this.$modal.msgSuccess("删除业务类型成功！");
      this.innerValue=false;
      this.$emit('on-save-success')
    }
 
  },
};
</script>

