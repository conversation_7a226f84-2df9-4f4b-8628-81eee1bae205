<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      @open="handleOpen"
      width="1050px"
    >
      <el-scrollbar>
        <div style="height: 72vh">
          <el-form :model="form" label-width="130px">
            <el-form-item label="业务类型:">
              <div
                class="border border-solid rounded px-2 inline-block h-6 leading-6"
                style="border-color: #cccccc; backgroundColor: #f2f2f2"
              >
                {{ form.dictLabel }}
              </div>
            </el-form-item>
            <el-form-item label="备注说明:">
              <div>{{ form.remark }}</div>
            </el-form-item>
            <el-tabs type="card" v-model="currentTab" @tab-click="changeTab">
              <el-tab-pane
                :label="item.label"
                :name="item.value"
                v-for="(item, index) in tabList"
                :key="index"
              ></el-tab-pane>
            </el-tabs>
            <MyTable
              v-if="configList.length"
              height="400px"
              :columns="columnsView"
              :source="configList"
              :showHeader="false"
              border
              class="w-1/2"
            >
            </MyTable>
            <div v-else>无</div>
            <pagination
              v-show="total > 20"
              :total="total"
              :page.sync="queryParams.pageNum"
              :page-sizes="[20, 50, 100]"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-form>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { companyList } from "@/api/businessInformation/companyInformation";
import { getListByType } from "@/api/oa/deploy";
import { getDicts } from "@/api/system/dict/data";
import XEUtils from "xe-utils";

export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      ...config,
      configListAll: [],
      tabList:[],
      configList: [],
      columnsView: [],
      currentTab: "project",
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      currentIndex:0
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
      this.init();
    },
    async init() {
      this.tabList =XEUtils.clone(this.tabListInit,true);
      this.currentTab="project";
      const { data } = await getDicts("company_type");
      this.companyTabList.forEach((item) => {
        data.forEach((item1) => {
          if (item.dictLabel == item1.dictLabel) {
            item.dictCode = item1.dictCode;
          }
        });
      });
      const commonParams = {
        ...this.queryParams,
        companyBusinessTypeCode: this.form.dictCode,
      };
      const tasks = [
        getListByType({ ...commonParams,typeId:this.form.dictCode,typeData:1 }),
        companyList({
          ...commonParams,
          companyTypeCode: this.companyTabList[0].dictCode,
        }),
        companyList({
          ...commonParams,
          companyTypeCode: this.companyTabList[1].dictCode,
        }),
        companyList({
          ...commonParams,
          companyTypeCode: this.companyTabList[2].dictCode,
        }),
      ];
      Promise.all(
        tasks.map((p) => {
          //.then返回需要的东西 .catch返回一些错误信息
          return p
            .then((e) => {
              return p;
            })
            .catch((err) => {
              return "错误了";
            });
        })
      )
        .then((res) => {
          this.configListAll = XEUtils.clone(res, true);
          res.forEach((item, index) => {
            this.tabList[
              index
            ].label = `${this.tabList[index].label}(${item.total})`;
          });
          this.configList = res[0].rows;
          this.total = res[0].total;
          this.columnsView = this.columnsViewProject;
        })
        .catch((reason) => {
          console.log(reason);
        });
    },
    changeTab() {
      const obj = {
        project: 0,
        guarantee: 1,
        assetSide: 2,
        funder: 3,
      };
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
      const objIndex={
        guarantee:0,
        assetSide:1,
        funder:2,
      }
      this.currentIndex=objIndex[this.currentTab];
      const currentTables = this.configListAll[obj[this.currentTab]];
      this.configList = currentTables.rows;
      this.total = currentTables.total;
      this.columnsView =
        this.currentTab == "project"
          ? this.columnsViewProject
          : this.columnsViewCompany;
    },
    async getList() {
      const obj = {
        project: getListByType,
        guarantee: companyList,
        assetSide: companyList,
        funder: companyList,
      };
      const { rows, total } = await obj[this.currentTab]({...this.queryParams,companyBusinessTypeCode: this.form.dictCode,typeId:this.form.dictCode,typeData:1,companyTypeCode:this.companyTabList[this.currentIndex].dictCode,});
      this.configList = rows;
      this.total = total;
    },
  },
};
</script>
