<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="更新记录"
      :visible.sync="innerValue"
      width="650px"
    >
      <el-scrollbar>
        <div style="max-height: 80vh">
          <MyTable :columns="columnsRecord" :source="recordList"> </MyTable>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";

export default {
  mixins: [vModelMixin],
  props: {
    recordList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      ...config
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
  },
};
</script>
