<template>
  <div class="p-5">
    <div class="mb-5">
      <div>
        管理员已开启编辑需审核功能：项目发生新增、修改、删除，必须由业务管理员进行审核，并且通过系统通知业务责任人和财务责任人
      </div>
      <div>业务责任人必须由项目经理担任，财务责任人必须由财务人员担任</div>
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="88px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <MyTable
      :columns="columns"
      :source="configList"
      :showIndex="true"
      :queryParams="queryParams"
    >
      <template #yewuList="{ record }">
        <div class="flex flex-wrap">
          <div
            v-for="(item, index) in record.yewuList"
            :key="index"
            class="mr-3 border border-solid rounded-sm px-2 flex h-8 items-center"
            style="border-color: #cccccc"
          >
            <div class="h-8 leading-8">{{ item.name }}</div>
            <div
              @click="deletType(record, item, 'business')"
              class="cursor-pointer"
            >
              <i class="el-icon-close"></i>
            </div>
          </div>
        </div>
      </template>
      <template #caiwuList="{ record }">
        <div class="flex flex-wrap">
          <div
            v-for="(item, index) in record.caiwuList"
            :key="index"
            class="mr-3 border border-solid rounded-sm px-2 flex h-8 items-center"
            style="border-color: #cccccc"
          >
            <div class="h-8 leading-8">{{ item.name }}</div>
            <div
              @click="deletType(record, item, 'finance')"
              class="cursor-pointer"
            >
              <i class="el-icon-close"></i>
            </div>
          </div>
        </div>
      </template>

      <template #operate="{ record }">
        <el-button
          type="text"
          v-hasPermi="['dutyUser:addBusiness']"
          @click="handleAdd(record, 'business')"
          >添加业务责任人</el-button
        >
        <el-button
          type="text"
          v-hasPermi="['dutyUser:addFinance']"
          @click="handleAdd(record, 'finance')"
          >添加财务责任人</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <UserDepPostSelect
      rowKey="userId"
      title="user"
      :showSelect="true"
      v-model="open"
      @on-submit-success-user="userSuccess"
    />
  </div>
</template>

<script>
import { listDeploy } from "@/api/oa/deploy";
import {
  addDutyUser,
  deleteDutyUser,
} from "@/api/businessInformation/dutyUser";
import { uniqueArrObj } from "@/utils";

import config from "./components/config";

export default {
  name: "DutyUser",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: undefined,
      },
      configList: [],
      total: 0,
      formTitle: "",
      formId: "",
      open: false,
      multipleSelectionUser: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    async getList() {
      const { rows, total } = await listDeploy(this.queryParams);
      this.configList = rows;
      this.total = total;
      this.handlecconfigList();
    },
    handlecconfigList() {
      console.log(this.configList, 22);
      this.configList.forEach((item) => {
        
          item.caiwuList = uniqueArrObj(item.caiwuList || []);
          item.yewuList = uniqueArrObj(item.yewuList || []);
        
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async deletType(record, item, type) {
      let params = {
        id: record.id,
        userid: item.id,
      };
      params.userFlag = type == "finance" ? 0 : 1;
      await deleteDutyUser(params);
      this.$modal.msgSuccess("删除用户成功！");
      this.getList();
    },

    handleAdd(value, type) {
      this.formId = value.id;
      this.formTitle = type == "business" ? "添加业务责任人" : "添加财务责任人";
      this.multipleSelectionUser =
        type == "business"
          ? value.yewuList.map((item) => {
              return { userId: item.id };
            })
          : value.caiwuList.map((item) => {
              return { userId: item.id };
            });
      this.open = true;
    },
    getParams(value) {
      const ids = value.map((item) => item.userId);
      let params = {
        id: this.formId,
      };
      if (this.formTitle == "添加业务责任人") {
        params.financialStaffList = [...new Set(ids)];
      } else if (this.formTitle == "添加财务责任人") {
        params.salesmanList = [...new Set(ids)];
      }
      return params;
    },
    async userSuccess(value) {
      const userListAll=value.concat(this.multipleSelectionUser);
      const params = this.getParams(userListAll);
      await addDutyUser(params);
      this.$modal.msgSuccess("添加用户成功！");
      this.open = false;
      this.getList();
    },
  },
};
</script>
