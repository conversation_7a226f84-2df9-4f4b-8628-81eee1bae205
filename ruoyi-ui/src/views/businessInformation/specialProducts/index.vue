<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <el-button
      v-hasPermi="['specialProducts:add']"
      @click="add"
      type="primary"
      size="mini"
      plain
      icon="el-icon-plus"
      class="mb-2"
      >新增规则</el-button
    >
    <MyTable ref="table" :columns="columns" :source="configList">
      <template #enableStatus="{ record }">
        <el-tag :type="record.enableStatus == 1 ? '' : 'danger'">{{
          record.enableStatusLabel
        }}</el-tag>
      </template>
      <template #operate="{ record }">
        <el-button
          v-hasPermi="['specialProducts:view']"
          type="text"
          @click="view(record)"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['specialProducts:update']"
          type="text"
          @click="update(record)"
          >编辑</el-button
        >
        <el-button
          v-hasPermi="['specialProducts:delete']"
          v-show="record.isDefault == '2'"
          type="text"
          @click="deletes(record)"
          >删除</el-button
        >
        <el-button
          v-hasPermi="['specialProducts:editRecord']"
          type="text"
          @click="updateRecord(record)"
          >编辑记录</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      :form="form"
      v-model="open"
      @on-save-success="getList"
      :productTypeList="productTypeList"
    />
    <DetailDialogView :form="form" v-model="openView" />
    <RecordDialogNew
      :params="paramsRecord"
      v-model="openRecord"
      :multiple="true"
      @openDetail="openDetail"
    />
    <ContrastDetail v-model="contrastDetailNew" :data="contrastDetailData" />
  </div>
</template>
<script>
import { clone } from "xe-utils";
import {
  getProjectNameRuleList,
  deleteProjectNameRule,
} from "@/api/businessInformation/specialProducts";
import DetailDialog from "./components/DetailDialog.vue";
import DetailDialogView from "./components/DetailDialogView.vue";
import ContrastDetail from "./components/ContrastDetailNew.vue";
import config from "./components/config";
import { getDataManageList } from "@/api/notice/management";
import { setEmptyArrayToUndefined, renameField } from "@/utils";

export default {
  name: "SpecialProducts",
  components: { DetailDialog, DetailDialogView, ContrastDetail },
  provide: {
    contrast: { productClassificationName: "产品分类", isDefault: "是否默认" }, //对比表格映射的字段
    arraryToString: ["productClassificationName"],
    dictionary: {
      isDefault: { 1: "是", 2: "否" },
    }, //需要转字典的对象集合 字符串则转字典 枚举值则根据枚举转
  },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        chanpinfenlei: undefined,
      },
      total: 0,
      configList: [],
      form: {},
      open: false,
      openView: false,
      openRecord: false,
      productTypeList: [],
      paramsRecord: {},
      contrastDetailNew: false,
      contrastDetailData: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getTypes();
      this.getList();
    },
    async getTypes() {
      let { rows } = await getDataManageList({
        firstDataCode: "business_type",
      });
      rows.forEach((rootNode) =>
        renameField(rootNode, "fPiattaformas", "children")
      );
      rows.forEach((rootNode) => renameField(rootNode, "dataName", "label"));
      this.formColumns[0].options = rows[0]?.children;
      setEmptyArrayToUndefined(rows, "children");
      this.productTypeList = rows[0]?.children;
    },
    async getList() {
      const { rows, total } = await getProjectNameRuleList({
        ...this.queryParams,
      });
      this.configList = rows;
      this.haneleConfigList();
      this.total = total;
    },
    haneleConfigList() {
      this.configList.forEach((item) => {
        item.isDefaultLabel = this.defaultObj[item.isDefault];
        item.enableStatusLabel = this.enableStatusObj[item.enableStatus];
        item.productClassificationName =
          item.productClassificationName.join() || "-";
      });
    },
    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    add() {
      this.form = {};
      this.open = true;
    },
    update(value) {
      this.form = clone(value, true);
      this.open = true;
    },
    view(value) {
      this.form = clone(value, true);
      this.openView = true;
    },
    openDetail(value) {
      this.contrastDetailData = {
        newInfo: value.oaApplyRecordsNewData,
        oldInfo: value.oaApplyRecordsOldData,
      };
      this.contrastDetailNew = true;
    },

    updateRecord(value) {
      this.paramsRecord = {
        applyType: 9,
        oaApplyId: value.id,
      };
      this.openRecord = true;
    },
    deletes(value) {
      this.$modal
        .confirm("是否确定删除此规则？")
        .then(function () {
          return deleteProjectNameRule(value.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
