<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="查看详情"
      :visible.sync="innerValue"
      width="1050px"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div style="max-height: 80vh">
          <MyForm
            v-model="myForm"
            :columns="formColumnsDialogView"
            formType="false"
            labelWidth="145px"
            ref="form"
          >
            <template #oaProjectNameRuleSlaveList>
              <div style="color: #606266; font-weight: 700">
                组成项目的必要公司:
              </div>
              <MyTable
                class="mt-3"
                :columns="columnsDialogView"
                :source="myForm.oaProjectNameRuleSlaveList"
              >
              </MyTable>
            </template>
          </MyForm>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { clone } from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      myForm: {},
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},

    handleOpen() {
      this.getMyForm();
    },
    async getMyForm() {
      this.myForm = clone(this.form, true);
      this.myForm.isOtherCompanyLabel=this.otherCompanyObj[this.myForm.isOtherCompany];
      this.myForm.oaProjectNameRuleSlaveList.forEach(item=>{
        this.$set(item,'isMandatoryLabel',this.defaultObj[item.isMandatory]);
        this.$set(item,'isMultipleChoiceLabel',this.defaultObj[item.isMultipleChoice]);
        this.$set(item,'isAbbreviationLabel',this.defaultObj[item.isAbbreviation]);
      })
    },
  },
};
</script>
