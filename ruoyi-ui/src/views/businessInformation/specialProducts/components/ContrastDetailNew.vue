<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="编辑记录"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="700px"
    >
      <div style="max-height: 70vh" class="overflow-y-auto">
        <MyTable
          class="mb-2"
          :columns="columnsRecord"
          :source="configList"
          :row-style="rowStyle"
          :cell-style="cellStyle"
          border
        />
        <div v-for="(item, index) in list" :key="index">
          顺序号{{ item[0].sequenceNumber }}
          <span v-show="item[0].type == 'add' || item[0].type == 'delete'">{{(
            { add: "新增", delete: "删除" }[item[0].type])
          }}</span>
          <MyTable
            :showSequenceNumber="false"
            class="my-2"
            :columns="columnsRecord"
            :source="item"
            :row-style="rowStyle"
            :cell-style="cellStyle"
            border
          />
        </div>
        <MyTable
          class="mt-2"
          :columns="columnsRecord"
          :source="configListEnd"
          :row-style="rowStyle"
          :cell-style="cellStyle"
          border
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";

export default {
  name: "ContrastDetailNew",
  mixins: [vModelMixin],
  inject: {
    contrast: {
      default: () => ({}),
    },
    arraryToString: {
      default: () => [],
    },
    dictionary: {
      default: () => ({}),
    },
  },
  props: {
    data: {
      type: Object,
      default: () => {},
      required: true,
    },
  },
  data() {
    return {
      columnsRecord: Object.freeze([
        { label: "", prop: "title" },
        { label: "修改前", prop: "before" },
        { label: "修改后", prop: "after" },
      ]),
      configList: [],
      configListEnd: [],
      contrastEnd: {
        isOtherCompany: "其他公司",
        remark: "备注说明",
        enableStatus: "启用状态",
      },
      dictionaryEnd: {
        isOtherCompany: { 1: "有", 2: "无" },
        enableStatus: { 1: "启用", 2: "禁用" },
      },
      list: [],
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handelOpen() {
      const newInfo = this.data?.newInfo ? JSON.parse(this.data.newInfo) : {};
      const oldInfo = this.data?.oldInfo ? JSON.parse(this.data.oldInfo) : {};
      const newTab = newInfo.oaProjectNameRuleSlaveList;
      const oldTab = oldInfo.oaProjectNameRuleSlaveList;
      console.log(newTab, oldTab);
      this.list = this.compareData(newTab, oldTab);
      this.getRecordList(newInfo, oldInfo);
    },
    getRecordList(newInfo, oldInfo) {
      Object.keys(this.dictionary).forEach((item) => {
        if (
          Object.prototype.toString.call(this.dictionary[item]) ===
          "[object String]"
        ) {
          //说明是字典
          newInfo[item] =
            this.$store.state.data.KV_MAP[this.dictionary[item]][newInfo[item]];
          oldInfo[item] =
            this.$store.state.data.KV_MAP[this.dictionary[item]][oldInfo[item]];
        } else {
          newInfo[item] = this.dictionary[item][newInfo[item]];
          oldInfo[item] = this.dictionary[item][oldInfo[item]];
        }
      });
      this.arraryToString.forEach((item) => {
        newInfo[item] = newInfo[item]?.join("");
        oldInfo[item] = oldInfo[item]?.join("");
      });
      this.configList = Object.keys(this.contrast).map((key) => {
        return {
          title: this.contrast[key],
          before: oldInfo[key],
          after: newInfo[key],
          diff: oldInfo[key] == newInfo[key] ? false : true,
        };
      });
      Object.keys(this.dictionaryEnd).forEach((item) => {
        if (
          Object.prototype.toString.call(this.dictionary[item]) ===
          "[object String]"
        ) {
          //说明是字典
          newInfo[item] =
            this.$store.state.data.KV_MAP[this.dictionary[item]][newInfo[item]];
          oldInfo[item] =
            this.$store.state.data.KV_MAP[this.dictionary[item]][oldInfo[item]];
        } else {
          newInfo[item] = this.dictionaryEnd[item][newInfo[item]];
          oldInfo[item] = this.dictionaryEnd[item][oldInfo[item]];
        }
      });
      this.configListEnd = Object.keys(this.contrastEnd).map((key) => {
        return {
          title: this.contrastEnd[key],
          before: oldInfo[key],
          after: newInfo[key],
          diff: oldInfo[key] == newInfo[key] ? false : true,
        };
      });
    },
    convertYesNo(value) {
      return value === "1" ? "是" : "否";
    },
    compareData(objNew, objOld) {
      let result = [];

      // 1. 比较 sequenceNumber 相同的数据，生成 update
      objNew.forEach((newItem) => {
        const oldItem = objOld?.find(
          (item) => item.sequenceNumber === newItem.sequenceNumber
        );

        if (oldItem) {
          let diffData = [
            {
              after: newItem.companTypeName,
              before: oldItem.companTypeName,
              diff: newItem.companTypeName !== oldItem.companTypeName,
              sequenceNumber: newItem.sequenceNumber,
              title: "公司类型",
              type: "update",
            },
            {
              after: this.convertYesNo(newItem.isMandatory),
              before: this.convertYesNo(oldItem.isMandatory),
              diff: newItem.isMandatory !== oldItem.isMandatory,
              sequenceNumber: newItem.sequenceNumber,
              title: "是否必选",
              type: "update",
            },
            {
              after: this.convertYesNo(newItem.isMultipleChoice),
              before: this.convertYesNo(oldItem.isMultipleChoice),
              diff: newItem.isMultipleChoice !== oldItem.isMultipleChoice,
              sequenceNumber: newItem.sequenceNumber,
              title: "是否允许多个同类公司",
              type: "update",
            },
            {
              after: this.convertYesNo(newItem.isAbbreviation),
              before: this.convertYesNo(oldItem.isAbbreviation),
              diff: newItem.isAbbreviation !== oldItem.isAbbreviation,
              sequenceNumber: newItem.sequenceNumber,
              title: "简称是否作为项目名称",
              type: "update",
            },
          ];
          result.push(diffData);
        }
      });

      // 2. 生成 add 类型（objNew 中新增的项）
      objNew.forEach((newItem) => {
        if (
          !objOld?.find((item) => item.sequenceNumber === newItem.sequenceNumber)
        ) {
          result.push([
            {
              after: newItem.companTypeName,
              before: "",
              diff: false,
              sequenceNumber: newItem.sequenceNumber,
              title: "公司类型",
              type: "add",
            },
            {
              after: this.convertYesNo(newItem.isMandatory),
              before: "",
              diff: false,
              sequenceNumber: newItem.sequenceNumber,
              title: "是否必选",
              type: "add",
            },
            {
              after: this.convertYesNo(newItem.isMultipleChoice),
              before: "",
              diff: false,
              sequenceNumber: newItem.sequenceNumber,
              title: "是否允许多个同类公司",
              type: "add",
            },
            {
              after: this.convertYesNo(newItem.isAbbreviation),
              before: "",
              diff: false,
              sequenceNumber: newItem.sequenceNumber,
              title: "简称是否作为项目名称",
              type: "add",
            },
          ]);
        }
      });

      // 3. 生成 delete 类型（objOld 中删除的项）
      objOld?.forEach((oldItem) => {
        if (
          !objNew?.find((item) => item.sequenceNumber === oldItem.sequenceNumber)
        ) {
          result.push([
            {
              after: "",
              before: oldItem.companTypeName,
              diff: false,
              sequenceNumber: oldItem.sequenceNumber,
              title: "公司类型",
              type: "delete",
            },
            {
              after: "",
              before: this.convertYesNo(oldItem.isMandatory),
              diff: false,
              sequenceNumber: oldItem.sequenceNumber,
              title: "是否必选",
              type: "delete",
            },
            {
              after: "",
              before: this.convertYesNo(oldItem.isMultipleChoice),
              diff: false,
              sequenceNumber: oldItem.sequenceNumber,
              title: "是否允许多个同类公司",
              type: "delete",
            },
            {
              after: "",
              before: this.convertYesNo(oldItem.isAbbreviation),
              diff: false,
              sequenceNumber: oldItem.sequenceNumber,
              title: "简称是否作为项目名称",
              type: "delete",
            },
          ]);
        }
      });
      return result;
    },
    rowStyle({ row, rowIndex }) {
      if (row.diff) {
        return {
          color: "#cccccc",
        };
      }
    },
    cellStyle({ column, columnIndex }) {
      // 设置第一列的字体颜色为黑色
      if (columnIndex === 0) {
        return {
          color: "#363636",
        };
      }
    },
  },
};
</script>
