<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="form.id ? '编辑规则' : '新增规则'"
      :visible.sync="innerValue"
      width="1050px"
      @open="handleOpen"
      @close="handleClose"
    >
      <el-scrollbar>
        <div style="max-height: 80vh">
          <MyForm
            v-model="myForm"
            :columns="formColumnsDialog"
            formType="false"
            labelWidth="145px"
            ref="form"
          >
            <template #productClassificationId>
              <el-form-item label="产品分类:" prop="productClassificationId">
                <el-cascader
                  v-model="myForm.productClassificationIdList"
                  :options="productTypeList"
                  style="width: 500px"
                  :props="{
                    expandTrigger: 'hover',
                    value: 'id',
                    checkStrictly: true,
                    multiple: true,
                  }"
                  clearable
                  popper-class="cascadermultiple"
                ></el-cascader>
                <div style="color: #999999">
                  指定产品分类，则该属于该产品分类的项目创建或编辑时将应用此规则
                </div>
              </el-form-item>
            </template>
            <template #oaProjectNameRuleSlaveList>
              <div style="color: #606266; font-weight: 700">
                组成项目的必要公司:
              </div>
              <MyTable
                class="mt-3"
                :columns="columnsDialog"
                :source="myForm.oaProjectNameRuleSlaveList"
              >
                <template #companyType="{ record }">
                  <span style="color: red" class="absolute top-2 left-0"
                    >*</span
                  >
                  <el-select v-model="record.companyType" placeholder="请选择">
                    <el-option
                      v-for="(item, index) in projectTypeList"
                      :key="index"
                      :label="item.selectName"
                      :value="item.selectCode"
                    >
                    </el-option>
                  </el-select>
                </template>
                <template #isMandatory="{ record }">
                  <el-select v-model="record.isMandatory" placeholder="请选择">
                    <el-option
                      v-for="(item, index) in whetherOrNot"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
                <template #isMultipleChoice="{ record }">
                  <el-select
                    v-model="record.isMultipleChoice"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item, index) in whetherOrNot"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
                <template #isAbbreviation="{ record }">
                  <el-select
                    v-model="record.isAbbreviation"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item, index) in whetherOrNot"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
                <template #operate="{ record }">
                  <el-button type="text" @click="del(record)">删除</el-button>
                  <el-button
                    type="text"
                    v-show="record.sequenceNumber != 1"
                    @click="moveUp(record.sequenceNumber)"
                    >上移</el-button
                  >
                  <el-button
                    type="text"
                    @click="moveDown(record.sequenceNumber)"
                    v-show="
                      record.sequenceNumber !=
                      myForm.oaProjectNameRuleSlaveList.length
                    "
                    >下移</el-button
                  >
                </template>
              </MyTable>
              <el-button type="primary" size="mini" class="mt-1" @click="add"
                >添加公司</el-button
              >
            </template>
            <template #isOtherCompany>
              <el-form-item label="其他:" prop="isOtherCompany">
                <el-switch
                  v-model="myForm.isOtherCompany"
                  active-color="'#13ce66'"
                  inactive-color="'#ff4949'"
                  active-text="有"
                  inactive-text=""
                  :active-value="1"
                  :inactive-value="2"
                >
                </el-switch>
              </el-form-item>
            </template>
          </MyForm>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
          <el-button @click="save" type="primary" class="ml-3">保存</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { clone } from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import {
  addProjectNameRule,
  updateProjectNameRule,
} from "@/api/businessInformation/specialProducts";
import { selectTypeList } from "@/api/companyTypeSelection";
export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    productTypeList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      ...config,
      myForm: {},
      projectTypeList: [
        
      ]
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},

    handleOpen() {
      this.getMyForm();
      this.getSelectTypeList();
    },
    async getSelectTypeList() {
      const { rows } = await selectTypeList({ modelCode: "PROJECTNAMERULE" });
      this.projectTypeList = rows;
    },
    async getMyForm() {
      this.myForm = clone(this.form, true);
      if (!this.myForm.id) {
        this.$set(this.myForm, "oaProjectNameRuleSlaveList", []);
        this.$set(this.myForm, "isOtherCompany", 1);
        this.$set(this.myForm, "enableStatus", 1);
        this.$set(this.myForm, "isDefault", 2);
      } else {
        this.myForm.isOtherCompany =
          this.myForm.isOtherCompany && Number(this.myForm.isOtherCompany);
        this.myForm.enableStatus =
          this.myForm.enableStatus && Number(this.myForm.enableStatus);
        this.myForm.isDefault =
          this.myForm.isDefault && Number(this.myForm.isDefault);
      }
    },
    add() {
      this.myForm.oaProjectNameRuleSlaveList.push({
        companyType: "",
        isMandatory: "1",
        isMultipleChoice: "1",
        isAbbreviation: "1",
      });
    },
    del(record) {
      this.myForm.oaProjectNameRuleSlaveList =
        this.myForm.oaProjectNameRuleSlaveList.filter(
          (item) => item.sequenceNumber != record.sequenceNumber
        );
    },
    moveUp(sequenceNumber) {
      const index = sequenceNumber - 1;
      if (index > 0) {
        const temp = this.myForm.oaProjectNameRuleSlaveList[index];
        this.$set(
          this.myForm.oaProjectNameRuleSlaveList,
          index,
          this.myForm.oaProjectNameRuleSlaveList[index - 1]
        );
        this.$set(this.myForm.oaProjectNameRuleSlaveList, index - 1, temp);
      }
    },
    moveDown(sequenceNumber) {
      const index = sequenceNumber - 1;
      if (index < this.myForm.oaProjectNameRuleSlaveList.length - 1) {
        const temp = this.myForm.oaProjectNameRuleSlaveList[index];
        this.$set(
          this.myForm.oaProjectNameRuleSlaveList,
          index,
          this.myForm.oaProjectNameRuleSlaveList[index + 1]
        );
        this.$set(this.myForm.oaProjectNameRuleSlaveList, index + 1, temp);
      }
    },
    async save() {
      // 校验 companyType 是否为空
      const hasEmptyCompanyType = this.myForm.oaProjectNameRuleSlaveList.some(
        (item) => !item.companyType
      );
      if (hasEmptyCompanyType) {
        this.$message.warning("请选择公司类型");
        return;
      }
      const params = clone(this.myForm, true);
      params.productClassificationId = params.productClassificationIdList?.map(
        (subArray) => subArray[subArray.length - 1]
      );
      delete params.productClassificationName;
      if (this.form.id) {
        await updateProjectNameRule(params);
      } else {
        await addProjectNameRule(params);
      }
      this.$message.success("操作成功");
      this.innerValue = false;
      this.$emit("on-save-success");
    },
    handleClose() {
      this.myForm = {};
    },
  },
};
</script>
<style lang="less"  >
.cascadermultiple {
  .el-cascader-panel .el-checkbox {
    width: 100%;
    height: 100%;
    z-index: 10;
    position: absolute;
  }
  .el-cascader-node__label {
    margin-left: 10px;
  }
  /* 这个样式针对IE有用，不考虑IE的可以不用管*/
  .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
  }
}
</style>