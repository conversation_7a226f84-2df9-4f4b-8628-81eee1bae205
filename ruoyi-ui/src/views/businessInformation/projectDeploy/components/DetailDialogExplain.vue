<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="说明"
      :visible.sync="innerValue"
      width="550px"
    >
      <div>
        <div>新增项目需在 [项目立项管理] 中创建，并通过OA流程进行审核</div>
        <div class="mb-3">修改、删除项目，可在本页面中操作，并通过OA流程进行审核</div>
        <div>通常项目由资金方、资产方/反担保方 、担保公司3种公司组成，还可添加其他类型的公司作为补充，例某些项目可包含技术服务方</div>
        <div class="mb-3">
          项目名称自动生成： [担保公司]简称 + [资产方]简称 + [资金方]简称
        </div>
        <div>产品分类为 [保函类] 的项目，由内部担保公司、资产方（非必填）、受益人/其代表组成，还可添加其他类型的公司作为补充</div>
        <div class="my3">项目名称自动生成： [内部担保公司]简称 + [资产方]简称 + [受益人/其代表]简称</div>
        <div >产品分类为 [不良出表类] 的项目，由内部担保公司、资金方或外部担保公司（二选一）组成，还可添加其他类型的公司作为补充</div>
        <div class="my-3">项目名称自动生成： [内部担保公司]简称 + [资金方]简称/ [外部担保公司]简称</div>
        <div>产品分类为 [分保业务类] 的项目，由内部担保公司、外部担保公司组成，还可添加其他类型的公司作为补充</div>
        <div class="my-3">项目名称自动生成： [内部担保公司]简称 + [外部担保公司]简称</div>
        <div>产品分类为 [金融科技类] 的项目，由内部担保公司或内部科技公司（二选一）、资产方、资金方、外部担保公司组成，还可添加其他类型公司</div>
        <div class="my-3">项目名称自动生成：[内部担保公司]简称/[内部科技公司]简称 + [资产方]简称  + [资金方]简称  + [外部担保公司]简称</div>
        <div>如果创建项目时找不到所需公司，请先在 [公司信息] 菜单中创建</div>
        <div>拥有任意一种项目角色权限的人员，都可以查看项目，或在对应功能模块中拥有所需的功能权限</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {},
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>
