<template>
  <div>
    <div style="margin-bottom: 20px">
      <el-button size="mini" @click="cancel">关闭</el-button>
      <el-button
        size="mini"
        v-hasPermi="['projectDeploy:addEdit']"
        @click="edit"
        type="primary"
        v-if="myForm.checkStatus == 3 || myForm.checkStatus === null"
        >编辑</el-button
      >
      <el-button
        size="mini"
        v-if="
          myForm.checkStatus == 3 ||
          myForm.checkStatus == 1 ||
          myForm.checkStatus === null
        "
        v-hasPermi="['projectDeploy:editCollec']"
        type="primary"
        @click="editCollec"
        >编辑项目收付款信息</el-button
      >
      <el-button
        size="mini"
        v-if="
          myForm.checkStatus == 3 ||
          myForm.checkStatus == 1 ||
          myForm.checkStatus === null
        "
        v-hasPermi="['projectDeploy:editInfo']"
        type="primary"
        @click="editInformation"
        >编辑项目信息费信息</el-button
      >
      <el-button
        size="mini"
        v-hasPermi="['projectDeploy:delet']"
        @click="del"
        v-if="myForm.checkStatus == 3 || myForm.checkStatus === null"
        style="margin-left: 30px"
        type="warning"
        >删除项目</el-button
      >
    </div>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="项目详情" name="1">
        <div class="pb-5">
          <div class="flex flex-wrap">
            <div
              v-for="(item, index) in viewDetail"
              :key="index"
              class="w-1/2 flex shrink-0 mb-5"
            >
              <div class="mr-2">{{ item.label }}:</div>

              <div
                v-if="item.value == 'businessTypeList'"
                class="flex flex-wrap"
              >
                <div
                  v-for="(item1, index1) in myForm[item.value]"
                  :key="index1"
                  class="mr-3 border border-solid rounded px-2 flex h-6 items-center"
                  style="
                    border-color: #cccccc;
                    background-color: #f2f2f2;
                    font-size: 13px;
                  "
                >
                  <div class="h-6 leading-6">{{ item1.typeName }}</div>
                </div>
              </div>
              <div v-else-if="item.value == 'isEnable'">
                <dict-tag :options="statusList" :value="myForm[item.value]" />
              </div>
              <div v-else-if="item.value == 'channelType'">
                {{ myForm[item.value] == 0 ? "内部" : "外部" }}
              </div>
              <div v-else-if="item.value == 'channelName'">
                <div v-if="myForm.channelType == 1">
                  {{ myForm.channelName }}
                </div>
                <div v-else>
                  <span
                    v-if="
                      myForm.qudaofangList && myForm.qudaofangList.length > 0
                    "
                  >
                    <span v-for="item in myForm.qudaofangList" :key="item.id"
                      >{{ item.name || item.nickName }}，</span
                    >
                  </span>
                </div>
              </div>
              <div v-else :style="{ fontWeight: item.css || 0 }">
                {{ myForm[item.value] }}
              </div>
            </div>
          </div>
          <div class="font-bold">项目支持的产品</div>
          <MyTable
            border
            :columns="columnsViewProd"
            :source="myForm.Prod"
            class="w-2/3"
          >
            <template #systemNo="{ record }">
              {{ dict.label.system_product[record.systemNo] }}
            </template>
          </MyTable>
          <pagination
            v-show="total > 20"
            :total="total"
            :page.sync="queryParams.pageNum"
            :page-sizes="[20, 50, 100]"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
          <div class="font-bold mt-5">组成项目的公司</div>
          <MyTable
            border
            :columns="columnsViewCompany"
            :source="myForm.necessityCompanyList"
            class="w-2/3"
            :span-method="spanMethod"
          >
            <template #operate="{ record }">
              <el-button type="text" @click="goCompany(record)"
                >查看公司信息 ></el-button
              >
            </template>
          </MyTable>
          <div v-if="myForm.otherUnitList && myForm.otherUnitList.length">
            <div class="my-2">其他</div>
            <MyTable
              border
              :columns="columnsViewCompany"
              :source="myForm.otherUnitList"
              class="w-2/3"
              :show-header="false"
            >
              <template #operate="{ record }">
                <el-button type="text" @click="goCompany(record)"
                  >查看公司信息 ></el-button
                >
              </template>
            </MyTable>
          </div>
          <div class="font-bold mt-5">使用本项目的功能模块</div>
          <MyTable
            border
            :columns="columnsViewMode"
            :source="myForm.featureModelList"
            class="w-1/2"
          >
            <template #isUse="{ record }">
              <div :style="{ color: record.isUse ? '#363636' : '#CCCCCC' }">
                {{ record.isUse == "false" ? "无" : "有" }}
              </div>
            </template>
          </MyTable>
        </div>
      </el-tab-pane>
      <el-tab-pane label="收付款信息" name="3">
        <CollecPay v-if="activeName == 3" :id="myForm.id" />
      </el-tab-pane>
      <el-tab-pane label="编辑记录" name="2">
        <div style="display: flex">
          <div style="display: flex; align-items: center; margin-right: 9px">
            <span style="margin-right: 9px">编辑人员</span>
            <el-input
              style="width: 180px"
              placeholder="请输入"
              v-model="queryParams2.nickName"
            ></el-input>
          </div>
          <div style="display: flex; align-items: center; margin-right: 9px">
            <span style="margin-right: 9px">编辑类型</span>
            <el-select placeholder="请选择" v-model="queryParams2.operation">
              <el-option label="新增" value="0"></el-option>
              <el-option label="修改" value="1"></el-option>
              <el-option label="删除" value="2"></el-option>
            </el-select>
          </div>
          <div style="display: flex; align-items: center; margin-right: 9px">
            <span style="margin-right: 9px">审核状态</span>
            <el-select placeholder="请选择" v-model="queryParams2.checkStatus">
              <el-option label="审核中" value="0"></el-option>
              <el-option label="通过" value="1"></el-option>
              <el-option label="驳回" value="2"></el-option>
            </el-select>
          </div>
          <div style="display: flex; align-items: center; margin-right: 9px">
            <span style="margin-right: 9px">编辑日期</span>
            <el-date-picker
              v-model="time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </div>
          <el-button type="primary" size="mini" @click="search">搜索</el-button>
          <el-button size="mini" @click="reset">重置</el-button>
        </div>
        <el-table
          border=""
          :data="tableData"
          style="width: 100%; margin-top: 16px"
        >
          <el-table-column prop="editTime" label="编辑日期" width="160" />
          <el-table-column prop="nickName" label="编辑人员" />
          <el-table-column prop="nickName" label="编辑类型">
            <template slot-scope="scope">
              {{
                scope.row.operation == 0
                  ? "新增"
                  : scope.row.operation == 1
                  ? "修改"
                  : "删除"
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="editInfo"
            label="说明"
            show-overflow-tooltip=""
            width="150"
          />

          <el-table-column prop="editTime" label="审核时间" width="160" />
          <el-table-column prop="date" label="审核状态" width="100">
            <template slot-scope="scope">
              {{
                scope.row.checkStatus == 0
                  ? "审核中"
                  : scope.row.checkStatus == 1
                  ? "通过"
                  : "驳回"
              }}
            </template>
          </el-table-column>

          <el-table-column prop="date" width="130" label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="see(scope.row)"
                >查看编辑内容</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total2 > 20"
          :total="total2"
          :page.sync="queryParams2.pageNum"
          :page-sizes="[20, 50, 100]"
          :limit.sync="queryParams2.pageSize"
          @pagination="getList2"
        />
      </el-tab-pane>
    </el-tabs>
    <preview
      v-if="previewType"
      @close="previewType = false"
      :detail="detail"
      :companytypeList="companytypeList"
      :companyList="companyList"
    />
    <DelTopis
      v-if="delTopisType"
      @close="delTopisType = false"
      @next="next"
      :type="showType"
      :showList="showList"
    />
  </div>
</template>

<script>
import {
  getDeploy,
  getDataByTemplName,
  getDataIsReference,
} from "@/api/oa/deploy";
import CollecPay from "./components/CollecPay.vue";
import { traderDetail, selectEditRecord2 } from "@/api/oa/voucharRules";
import preview from "../../../preview.vue";
import config from "../../config";
import { projectParameter } from "@/api/businessInformation/productInformation";
import tableSpanMethod from "@/mixin/tableSpanMethod";
import DelTopis from "./DelTopis.vue";
import XEUtils from "xe-utils";
export default {
  components: {
    preview,
    DelTopis,
    CollecPay,
  },
  name: "Views",
  dicts: ["system_product"],
  mixins: [tableSpanMethod],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    otherUnitTypeListAll: {
      type: Array,
      required: true,
      default: () => [],
    },
    changeEditType: {
      type: Boolean,
      required: true,
      default: false,
    },
    companytypeList: Array,
    companyList: Object,
  },
  data() {
    return {
      time: [],
      showType: false,
      delTopisType: false,
      previewType: false,
      activeName: "1",
      ...config,
      myForm: {},
      total: 0,
      total2: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 20,
        applyType: 1,
        oaApplyId: "",
        nickName: "",
        operation: "",
        checkStatus: "",
        createTime: "",
        updateTime: "",
      },
      tableData: [],
      detail: null,
      showList: [],
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    editCollec() {
      if (this.myForm.traderProcessFlag == 1) {
        this.$message.warning(
          "当前项目的收付款信息正在审核中,请等待审核完成再进行编辑"
        );
        return;
      }
      this.$router.push({
        path: "/businessInformationOther/editCollecPay",
        query: {
          id: this.myForm.id,
          name: this.myForm.projectName,
        },
      });
    },
    editInformation() {
      if (this.myForm.feeProcessFlag == 1) {
        this.$message.warning(
          "当前项目的信息费信息已被修改，正在审核中,请等待审核完成再进行编辑"
        );
        return;
      }
      this.$router.push({
        path: "/businessInformationOther/editInformation",
        query: {
          id: this.myForm.id,
          name: this.myForm.projectName,
        },
      });
    },

    next() {
      console.log(this.myForm);
      sessionStorage.setItem("delProData", JSON.stringify(this.myForm));
      getDataByTemplName({
        templateName: "业务信息配置-删除项目名称申请",
      }).then((res) => {
        if (res.code == 200) {
          this.delTopisType = false;
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              delProject: true,
            },
          });
        }
      });
    },
    del() {
      getDataIsReference({ id: this.myForm.id }).then((res) => {
        if (res.isUse == "true") {
          this.showType = true;
          this.showList = res.model;
        }
        this.delTopisType = true;
      });
    },
    edit() {
      this.$emit("edit");
    },
    see(v) {
      this.detail = v;
      this.previewType = true;
    },
    reset() {
      this.queryParams2 = {
        pageNum: 1,
        pageSize: 20,
        applyType: 1,
        oaApplyId: "",
        nickName: "",
        operation: "",
        checkStatus: "",
        createTime: "",
        updateTime: "",
      };
      this.time = [];
      this.getList2();
    },
    search() {
      this.queryParams2.pageNum = 1;
      this.getList2();
    },
    getList2() {
      this.queryParams2.oaApplyId = this.form.id;
      if (this.time && this.time.length > 0) {
        this.queryParams2.createTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.queryParams2.updateTime = this.$format(this.time[1], "yyyy-MM-dd");
      } else {
        this.queryParams2.createTime = "";
        this.queryParams2.updateTime = "";
      }
      selectEditRecord2(this.queryParams2).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.total2 = res.total || 0;
        }
      });
    },
    identity(e) {
      if (e == 0) {
        return "财务负责人";
      } else if (e == 1) {
        return "业务负责人";
      } else if (e == 2) {
        return "财务管理员";
      } else if (e == 3) {
        return "业务管理员";
      } else if (e == 9) {
        return "超级管理员";
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return this.objectSpanMethod({
        row,
        column,
        rowIndex,
        columnIndex,
        data: this.myForm.necessityCompanyList,
        sortArr: ["unitType"],
      });
    },
    async init() {
      this.getMyForm();
      this.getList();
      this.getList2();
    },
    getMyForm() {
      this.myForm = XEUtils.clone(this.form, true);
      console.log(this.myForm);
      this.hanleMyForm();
    },

    hanleMyForm() {
      this.myForm.necessityCompanyList = Object.values(this.myForm.tableList).flat();
      this.myForm.necessityCompanyList.forEach((item) => {
        item.unitType = this.$store.state.data.KV_MAP.select_name[item.unitType];
        item.proportion = item.proportion + "%";
        item.unitShortName = item.unitShortName || `暂不确定${item.unitType}`;
      });
      const otherCompanyList = this.otherUnitTypeListAll.filter(
        (item) => !this.paramsLabel.includes(item.dictLabel)
      );
      this.myForm.otherUnitList.forEach((item) => {
        item.proportion = item.proportion + "%";
        item.unitType = otherCompanyList.filter(
          (item1) => item1.dictCode == item.unitTypeId
        )[0].dictLabel;
        item.unitShortName = item.unitShortName || `暂不确定${item.unitType}`;
      });
      this.myForm.projectTypeListShow = this.myForm.projectTypeList
        ?.map((item) => item.typeName)
        ?.join(",");
      this.myForm.caiwuList = this.myForm.caiwuList
        .map((item) => item.userNickName)
        ?.join(",");
      this.myForm.yewuList = this.myForm.yewuList
        .map((item) => item.userNickName)
        ?.join(",");
      console.log(this.myForm);
    },
    async getList() {
      const { rows, total } = await projectParameter({
        ...this.queryParams,
        projectId: this.myForm.id,
      });
      this.myForm.Prod = rows;
      this.total = total;
    },
    goCompany(row) {
      this.$router.push({
        path: "/businessInformation/companyInformation",
        query: { id: row.unitId },
      });
    },
    cancel() {
      this.$emit("viewCancel");
    },
  },
};
</script>
