<template>
  <div>
    <el-dialog title="编辑项目收付款信息" :visible.sync="dialogVisible" width="1300px" :before-close="handleClose">
      <div>
        <span>项目名称：{{ $route.query.name }}</span>
        <span style="margin-left: 30px">类别：{{
          selectType == 1
            ? "常规业务收支款项（含助贷平台和资金方）"
            : "技术服务方支出款项"
        }}</span>
        <span style="margin-left: 30px">事项：{{ name }}</span>
      </div>
     
      <el-table :data="tableData.orderByItemName" style="width: 100%; margin: 12px 0" border="">
        <el-table-column width="50" prop="index" label="序号">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column width="150" prop="businessScenario" label="业务场景">
          <template slot-scope="scope">
            <div v-if="['收资产方','内部中转','付资金方'].includes(scope.row.orderBySerialNum[0].businessScenario)">{{ scope.row.orderBySerialNum[0].businessScenario }}</div>
            <div v-else class="required-input-wrap">
              <span class="required-star">*</span>
              <el-input v-model="scope.row.orderBySerialNum[0].businessScenario" clearable 
                @input="val => scope.row.orderBySerialNum[1].businessScenario = val" />
            </div>
          </template>
        </el-table-column>
        <el-table-column width="80" prop="" label="类型">
          <template slot-scope="scope">
            <div style="line-height: 40px" v-for="(item, index) in scope.row.orderBySerialNum" :key="index">
              {{
                item.traderType == 1
                  ? "收款人"
                  : item.traderType == 0
                    ? "付款人"
                    : item.traderType == 9
                      ? "收/付款人"
                      : ""
              }}
            </div>
          </template>
        </el-table-column>
         <!-- <el-table-column prop="" label="录入方式" width="120">
          <template slot-scope="scope">
            <div style="line-height: 40px" v-for="(item, index) in scope.row.orderBySerialNum" :key="index">
              <el-select @change="changeType(index, scope.$index)" v-model="item.inputType" style="width: 100%"
                placeholder="请选择">
                <el-option value="1" label="选择"></el-option>
                <el-option value="2" label="填写"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column> -->
       
    
        <el-table-column prop="" label="账户名称" min-width="220">
          <template slot-scope="scope">
            <div style="line-height: 40px" v-for="(item, index) in scope.row.orderBySerialNum" :key="index">
              <div class="required-input-wrap">
                <span v-if="(item.businessScenario === '收资产方' && item.traderType === '1') || (item.businessScenario === '收资产方' && item.traderType === '0') || (item.businessScenario === '付资金方')" class="required-star">*</span>
                <el-input v-model="item.accountName" placeholder="请输入账户名称" @input="() => syncAccountInfo(item)" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="账号" min-width="250">
          <template slot-scope="scope">
            <div style="line-height: 40px" v-for="(item, index) in scope.row.orderBySerialNum" :key="index">
              <div class="required-input-wrap">
                <span v-if="(item.businessScenario === '收资产方' && item.traderType === '1') || (item.businessScenario === '付资金方')" class="required-star">*</span>
                <el-select
                  v-model="item.accountNumberShow"
                  filterable
                  remote
                  allow-create
                  default-first-option
                  reserve-keyword
                  placeholder="输入账号或账号后四位检索"
                  :remote-method="(query) => getItem(query, item.traderType)"
                  :loading="loading"
                  @change="(value) => handleAccountChange(value, item)"
                  @focus="focus"
                >
                  <el-option
                    v-for="option in accountOptions"
                    :key="option.id"
                    :label="option.accountNumber"
                    :value="option.userName+'/'+option.accountNumber"
                  >
                    <span>{{ option.userName }}/{{ option.accountNumber }}</span>
                  </el-option>
                </el-select>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="开户行" min-width="240">
          <template slot-scope="scope">
            <div style="line-height: 40px" v-for="(item, index) in scope.row.orderBySerialNum" :key="index" class="  pr-6 relative ">
              <div class="required-input-wrap">
                <span v-if="(item.businessScenario === '收资产方' && item.traderType === '1') || (item.businessScenario === '付资金方')" class="required-star">*</span>
                <el-input v-model="item.bankOfDeposit" placeholder="请输入开户行" @input="() => syncAccountInfo(item)" />
              </div>
              <i v-show="(item.traderType==1&&item.businessScenario=='付资金方'&&scope.row.orderBySerialNum.length!=2)||(item.traderType==0&&item.businessScenario=='收资产方'&&scope.row.orderBySerialNum.length!=2)" @click="delItems(scope.row, index)" class="el-icon-close  absolute right-1 top-3 cursor-pointer	"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="200" prop="" label="操作">
          <template slot-scope="scope">
            <el-button type="text" v-if="['收资产方'].includes(scope.row.orderBySerialNum[0].businessScenario)" @click="addItempPayer(scope.row, scope.$index)">增加付款人</el-button>
            <el-button type="text" v-if="['付资金方'].includes(scope.row.orderBySerialNum[0].businessScenario)" @click="addItemRecipient(scope.row, scope.$index)">增加收款人</el-button>
            <el-button type="text" v-if="!['收资产方','付资金方'].includes(scope.row.orderBySerialNum[0].businessScenario)" @click="del(scope.row, scope.$index)">删除</el-button>
            <el-button type="text" v-if="scope.$index != 0" @click="moveUp(scope.$index)">上移</el-button>
            <el-button type="text" @click="moveDown(scope.$index)"
              v-if="scope.$index != tableData.orderByItemName.length - 1">下移</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button type="primary" size="mini" @click="add">+添加一组收付款人</el-button>
      <div style="display: flex; margin-top: 12px">
        <span style="flex-shrink: 0">备注：</span>
        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="tableData.remark">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">保 存</el-button>
      </span>
    </el-dialog>
    <SelectPay v-if="selectType" :traderType="selectIndex.traderType" @close="selectType = false"
      @submit="submitSelect" />
  </div>
</template>

<script>
import SelectPay from "./SelectPay.vue";
import { listTrader } from "@/api/oa/trader";
export default {
  components: {
    SelectPay,
  },
  props: {
    itemData: Object,
    type: Number,
    name: String,
  },
  data() {
    return {
      selectType: false,
      tableData: {},

      dialogVisible: true,
      selectIndex: null,
      editList: [],
      loading: false,
      accountOptions: [],
      isEdit:false
    };
  },
  mounted() {
    this.oldTableData = JSON.parse(JSON.stringify(this.itemData));
    this.tableData = JSON.parse(JSON.stringify(this.itemData));
    this.isEdit=false;
    console.log(this.tableData.orderByItemName,123)
    this.tableData.orderByItemName.forEach(item=>{
      item.orderBySerialNum.forEach(item1=>{
        if(item1.accountName||item1.accountNumber||item1.bankOfDeposit){
          this.isEdit=true
        }
      })
    })
    if (!this.isEdit) {
      this.oldTableData.orderByItemName = [];
      this.tableData.orderByItemName = [
        {
          serialNum: 1,
          orderBySerialNum: [
            {
              abbreviation: null,
              accountIdOfName: null,
              accountName: null,
              accountNumber: null,
              bankOfDeposit: null,
              oaTraderId: null,
              status: "0",
              traderType: "0",
              businessScenario:'收资产方',
            },
            {
              abbreviation: null,
              accountIdOfName: null,
              accountName: null,
              accountNumber: null,
              bankOfDeposit: null,
              oaTraderId: null,
              status: "0",
              traderType: "1",
              businessScenario:'收资产方',
            },
          ],
        },
        {
          serialNum: 2,
          orderBySerialNum: [
            {
              abbreviation: null,
              accountIdOfName: null,
              accountName: null,
              accountNumber: null,
              bankOfDeposit: null,
              oaTraderId: null,
              status: "0",
              traderType: "0",
              businessScenario:'内部中转',
            },
            {
              abbreviation: null,
              accountIdOfName: null,
              accountName: null,
              accountNumber: null,
              bankOfDeposit: null,
              oaTraderId: null,
              status: "0",
              traderType: "1",
              businessScenario:'内部中转',
            },
          ],
        },
        {
          serialNum: 3,
          orderBySerialNum: [
            {
              abbreviation: null,
              accountIdOfName: null,
              accountName: null,
              accountNumber: null,
              bankOfDeposit: null,
              oaTraderId: null,
              status: "0",
              traderType: "0",
              businessScenario:'付资金方',
            },
            {
              abbreviation: null,
              accountIdOfName: null,
              accountName: null,
              accountNumber: null,
              bankOfDeposit: null,
              oaTraderId: null,
              status: "0",
              traderType: "1",
              businessScenario:'付资金方',
            },
          ],
        },
      ];
      console.log(this.tableData);
    }
  },
  methods: {
    clearSelection(item) {
      item.oaTraderId = null
      item.accountName = ''
      item.accountNumber = ''
      item.bankOfDeposit = ''
    },

    submitSelect(v) {
      console.log(v);
      console.log(
        this.tableData.orderByItemName[this.selectIndex.index].orderBySerialNum[
        this.selectIndex.i
        ]
      );
      this.tableData.orderByItemName[this.selectIndex.index].orderBySerialNum[
        this.selectIndex.i
      ].oaTraderId = v.id;
      this.tableData.orderByItemName[this.selectIndex.index].orderBySerialNum[
        this.selectIndex.i
      ].accountName = v.userName;
      this.tableData.orderByItemName[this.selectIndex.index].orderBySerialNum[
        this.selectIndex.i
      ].accountNumber = v.accountNumber;
      this.tableData.orderByItemName[this.selectIndex.index].orderBySerialNum[
        this.selectIndex.i
      ].bankOfDeposit = v.bankOfDeposit;
      this.selectType = false;
    },
    select(index, i, traderType) {
      console.log(index, i);
      this.selectIndex = {
        index,
        i,
        traderType,
      };
      this.selectType = true;
    },
    getItem(accountNumber, traderType) {
      if (accountNumber) {
        this.loading = true;
        listTrader({
          isEnable: 'Y',
          accountNumber,
          traderType: traderType == 1 ? '1,9' : '0,9'
        }).then((response) => {
          this.accountOptions = response.rows;
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      } else {
        this.accountOptions = [];
      }
    },
 
    handleAccountChange(value, item) {
      item.accountNumber=value;
      const selectedAccount = this.accountOptions.find(option => (option.userName+'/'+option.accountNumber) === value);
      if (selectedAccount) {
        item.accountName = selectedAccount.userName;
        item.bankOfDeposit = selectedAccount.bankOfDeposit;
        item.oaTraderId = selectedAccount.id;
        item.accountNumber = selectedAccount.accountNumber;
        this.syncAccountInfo(item,true);
      }else{
        this.syncAccountInfo(item);
      }
      this.accountOptions=[];
      
    },
    focus(){
      this.accountOptions=[];
    },
    syncAccountInfo(item,isSelect=false) {
      item.inputType=isSelect?1:2;
      item.oaTraderId=isSelect?item.oaTraderId:null;
      // 判断是否是从accountOptions中选择的值
      const isFromOptions = this.accountOptions.some(option => 
        option.userName === item.accountName && option.accountNumber === item.accountNumber
      );

      if (item.businessScenario === '收资产方' && item.traderType === '1') {
        // 找到付资金方的付款人并赋值
        const fundPayer = this.tableData.orderByItemName.find(row => 
          row.orderBySerialNum[0].businessScenario === '付资金方'
        )?.orderBySerialNum.find(item => item.traderType === '0');
        if (fundPayer) {
          fundPayer.accountName = item.accountName;
          fundPayer.accountNumber = item.accountNumber;
          this.$set(fundPayer, 'accountNumberShow', isFromOptions ? item.accountName + '/' + item.accountNumber : item.accountNumber);
          fundPayer.bankOfDeposit = item.bankOfDeposit;
          fundPayer.oaTraderId =isSelect? item.oaTraderId:null;
          fundPayer.inputType = isSelect?1:2;
        }
      }

      if (item.businessScenario === '内部中转' && item.traderType === '1') {
        // 找到付资金方的付款人并赋值
        const fundPayer = this.tableData.orderByItemName.find(row => 
          row.orderBySerialNum[0].businessScenario === '付资金方'
        )?.orderBySerialNum.find(item => item.traderType === '0');
        if (fundPayer) {
          fundPayer.accountName = item.accountName;
          fundPayer.accountNumber = item.accountNumber;
          this.$set(fundPayer, 'accountNumberShow', isFromOptions ? item.accountName + '/' + item.accountNumber : item.accountNumber);
          fundPayer.bankOfDeposit = item.bankOfDeposit;
          fundPayer.oaTraderId = isSelect? item.oaTraderId:null;
          fundPayer.inputType = isSelect?1:2;
        }

        // 找到收资产方的收款人
        const assetReceiver = this.tableData.orderByItemName.find(row => 
          row.orderBySerialNum[0].businessScenario === '收资产方'
        )?.orderBySerialNum.find(item => item.traderType === '1');
        // 找到内部中转的付款人
        const internalTransferPayer = this.tableData.orderByItemName.find(row => 
          row.orderBySerialNum[0].businessScenario === '内部中转'
        )?.orderBySerialNum.find(item => item.traderType === '0');
        if (assetReceiver && internalTransferPayer) {
          internalTransferPayer.accountName = assetReceiver.accountName;
          internalTransferPayer.accountNumber = assetReceiver.accountNumber;
          if(assetReceiver.accountName){
            this.$set(internalTransferPayer, 'accountNumberShow', isFromOptions ? assetReceiver.accountName + '/' + assetReceiver.accountNumber : assetReceiver.accountNumber);
          }
          internalTransferPayer.bankOfDeposit = assetReceiver.bankOfDeposit;
          internalTransferPayer.oaTraderId = isSelect ? assetReceiver.oaTraderId : null;
          internalTransferPayer.inputType = isSelect ? 1 : 2;
        }
      }
    },
    addItempPayer(v,index){
      console.log(v,index);
      v.orderBySerialNum.splice(1, 0, {
        abbreviation: null,
        accountIdOfName: null,
        accountName: null,
        accountNumber: null,
        bankOfDeposit: null,
        oaTraderId: null,
        status: "0",
        traderType: "0",
        businessScenario:'收资产方',
      });
    },
    addItemRecipient(v,index){
      console.log(v,index)
      v.orderBySerialNum.splice(1, 0, {
        abbreviation: null,
        accountIdOfName: null,
        accountName: null,
        accountNumber: null,
        bankOfDeposit: null,
        oaTraderId: null,
        status: "0",
        traderType: "1",
        businessScenario:'付资金方',
      },);
    },
    delItems(row, index){
      row.orderBySerialNum.splice(index, 1);
    },
    del(v, index) {
      console.log(v);
      
      if (v.orderBySerialNum[0].id) {
        this.editList.push({ oldData: v, newData: null, type: "del" });
      }
      this.tableData.orderByItemName.splice(index, 1);
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.tableData.orderByItemName[index];
        this.$set(
          this.tableData.orderByItemName,
          index,
          this.tableData.orderByItemName[index - 1]
        );
        this.$set(this.tableData.orderByItemName, index - 1, temp);
      }
    },
    moveDown(index) {
      if (index < this.tableData.orderByItemName.length - 1) {
        const temp = this.tableData.orderByItemName[index];
        this.$set(
          this.tableData.orderByItemName,
          index,
          this.tableData.orderByItemName[index + 1]
        );
        this.$set(this.tableData.orderByItemName, index + 1, temp);
      }
    },
    add() {
      this.tableData.orderByItemName.push({
        serialNum: this.tableData.orderByItemName.length + 1,
        orderBySerialNum: [
          {
            abbreviation: null,
            accountIdOfName: null,
            accountName: null,
            accountNumber: null,
            bankOfDeposit: null,
            oaTraderId: null,
            status: "0",
            traderType: "1",
            businessScenario:'',
          },
          {
            abbreviation: null,
            accountIdOfName: null,
            accountName: null,
            accountNumber: null,
            bankOfDeposit: null,
            oaTraderId: null,
            status: "0",
            traderType: "0",
            businessScenario:'',
          },
        ],
      });
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      var flag = false;
      this.tableData.orderByItemName.forEach((v, i) => {
        v.orderBySerialNum.forEach(item => {
          // 业务场景必填
          if (!item.businessScenario) {
            flag = true;
            return;
          }
          // 收资产方的收款人全部必填
          if (item.businessScenario === '收资产方' && item.traderType === '1') {
            if (!item.accountName || !item.accountNumber || !item.bankOfDeposit) {
              flag = true;
              return;
            }
          }
          // 收资产方的付款人账户名称必填
          if (item.businessScenario === '收资产方' && item.traderType === '0') {
            if (!item.accountName) {
              flag = true;
              return;
            }
          }
          // 付资金方全部必填
          if (item.businessScenario === '付资金方') {
            if (!item.accountName || !item.accountNumber || !item.bankOfDeposit) {
              flag = true;
              return;
            }
          }
        });
        v.serialNum = i + 1;
        if (!v.orderBySerialNum[0].id) {
          this.editList.push({
            oldData: null,
            newData: v,
            type: "add",
          });
        }
        if (this.oldTableData.orderByItemName.length > 0) {
          this.oldTableData.orderByItemName.forEach((item, index) => {
            if (
              (item.orderBySerialNum[0].id &&
                v.orderBySerialNum[0].id &&
                item.orderBySerialNum[0].id == v.orderBySerialNum[0].id &&
                (item.orderBySerialNum[0].accountName !=
                  v.orderBySerialNum[0].accountName ||
                  item.orderBySerialNum[0].accountNumber !=
                  v.orderBySerialNum[0].accountNumber ||
                  item.orderBySerialNum[0].bankOfDeposit !=
                  v.orderBySerialNum[0].bankOfDeposit)) ||
              (item.orderBySerialNum[1]&&item.orderBySerialNum[1].id &&
                v.orderBySerialNum[1].id &&
                item.orderBySerialNum[1].id == v.orderBySerialNum[1].id &&
                (item.orderBySerialNum[1].accountName !=
                  v.orderBySerialNum[1].accountName ||
                  item.orderBySerialNum[1].accountNumber !=
                  v.orderBySerialNum[1].accountNumber ||
                  item.orderBySerialNum[1].bankOfDeposit !=
                  v.orderBySerialNum[1].bankOfDeposit))
            ) {
              this.editList.push({
                oldData: item,
                newData: v,
                type: "edit",
              });
            }
          });
        }
      });
      console.log(this.editList);
      if (flag) {
        this.$message.warning("请填写必填项");
        return;
      }
     
      this.$emit(
        "submit",
        this.tableData,
        this.editList,
        this.oldTableData.remark,
        this.tableData.remark
      );
    },
   
  },
};
</script>

<style lang="less" scoped>
.required-input-wrap {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 18px; /* 给左边留出空间 */
}
.required-star {
  color: #f56c6c;
  font-size: 16px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  line-height: 1;
}
.required-input-wrap .el-input,
.required-input-wrap .el-select {
  width: 100%;
}
</style>