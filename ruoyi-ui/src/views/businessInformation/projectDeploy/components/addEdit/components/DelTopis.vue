<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="580px"
      :before-close="handleClose"
    >
      <div v-if="type">
        <p>当前项目名称已被其他功能模块引用，不能删除</p>
        <div v-for="item in showList" :key="item">{{ item }}</div>
        
      </div>
      <div v-else>
        <p>删除项目名称，需要由管理员进行审核,点击下一步，将发起OA流程</p>
        <div style="color: red">删除后将无法恢复！</div>
        <div>审核结果请关注 [OA办公-我的流程]</div>
        <div>如果审核通过也将通过系统待办通知 [业务责任人] 和 [财务责任人]</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="next" v-if="!type">下一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    type: <PERSON>olean,
    showList: Array,
  },
  data() {
    return {
      dialogVisible: true,
    };
  },
  methods: {
    next() {
      this.$emit("next");
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>