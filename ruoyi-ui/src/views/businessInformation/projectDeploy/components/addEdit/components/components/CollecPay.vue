<template>
  <div>
    <div class="header"><span></span>项目收付款</div>
    <div class="solid"></div>
    <div style="color: #777">
      当本项目在OA发起对外支付流程时（从担保公司账户支付至客户账户），选择事项名称，可限定使用以下收付款人信息
    </div>
    <div style="font-weight: bold">常规业务收支款项（含助贷平台和资金方）</div>
    <el-table
      :data="tableData"
      style="width: 100%"
      border=""
      :cell-style="tableRowClassName"
    >
      <el-table-column align="center" width="140" prop="itemName" label="事项">
        <template slot-scope="scope">
          <div style="font-weight: bold">
            {{ namefilter(scope.row.itemName) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" width="200" prop="remark" label="备注" />
      <el-table-column
        align="center"
        width="50"
        prop=""
        label="序号"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.orderByItemName[0].orderBySerialNum[0].id">
            <div
              class="commodityDiscountAmount"
              :style="{
                height: item.orderBySerialNum.length * 42 + 'px',
                lineHeight: item.orderBySerialNum.length * 42 + 'px',
              }"
              v-for="(item, index) in scope.row.orderByItemName"
              :key="index"
            >
              {{ index + 1 }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="150"
        prop=""
        label="业务场景"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            v-if="scope.row.orderByItemName[0].orderBySerialNum[0].accountName"
          >
            <div
              class="commodityDiscountAmount"
              :style="{
                height: item.orderBySerialNum.length * 42 + 'px',
                lineHeight: item.orderBySerialNum.length * 42 + 'px',
              }"
              v-for="(item, index) in scope.row.orderByItemName"
              :key="index"
            >
              {{ item.orderBySerialNum[0].businessScenario }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="60"
        prop=""
        label="类型"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{
                v.traderType == 1
                  ? "收"
                  : v.traderType == 0
                  ? "付"
                  : v.traderType == 9
                  ? "收/付"
                  : ""
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="账户名称"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="账号"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountNumber }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="开户行"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.bankOfDeposit }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="简称"
        class-name="commodityDiscountAmount1"
        min-width="120"
      >
        <template slot="header" slot-scope="scope">
          <div>
            简称
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                在生成财务记账凭证时如果需要，简称可作为科目名称<br />
                通常内部公司需要此信息，由财务人员在 [收付款人] 中维护
              </div>
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.abbreviation || "-" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="所属账套"
        class-name="commodityDiscountAmount1"
        min-width="120"
      >
        <template slot="header" slot-scope="scope">
          <div>
            所属账套
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                在生成财务记账凭证时如果需要，可在指定账套来生成记账凭证<br />
                通常内部公司需要此信息，由财务人员在 [收付款人] 中维护
              </div>
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountIdOfName || "-" }}
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div style="font-weight: bold; margin-top: 12px">技术服务方支出款项</div>
    <el-table
      :data="tableData2"
      style="width: 100%"
      border=""
      :cell-style="tableRowClassName"
    >
      <el-table-column align="center" width="140" prop="itemName" label="事项">
        <template slot-scope="scope">
          <div style="font-weight: bold">
            {{ namefilter(scope.row.itemName) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" width="200" prop="remark" label="备注" />
      <el-table-column
        align="center"
        width="50"
        prop=""
        label="序号"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.orderByItemName[0].orderBySerialNum[0].id">
            <div
              class="commodityDiscountAmount"
              :style="{
                height: item.orderBySerialNum.length * 42 + 'px',
                lineHeight: item.orderBySerialNum.length * 42 + 'px',
              }"
              v-for="(item, index) in scope.row.orderByItemName"
              :key="index"
            >
              {{ index + 1 }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="150"
        prop=""
        label="业务场景"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            v-if="scope.row.orderByItemName[0].orderBySerialNum[0].accountName"
          >
            <div
              class="commodityDiscountAmount"
              :style="{
                height: item.orderBySerialNum.length * 42 + 'px',
                lineHeight: item.orderBySerialNum.length * 42 + 'px',
              }"
              v-for="(item, index) in scope.row.orderByItemName"
              :key="index"
            >
              {{ item.orderBySerialNum[0].businessScenario }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="60"
        prop=""
        label="类型"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{
                v.traderType == 1
                  ? "收"
                  : v.traderType == 0
                  ? "付"
                  : v.traderType == 9
                  ? "收/付"
                  : ""
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="账户名称"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="账号"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountNumber }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="开户行"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.bankOfDeposit }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="简称"
        class-name="commodityDiscountAmount1"
        min-width="120"
      >
        <template slot="header" slot-scope="scope">
          <div>
            简称
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                在生成财务记账凭证时如果需要，简称可作为科目名称<br />
                通常内部公司需要此信息，由财务人员在 [收付款人] 中维护
              </div>
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.abbreviation || "-" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop=""
        label="所属账套"
        class-name="commodityDiscountAmount1"
        min-width="120"
      >
        <template slot="header" slot-scope="scope">
          <div>
            所属账套
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                在生成财务记账凭证时如果需要，可在指定账套来生成记账凭证<br />
                通常内部公司需要此信息，由财务人员在 [收付款人] 中维护
              </div>
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountIdOfName || "-" }}
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="header" style="margin-top: 20px"><span></span>项目信息费</div>
    <div class="solid"></div>
    <div style="color: #777">
      在 [财务项目管理] 功能中的信息费公司与费率信息，用于计算信息费
    </div>
    <el-table :data="tableData3" style="width: 100%" border="">
      <el-table-column width="140" prop="companyName" label="信息费公司名称" />
      <el-table-column prop="accountNumber" label="账号" />
      <el-table-column prop="bankOfDeposit" label="开户行" />
      <el-table-column prop="" label="费率">
        <template slot-scope="scope">
          <span v-if="scope.row.rate">{{ scope.row.rate }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="税率">
        <template slot-scope="scope">
          <span v-if="scope.row.taxRate">{{ scope.row.taxRate }}%</span>
        </template>
      </el-table-column>
    </el-table>
    <EditDialog v-if="editType" @close="editType = false" />
  </div>
</template>

<script>
import { getCwProjectFeeInfo } from "@/api/oa/deploy";
import { getReceiptAndPaymentInfo, getItemInfo } from "@/api/oa/deploy";
import EditDialog from "./EditDialog.vue";
export default {
  components: {
    EditDialog,
  },
  props: {
    id: Number,
  },
  data() {
    return {
      editType: false,
      tableData: [{}],
      tableData2: [],
      tableData3: [],
      nameList: [],
    };
  },

  mounted() {
    getCwProjectFeeInfo(this.id).then((res) => {
      this.tableData3 = res.data.oaProjectDeployCwProjectFeeInfoList;
      this.tableData3.forEach((item) => {
        if (item.rate) {
          item.rate = item.rate.toFixed(2);
        }
        if (item.taxRate) {
          item.taxRate = item.taxRate.toFixed(2);
        }
        if (item.id && !item.companyId) {
          this.$set(item, "noWap", true);
          item.companyName = "暂不确定公司";
          item.accountNumber = "暂不确定";
          item.bankOfDeposit = "暂不确定";
        }
      });
    });
    getItemInfo().then((res) => {
      this.nameList = res.data;
    });
    getReceiptAndPaymentInfo(this.id).then((res) => {
      if (res.data) {
        this.tableData =
          res.data.oaProjectDeployReceiptAndPaymentInfoVoList[0].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType;
        this.tableData2 =
          res.data.oaProjectDeployReceiptAndPaymentInfoVoList[1].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType;
      }
    });
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      // 根据 row.special 字段的值返回类名
      return row &&
        row.orderByItemName &&
        !row.orderByItemName[0].orderBySerialNum[0].id
        ? "background:#f8f8f9"
        : "background:#fff";
    },
    namefilter(v) {
      let data = this.nameList.find((item) => {
        return item.code == v;
      });
      if (data) {
        return data.info;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  span {
    display: inline-block;
    width: 3px;
    height: 14px;
    background: #1890ff;
    margin-right: 10px;
  }
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin: 12px 0;
}
/deep/ .el-table__cell {
  padding: 0 !important;
}

/deep/ .commodityDiscountAmount1 .is-center .is-leaf .el-table__cell {
  line-height: 42px !important;
  height: 42px;
}

/deep/ .commodityDiscountAmount1 .cell {
  padding: 0 !important;

  .commodityDiscountAmount {
    border-bottom: 1px solid #ebeef5;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: 40px;
  }

  div:last-child {
    border: none !important;
  }
}
.commodityDiscountAmount2 {
  border-bottom: 1px solid #ebeef5;
  line-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
 