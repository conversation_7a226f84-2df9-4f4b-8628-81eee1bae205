<template>
  <div>
    <el-dialog
      title="选择收付款人"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div>请从 [业务信息配置-收付款人] 中选择</div>
      <div style="display: flex; margin: 12px 0">
        <div class="item">
          <span>账户名称</span
          ><el-input
            placeholder="请输入"
            v-model="params.userName"
            style="width: 200px"
          ></el-input>
        </div>
        <div class="item">
          <span>账号</span
          ><el-input
            placeholder="请输入"
            v-model="params.accountNumber"
            style="width: 200px"
          ></el-input>
        </div>
        <div class="item">
          <span>开户行</span
          ><el-input
            placeholder="请输入"
            v-model="params.bankOfDeposit"
            style="width: 200px"
          ></el-input>
        </div>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="reset">重置</el-button>
      </div>
      <el-table :data="traderList" style="width: 100%" border="">
        <el-table-column width="50" align="center" label="选择">
          <template slot-scope="scope">
            <el-radio
              v-model="active"
              :label="scope.row.id"
              @input="changeRadio($event, scope.row)"
            ></el-radio>
          </template>
        </el-table-column>
        <el-table-column width="120" label="类型">
          <template slot-scope="scope">
            {{
              scope.row.traderType == 1
                ? "收款人"
                : scope.row.traderType == 0
                ? "付款人"
                : scope.row.traderType == 9
                ? "收/付款人"
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column label="账户名称" prop="userName"></el-table-column>
        <el-table-column label="账号" prop="accountNumber"></el-table-column>
        <el-table-column label="开户行" prop="bankOfDeposit"></el-table-column>
      </el-table>
      <pagination
        v-show="params.total > 0"
        :total="params.total"
        :page.sync="params.pageNum"
        :limit.sync="params.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="submit"
          :disabled="!selectData"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { listTrader } from "@/api/oa/trader";
export default {
  props: {
    traderType: String,
  },
  data() {
    return {
      traderList: [],
      active: null,
      dialogVisible: true,
      params: {
        total: 0,
        pageNum: 1,
        pageSize: 20,
        traderType: [],
        isEnable:'Y',
        userName: "",
        accountNumber: "",
        bankOfDeposit: "",
      },
      selectData: null,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    changeRadio(v, row) {
      this.selectData = row;
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit", this.selectData);
    },
    getList() {
      if (this.traderType == 1) {
        this.params.traderType = ["1", "9"];
      } else {
        this.params.traderType = ["0", "9"];
      }
      listTrader({
        ...this.params,
        traderType: this.params.traderType.join(","),
      }).then((response) => {
        this.traderList = response.rows;
        this.params.total = response.total;
      });
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        total: 0,
        pageNum: 1,
        pageSize: 20,
        traderType: [],
        userName: "",
        accountNumber: "",
        bankOfDeposit: "",
      };
      this.getList();
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  margin: 0;
  margin-right: 16px;
  span {
    margin-right: 5px;
  }
}
/deep/ .el-table__cell {
  padding: 10px !important;
}
/deep/ .el-table td.el-table__cell div {
  padding: 10px !important;
}
/deep/ .el-radio__label {
  display: none !important;
}
</style>