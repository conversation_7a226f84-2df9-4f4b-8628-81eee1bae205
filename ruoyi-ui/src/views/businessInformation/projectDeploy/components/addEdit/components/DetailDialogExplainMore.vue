<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="说明"
      :visible.sync="innerValue"
      width="550px"
    >
      <div>
        <div>通常项目由资金方、资产方/反担保方、担保公司3种公司组成，还可添加其他类型的公司作为补充，例某些项目可包含技术服务方</div>
        <div>项目名称自动生成： [担保公司]简称 + [资产方]简称 + [资金方]简称</div>
        
        <div class="mt-3">产品分类为 [保函类] 的项目，由内部担保公司、资产方（非必填）、受益人/其代表组成，还可添加其他类型的公司作为补充</div>
        <div>项目名称自动生成： [内部担保公司]简称 + [资产方]简称 + [受益人/其代表]简称</div>
        <div class="mt-3">产品分类为 [不良出表类] 的项目，由内部担保公司、资金方或外部担保公司（二选一）组成，还可添加其他类型的公司作为补充</div>
         <div>项目名称自动生成： [内部担保公司]简称 + [资金方]简称/ [外部担保公司]简称</div>
        <div class="mt-3">产品分类为 [分保业务类] 的项目，由内部担保公司、外部担保公司组成，还可添加其他类型的公司作为补充</div>
         <div>项目名称自动生成： [内部担保公司]简称 + [外部担保公司]简称</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {},
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>
