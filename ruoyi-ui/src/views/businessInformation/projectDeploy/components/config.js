export default {
  paramsLabel: Object.freeze(["担保公司", "资产方", "资金方","内部餐饮公司"]),
  isNecessityList: Object.freeze({ 0: "必要公司", 1: "其他公司" }),

  rules: Object.freeze({
    companyNo: [{ required: true, message: "请选择所属公司", trigger: "blur" }],
    projectTypeList: [{ required: true, message: " ", trigger: "change" }],
    businessTypeList: [{ required: true, message: " ", trigger: "change" }],
    // channelType: [{ required: true, message: "请选择", trigger: "blur" }],
    
  }),
  columnsCompany: Object.freeze([
    { label: "公司名称", key: "companyName", },
    { label: "简称", prop: "companyShortName", },
  ]),
  columnsExamine: Object.freeze([
    { label: "", prop: "unitType", },
    { label: "公司简称", prop: "unitShortName", },
    { label: "公司全称", prop: "unitName", },
  ]),
  projectListTableInit: [
    // {
    //   myId: 1,
    //   isNecessity: "0",
    //   companyType: 0,
    //   tableItem: [
    //     {
    //       myId: 1,
    //       companyId: "",
    //       companyName: "",
    //       companyShortName: "",
    //       companyType: 0,
    //       proportion: "100",
    //       isNecessity: "0",
    //     },
    //   ],
    // },
    // {
    //   myId: 2,
    //   isNecessity: "0",
    //   companyType: 1,
    //   tableItem: [
    //     {
    //       myId: 2,
    //       companyId: "",
    //       companyName: "",
    //       companyShortName: "",
    //       companyType: 1,
    //       proportion: "100",
    //       isNecessity: "0",
    //     },
    //   ],
    // },
    // {
    //   myId: 3,
    //   isNecessity: "0",
    //   companyType: 2,
    //   tableItem: [
    //     {
    //       myId: 3,
    //       companyId: "",
    //       companyName: "",
    //       companyShortName: "",
    //       companyType: 2,
    //       proportion: "100",
    //       isNecessity: "0",
    //     },
    //   ],
    // },
    // {
    //   myId: 'otherId',
    //   isNecessity: "1",
    //   companyType: 3,
    //   tableItem: [
    //     {
    //       myId: 'otherId',
    //       unitTypeId: "",
    //       companyId: "",
    //       companyName: "",
    //       companyShortName: "",
    //       companyType: 3,
    //       proportion: "100",
    //       isNecessity: "1",
    //     },
    //   ],
    // },
  ],
  viewDetail: Object.freeze([
    { label: '项目名称', value: 'projectName', css: "700" },
    { label: '创建时间', value: 'createTime' },
    { label: '项目类型', value: 'projectTypeListShow', css: "700" },
    { label: '创建人', value: 'createBr' },
    { label: '产品分类', value: 'businessTypeList' },
    { label: '启用状态', value: 'isEnable' },
    // { label: '渠道方', value: 'channelName' },
    // { label: '渠道方类型', value: 'channelType' },
    { label: '业务责任人', value: 'yewuList' },
    { label: '财务责任人', value: 'caiwuList' },
    // { label: '授信金额', value: 'creditAmount' },

  ]),
  statusList: Object.freeze([
    { label: "正常", value: "Y", raw: { listClass: 'primary' } },
    { label: "停用", value: "N", raw: { listClass: 'danger' } },
  ]),
  columnsViewProd: Object.freeze([
    { label: "产品", prop: "productName", minWidth: 150 },
    { label: "产品编码", prop: "productNo", minWidth: 150 },
    { label: "系统", key: "systemNo", width: 100 },
    { label: "说明", prop: "description", minWidth: 350 },
  ]),
  columnsViewCompany: Object.freeze([
    { label: "类型", prop: "unitType", width: 150 },
    { label: "公司名称", prop: "unitName", minWidth: 250 },
    { label: "公司简称", prop: "unitShortName", minWidth: 150 },
    { label: "占比", prop: "proportion", width: 100 },
    { label: "操作", key: "operate", width: 100 },
  ]),
  columnsViewMode: Object.freeze([
    { label: "模块", prop: "model", },
    { label: "是否使用", key: "isUse", },
  ]),
  viewDetailChangeEditType: Object.freeze([
    { label: '业务责任人', value: 'yewuList' },
    { label: '当前审核状态', value: 'checkStatus' },
    { label: '财务责任人', value: 'caiwuList' },
  ]),
  checkStatusObj: Object.freeze({
    0: '待业务审核',
    1: '待财务审核',
    2: '待业务管理员审核',
  })
};
