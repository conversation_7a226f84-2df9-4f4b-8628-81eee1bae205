<template>
  <div class="app-container" id="projectDeploy">
    <div v-if="!open">
      <div style="width: 100%" class="mb-3.5">
        <div>配置智慧平台统一的项目名称，各功能模块使用的项目名称都将从此处引用</div>
        <div class="flex items-center">
          <div>新增项目需在 [项目立项管理] 中创建</div>
          <div
            @click="openExplain = true"
            class="ml-3 cursor-pointer"
            style="color: #1890ff"
          >
            更多说明
          </div>
        </div>
      </div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            v-model.trim="queryParams.projectName"
            placeholder="请输入"
            clearable
            size="small"
            @clear="handleQuery"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="担保公司" prop="custNo">
          <el-select
            v-model="queryParams.custNo"
            placeholder="请选择"
            filterable
            clearable
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in companyList.custNoList"
              :key="dict.id"
              :label="dict.companyShortName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资产方" prop="partnerNo">
          <el-select
            v-model="queryParams.partnerNo"
            placeholder="请选择"
            filterable
            clearable
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in companyList.partnerNoList"
              :key="dict.id"
              :label="dict.companyShortName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资金方" prop="fundNo">
          <el-select
            v-model="queryParams.fundNo"
            placeholder="请选择"
            filterable
            clearable
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in companyList.fundNoList"
              :key="dict.id"
              :label="dict.companyShortName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="isEnable">
          <el-select
            v-model="queryParams.isEnable"
            placeholder="请选择"
            filterable
            clearable
            @change="handleQuery"
            size="small"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="正常" value="Y"></el-option>
            <el-option label="停用" value="N"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型" prop="projectTypeIds">
          <el-select
            v-model="queryParams.projectTypeIds"
            placeholder="请选择"
            filterable
            clearable
            multiple
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in projectTypesList"
              :key="dict.id"
              :label="dict.dataName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品分类" prop="businessTypeIds">
          <el-select
            v-model="queryParams.businessTypeIds"
            placeholder="请选择"
            filterable
            clearable
            multiple
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in businessTypeList"
              :key="dict.id"
              :label="dict.dataName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <MoreSearch
          modelCode="PROJNAME"
          :params="queryParams"
          v-show="showMoreSearch"
          needHandel
        ></MoreSearch>

        <el-form-item class="relative bottom-0.5">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
          <el-button
            @click="
              showMoreSearch = !showMoreSearch;
              queryParams.moreSearch = undefined;
            "
            type="text"
            >更多搜索条件<i
              :class="showMoreSearch ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
          /></el-button>
        </el-form-item>
      </el-form>
      <!-- 导入excel表格用 -->
      <div style="display: flex">
        <div v-hasPermi="['projectDeploy:excelImport']">
          <p>上传收付款信息-项目信息费信息excel用</p>
          <el-upload
            ref="uploadExcel"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url"
            :before-remove="beforeRemove"
            :on-change="handleChange"
            :before-upload="beforeUpload"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            :file-list="upFileList"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
          <el-button size="mini" type="primary" @click="handleImport()"
            >上传入库</el-button
          >
        </div>
        <div v-hasPermi="['projectDeploy:excelImport']" style="margin-left:20px">
          <p>上传收付款信息-项目收付款信息excel用</p>
          <el-upload
            ref="uploadExcel1"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload1.headers"
            :action="upload1.url"
            :before-remove="beforeRemove1"
            :on-change="handleChange1"
            :before-upload="beforeUpload1"
            :on-progress="handleFileUploadProgress1"
            :on-success="handleFileSuccess1"
            :auto-upload="false"
            :file-list="upFileList1"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
          <el-button size="mini" type="primary" @click="handleImport1()"
            >上传入库</el-button
          >
        </div>
      </div>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            size="mini"
            style="margin-right: 16px"
            @click="$router.push({ path: '/businessInformationOther/dutyUser' })"
            v-hasPermi="['projectDeploy:responsible']"
            type="primary"
            >责任人管理</el-button
          >
          <!-- <el-button
            size="small"
            style="margin-left: 16px"
            type="primary"
            icon="el-icon-plus"
            v-hasPermi="['projectDeploy:addEdit']"
            @click="handleAdd"
            >新增项目</el-button
          > -->
          <el-switch
            @change="getList"
            v-model="changeMyProjectType"
            active-text="我担任责任人的项目"
          >
          </el-switch>
          <!-- <span v-if="changeEditType">视图：</span>
          <el-radio-group
            v-if="changeEditType"
            v-model="queryParams.selectType"
            @change="getList"
          >
            <el-radio-button label="0">全部项目</el-radio-button>
            <el-radio-button label="1"
              >待我审核<span v-if="countData.myCheckCount > 0"
                >({{ countData.myCheckCount }})</span
              ></el-radio-button
            >
            <el-radio-button label="3"
              >我负责的<span v-if="countData.myResponsibleCount > 0"
                >({{ countData.myResponsibleCount }})</span
              ></el-radio-button
            >
            <el-radio-button label="2"
              >我的提交<span v-if="countData.mySubmitCount > 0"
                >({{ countData.mySubmitCount }})</span
              ></el-radio-button
            >
          </el-radio-group>


          <el-button
            size="small"
            v-hasPermi="['projectDeploy:auditSwitch']"
            type="primary"
            @click="editTypeChange"
            >编辑是否需审核({{
              changeEditType ? "已开启" : "已关闭"
            }})</el-button
          > -->
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="deployList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" label="序号" width="50" :index="columnIndex" />
        <el-table-column
          label="项目名称"
          align="left"
          show-overflow-tooltip
          min-width="500"
        >
          <template slot-scope="scope">
            <div
              class="truncate ... cursor-pointer"
              style="color: #46a6ff"
              @click="projectDetail(scope.row)"
            >
              {{ scope.row.projectName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="项目类型"
          align="left"
          prop="projectTypeList"
          min-width="200"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.projectTypeList && scope.row.projectTypeList.length">
              <div
                class="mr-3 mt-1 border border-solid rounded px-2 flex h-6 items-center"
                style="
                  border-color: #cccccc;
                  background-color: #f2f2f2;
                  font-size: 13px;
                  width: fit-content;
                "
                v-for="(item, index) in scope.row.projectTypeList"
                :key="index"
              >
                {{ item && item.typeName }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="产品分类" align="left" min-width="200">
          <template slot-scope="scope">
            <div class="flex flex-wrap items-center">
              <div
                v-for="(item, index) in scope.row.businessTypeList"
                :key="index"
                class="mr-3 mt-1 border border-solid rounded px-2 flex h-6 items-center"
                style="border-color: #cccccc; background-color: #f2f2f2; font-size: 13px"
              >
                <div class="h-6 leading-6">{{ item && item.typeName }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="渠道方" align="left" min-width="200">
          <template slot-scope="scope">
            <span v-if="scope.row.channelType == 1">{{
              scope.row.channelName
            }}</span>
            <span v-else>
              <span v-if="scope.row.qudaofangList.length > 0">
                <span v-for="item in scope.row.qudaofangList" :key="item.id"
                  >{{ item.name }}，</span
                >
              </span>
            </span>
          </template>
        </el-table-column> -->
        <el-table-column label="产品" align="left" min-width="250">
          <template slot-scope="scope">
            <div v-if="scope.row.productList && scope.row.productList.length">
              <div v-for="(item, index) in scope.row.productList" :key="index">
                {{ item.productName }}
              </div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column
          label="审核状态"
          width="120"
          show-overflow-tooltip=""
          align="left"
          prop="remark"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.checkStatus == 0
                ? "新增项目审核中"
                : scope.row.checkStatus == 1
                ? "修改项目审核中"
                : scope.row.checkStatus == 2
                ? "删除项目审核中"
                : scope.row.checkStatus == 3 || scope.row.checkStatus === null
                ? "-"
                : ""
            }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column align="center" prop="companyName" width="200">
          <template slot="header" slot-scope="scope">
            <div>
              所属公司
              <el-tooltip
                class="item"
                effect="dark"
                content="默认用户只能看到所属公司与自己岗位所在公司相同的项目可以使用通用授权功能将自己可见的项目或公司授权给自己下级"
                placement="top-start"
              >
                <span class="relative bottom-1">①</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column
          label="业务责任人"
          width="200"
          show-overflow-tooltip=""
          align="left"
        >
          <template slot="header" slot-scope="scope">
            <div>
              业务责任人
              <el-tooltip
                class="item"
                effect="dark"
                content="当修改、删除项目时，将通过系统待办通知业务责任人与财务责任人。
                业务责任人业务总监设置，财务责任人可由财务总监设置"
                placement="top-start"
              >
                <span class="relative bottom-1">①</span>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <div class="truncate ...">
              {{
                scope.row.yewuList && scope.row.yewuList.map((item) => item.name).join()
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="财务责任人"
          width="200"
          show-overflow-tooltip=""
          align="left"
        >
          <template slot="header" slot-scope="scope">
            <div>
              财务责任人
              <el-tooltip
                class="item"
                effect="dark"
                content="当修改、删除项目时，将通过系统待办通知业务责任人与财务责任人。业务责任人业务总监设置，财务责任人可由财务总监设置"
                placement="top-start"
              >
                <span class="relative bottom-1">①</span>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <div class="truncate ...">
              {{
                scope.row.caiwuList && scope.row.caiwuList.map((item) => item.name).join()
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="启用状态" align="center" prop="isEnable" width="160">
          <template slot="header" slot-scope="scope">
            <div>
              启用状态
              <el-tooltip
                class="item"
                effect="dark"
                content="停用状态的项目，将不能在智慧平台其他功能模块中引用并创建新的信息项，但不影响已经创建的历史项目"
                placement="top-start"
              >
                <span class="relative bottom-1">①</span>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <dict-tag :options="statusList" :value="scope.row.isEnable" />
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="left"
          width="220"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="projectDetail(scope.row)"
              >项目详情</el-button
            >

            <el-button
              size="mini"
              type="text"
              v-if="scope.row.checkStatus == 3 || scope.row.checkStatus === null"
              v-hasPermi="['projectDeploy:addEdit']"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <!-- <el-button size="mini" type="text" @click="editRcored(scope.row)"
              >编辑记录</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <AddEdit
      v-if="open"
      :projects="projects"
      @edit="handleUpdate"
      :form="form"
      :openTypes="openTypes"
      :companyList="companyList"
      :companytypeList="companytypeList"
      :changeEditType="changeEditType"
      @deletePro="deletePro"
      @cancel="cancel"
      @submitForm="submitForm"
      @next="next"
    />
    <el-dialog :title="addOrUpdate" :visible.sync="updateOrAddDeilog" width="30%">
      <span>{{ this.addOrUpdateText }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cencelclose()">取 消</el-button>
        <el-button type="primary" @click="submitData()">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="设置"
      :show-close="false"
      :visible.sync="editDialogType"
      width="600px"
    >
      <p>
        编辑需审核：<el-switch v-model="changeEditType2"> </el-switch
        >{{ changeEditType2 ? "已开启" : "已关闭" }}
      </p>
      <p>
        开启[编辑需审核]功能后，如果新增、修改、删除项目名称，必须由所属的财务、业务责任人审核后才能生效
      </p>
      <p>如果财务责任人进行了修改，则需要业务责任人审核确认</p>
      <p>如果业务责任人进行了修改，则需要财务责任人审核确认</p>
      <p>如果本条规则尚未设置财务、业务责任人，则需先设置才能进行修改</p>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            editDialogType = false;
            changeEditType2 = false;
          "
          >取 消</el-button
        >
        <el-button type="primary" :disabled="!changeEditType2" @click="submitChangeEdit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <editData
      :oldData="oldForm"
      :newData="addEditParams"
      :companytypeList="companytypeList"
      :type="editType"
      v-if="editDataType"
      @close="editDataType = false"
      @submit="submitEdit"
      :userList="userList"
    />
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="editSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">
        {{
          editType == 0
            ? "新增项目名称"
            : editType == 1
            ? "修改项目名称"
            : "删除项目名称"
        }}申请已提交!
      </p>
      <p style="text-align: center">
        业务总监将在OA系统待办中收到待审核通知，审核通过后立即生效。请及时沟通以尽快完成审核
      </p>
      <p style="text-align: center">
        <span style="font-weight: bold">业务总监</span>：{{ yewuList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <examine
      v-if="examineType"
      :detailData="detailData"
      :examineId="examineId"
      :examineData="examineData"
      @close="examineType = false"
      :userList="userList"
      @submit="submitExamine"
      :changeEditType="changeEditType"
      :companyList="companyList"
      :companytypeList="companytypeList"
    />
    <know
      v-if="knowType"
      :detailData="detailData"
      @close="knowType = false"
      @confirm="submitKnow"
    />
    <editRecord
      :changeEditType="changeEditType"
      :companytypeList="companytypeList"
      :companyList="companyList"
      :recordId="recordId"
      v-if="editRecordType"
      @close="editRecordType = false"
    />

    <DetailDialogExplain v-model="openExplain" />
  </div>
</template>

<script>
import DetailDialogExplain from "./components/DetailDialogExplain.vue";
import AddEdit from "./components/addEdit";
import config from "./components/config";
import XEUtils from "xe-utils";

import { allCompanyList } from "@/api/oa/processTemplate";
import examine from "./examine.vue";
import editRecord from "./editRecord.vue";
import editData from "./editData.vue";
import know from "./know.vue";
import { getDicts } from "@/api/system/dict/data";
import {
  checkEditExamine,
  editExamine,
  addNewEditInfoDeploy,
  deployCheck,
  getuser,
  deployConfirm,
  deployDetail,
  deployviewCount,
} from "@/api/oa/voucharRules";
import {
  listDeploy,
  getDeploy,
  delDeploy,
  addDeploy,
  addNewEditInfo,
} from "@/api/oa/deploy";
import {
  companyList,
  newCompanySelectList,
} from "@/api/businessInformation/companyInformation";
import { getToken } from "@/utils/auth";
import { splicingListByCode } from "@/api/businessInformation/productInformation";

export default {
  name: "ProjectDeploy",
  components: {
    editData,
    examine,
    know,
    editRecord,
    DetailDialogExplain,
    AddEdit,
  },

  data() {
    return {
      changeMyProjectType: false,
      ...config,
      countData: {},
      examineType: false,
      editType: 0,
      personData: {},
      oldForm: {},
      editDataType: false,
      knowType: false,
      detailData: null,
      changeEditType2: false,
      editDialogType: false,
      changeEditType: false,
      pType: false,
      addOrUpdate: "",
      addOrUpdateText: "",
      updateOrAddDeilog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      deployList: [],
      // 是否显示新增编辑
      open: false,
      openTypes: "edit",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        projectTypeIds: undefined,
        selectType: 0,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        isEnable: null,
        createBr: null,
        updateBr: null,
        businessTypeIds: undefined,
      },
      companyList: {
        custNoList: [],
        partnerNoList: [],
        fundNoList: [],
      },
      openExplain: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
        projectType: [{ required: true, message: "请选择项目类型", trigger: "change" }],
        companyNo: [{ required: true, message: "请选择公司", trigger: "change" }],
      },
      //业务类型
      projectTypesList: [],
      companytypeList: [],
      userList: {},
      editSubmitType: false,
      yewuList: null,
      projects: [],
      recordId: null,
      editRecordType: false,
      statusList: Object.freeze([
        { label: "正常", value: "Y", raw: { listClass: "primary" } },
        { label: "停用", value: "N", raw: { listClass: "danger" } },
      ]),
      addEditParams: {},
      examineId: 0,
      examineData: {},
      //excel导入begin
      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/oasystem/deploy/excelFileUpload",
      },
      upload1: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/oasystem/deploy/excelFileUploadForReceiptAndPaymentInfo",
      },
      //文件集合
      upFileList: [],
      upFileList1: [],
      //excel导入end
      showMoreSearch: false,
      businessTypeList: [],
    };
  },

  created() {
    this.init();
    if (this.$route.query.oaNotifyStep) {
      this.queryParams.selectType = this.$route.query.oaNotifyStep;
    }
    allCompanyList().then((response) => {
      this.projects = response;
    });
    getuser().then((res) => {
      this.userList = res;
      this.yewuList = this.userList.yewu.map((item) => item.label);
    });

    this.getList();
  },
  async activated() {
    await this.getDicts();
    this.getCompanyList();
    this.getList();
  },
  methods: {
    async init() {
      await this.checkEditExamine();
      if (this.$route.query.id) {
        const { data } = await getDeploy(this.$route.query.id);
        this.form = data;
        this.oldForm = JSON.parse(JSON.stringify(data));
        this.openTypes = "view";
        this.open = true;

        return;
      }
      await this.getDicts();
      this.getCompanyList();
      this.getProjectTypesList();
      this.getBusinessTypeList();
    },
    async getProjectTypesList() {
      const { rows } = await splicingListByCode({ firstDataCode: "project_type" });
      this.projectTypesList = rows;
    },
    async getBusinessTypeList() {
      const { rows } = await splicingListByCode({ firstDataCode: "business_type" });
      this.businessTypeList = rows;
    },
    async getDicts() {
      return new Promise((resolve) => {
        const tasks = [];
        const params = ["project_type", "company_type"];
        params.forEach((item) => {
          tasks.push(getDicts(item));
        });
        Promise.all(
          tasks.map((p) => {
            //.then返回需要的东西 .catch返回一些错误信息
            return p
              .then((e) => {
                return p;
              })
              .catch((err) => {
                return "错误了";
              });
          })
        )
          .then((res) => {
            // this.projectTypesList = res[0].data.map((item) => {
            //   return {
            //     label: item.dictLabel,
            //     value: item.dictValue,
            //     code: item.dictCode,
            //   };
            // });
            this.companytypeList = res[1].data;
            resolve();
          })
          .catch((reason) => {
            console.log(reason);
          });
      });
    },
    getCompanyList() {
      let taskParams = ["cust", "partner", "fund"];
      // this.paramsLabel.forEach((item) => {
      //   const itemParam = this.companytypeList.filter(
      //     (item1) => item1.dictLabel == item
      //   )[0]?.dictCode;
      //   taskParams.push(itemParam);
      // });
      const tasks = [];
      taskParams.forEach((item) => {
        tasks.push(newCompanySelectList({ selectCode: item, modelCode: "PROJNAME" }));
      });
      Promise.all(
        tasks.map((p) => {
          //.then返回需要的东西 .catch返回一些错误信息
          return p
            .then((e) => {
              return p;
            })
            .catch((err) => {
              return "错误了";
            });
        })
      )
        .then((res) => {
          this.companyList.custNoList = res[0];
          this.companyList.partnerNoList = res[1];
          this.companyList.fundNoList = res[2];
        })
        .catch((reason) => {
          console.log(reason);
        });
    },
    columnIndex(index) {
      return index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
    },
    editRcored(v) {
      this.recordId = v.id;
      this.editRecordType = true;
    },
    submitKnow() {
      deployConfirm({
        ...this.detailData,
        confirmFlag: 1,
        oaNotifyStep: this.queryParams.selectType,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("本条通知已删除");
          this.knowType = false;
          this.getList();
        }
      });
    },
    rulesConfirm(v) {
      deployDetail({ oaApplyType: 4, oaApplyId: v.oaApplyId || v.id }).then((res) => {
        if (res.code == 200) {
          this.detailData = res.data;
          this.knowType = true;
        }
      });
    },
    submitExamine(v, i, x) {
      //0通过1驳回
      if (v == 0) {
        this.$confirm("点击确定，本次修改将立即生效?", "审核通过", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            deployCheck({ ...this.detailData, rejectFlag: v, ...x }).then((res) => {
              if (res.code == 200) {
                this.examineType = false;
                this.$message.success("审核已通过");
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      } else {
        deployCheck({
          ...this.detailData,
          rejectFlag: v,
          checkRejectInfo: i,
          ...x,
        }).then((res) => {
          if (res.code == 200) {
            this.examineType = false;
            this.$message.success("审核已驳回");
            this.getList();
          }
        });
      }
    },
    getDetail(v) {
      this.examineId = v.id;
      this.examineData = { ...v };
      deployDetail({ oaApplyType: 4, oaApplyId: v.oaApplyId || v.id }).then((res) => {
        if (res.code == 200) {
          this.detailData = res.data;
          this.examineType = true;
        }
      });
    },
    submitEdit(v) {
      // const tempArr=['custList','fundList','partnerList','otherUnitList']
      // tempArr.forEach(item=>{
      //   v[item].forEach(item1=>{
      //   delete item1.unitShortName;
      //   })
      // })
      addNewEditInfoDeploy({ ...v }).then((res) => {
        if (res.code == 200) {
          this.editSubmitType = true;
        }
      });
    },
    next(value) {
      this.addEditParams = { ...value };
      this.editDataType = true;
    },
    checkEditExamine() {
      return checkEditExamine({ projectType: 1 }).then((res) => {
        if (res.code == 200) {
          this.changeEditType = res.msg == 0 ? false : true;
          if (!this.changeEditType) {
            this.queryParams.selectType = 0;
          }
        }
      });
    },
    editTypeChange() {
      if (!this.changeEditType) {
        this.editDialogType = true;
      } else {
        this.$confirm(
          "关闭后，当前已提交但未被业务管理员审核完成的项目名称，将会恢复为提交前的状态！",
          "关闭编辑需审核功能",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            editExamine({ projectType: 1, isEnable: 'N' }).then((res) => {
              if (res.code == 200) {
                this.$message.success("审核需编辑已关闭");
                this.checkEditExamine();
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      }
    },
    submitChangeEdit() {
      editExamine({ projectType: 1, isEnable: 'Y' }).then((res) => {
        if (res.code == 200) {
          this.$message.success("审核需编辑已开启");
          this.editDialogType = false;
          this.checkEditExamine();
          this.getList();
        }
      });
    },
    cencelclose() {
      this.updateOrAddDeilog = false;
    },

    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      const deletPatams = ["custNo", "partnerNo", "fundNo"];
      deletPatams.forEach((item) => {
        if (this.queryParams[item] == "") {
          this.queryParams[item] = undefined;
        }
      });
      this.queryParams.selectType = this.changeMyProjectType ? 1 : 0;
      listDeploy(this.queryParams).then((response) => {
        this.deployList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      // deployviewCount(this.queryParams).then((res) => {
      //   this.countData = res.data;
      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    close() {
      this.editSubmitType = false;
      this.delSubmitType = false;
      this.editDataType = false;
      this.open = false;
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        projectTypeIds: null,
        businessTypeIds: null,
        isEnable: null,
        companyNo: null,
        createBr: null,
        createTime: null,
        updateBr: null,
        updateTime: null,
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 导入按钮操作  begin*/
    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error("上传文件大小不能为 0 MB");
        return false;
      } else if (fileSize < maxSize) {
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {},
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      // if (response.code === 500) {
      //   this.$modal.msgError("文件类型错误，请删除文件后重新选择后上传！");
      // }
      if (response.remark === "200") {
        // this.respObj = response;
        // this.dialogOpen = true;
        this.$refs.uploadExcel.clearFiles();
      }
    },
    // 提交上传文件
    handleImport() {
      this.$refs.uploadExcel.submit();
    },
    beforeRemove(file, upFileList) {
      // this.buttonFlag = false;
    },
    //点击文件列表中已上传文件进行下载
    // handlePreview(file) {
    //   this.buttonFlag = true;
    // },
    handleChange(file, fileList) {
      // if (file !== null) {
      //   this.buttonFlag = true;
      // }
    },
    /** 导入按钮操作  end*/
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.moreSearch = undefined;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.openTypes = "edit";
      this.reset();
      this.open = true;
      this.pType = false;
      this.editType = 0;
    },
    projectDetail(row) {
      this.openTypes = "view";
      const id = row.id || this.ids;
      getDeploy(id).then((response) => {
        this.oldForm = JSON.parse(JSON.stringify(response.data));
        this.form = response.data;
        this.open = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      console.log("----");
      this.openTypes = "edit";
      this.reset();
      const id = row.id || this.ids;
      getDeploy(id).then((response) => {
        this.oldForm = JSON.parse(JSON.stringify(response.data));
        this.form = response.data;
        this.open = true;

        this.editType = 1;
      });
    },
    /** 提交按钮 */
    submitForm(value) {
      this.addEditParams = { ...value };
      if (this.form.id != null) {
        this.addOrUpdate = "保存项目修改";
        this.addOrUpdateText = "是否确定保存对项目的修改？";
        this.updateOrAddDeilog = true;
      } else {
        this.updateOrAddDeilog = true;
        this.addOrUpdateText = "是否确定创建此项目？";
        this.addOrUpdate = "新增项目";
      }
    },
    deletePro(value) {
      if (this.changeEditType) {
        this.addEditParams = { ...value };
        this.editType = 2;
        this.editDataType = true;
        return;
      }
      this.handleDelete(this.form);
    },
    async submitData() {
      const addEditParams = XEUtils.clone(this.addEditParams, true);
      // const tempArr=['custList','fundList','partnerList','otherUnitList']
      // tempArr.forEach(item=>{
      //   addEditParams[item].forEach(item1=>{
      //   delete item1.unitShortName;
      //   })
      // })
      if (!this.changeEditType) {
        await addDeploy(addEditParams);
      } else {
        await addNewEditInfo(addEditParams);
      }
      if (this.form.id != null) {
        this.$modal.msgSuccess("保存成功");
      } else {
        this.$modal.msgSuccess("新增项目成功");
      }
      this.open = false;
      this.updateOrAddDeilog = false;
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除项目名称为" + row.projectName + "的数据项？")
        .then(function () {
          return delDeploy(ids);
        })
        .then(() => {
          this.open = false;
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/deploy/export",
        {
          ...this.queryParams,
        },
        `deploy_${new Date().getTime()}.xlsx`
      );
    },

    /** 导入按钮操作  begin*/
    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload1(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error("上传文件大小不能为 0 MB");
        return false;
      } else if (fileSize < maxSize) {
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress1(event, file, fileList) {},
    // 文件上传成功处理
    handleFileSuccess1(response, file, fileList) {
      // if (response.code === 500) {
      //   this.$modal.msgError("文件类型错误，请删除文件后重新选择后上传！");
      // }
      if (response.remark === "200") {
        // this.respObj = response;
        // this.dialogOpen = true;
        this.$refs.uploadExcel1.clearFiles();
      }
    },
    // 提交上传文件
    handleImport1() {
      this.$refs.uploadExcel1.submit();
    },
    beforeRemove1(file, upFileList) {
      // this.buttonFlag = false;
    },
    //点击文件列表中已上传文件进行下载
    // handlePreview(file) {
    //   this.buttonFlag = true;
    // },
    handleChange1(file, fileList) {
      // if (file !== null) {
      //   this.buttonFlag = true;
      // }
    },
    /** 导入按钮操作  end*/
  },
};
</script>
<style lang="less" scoped>
.item {
  margin-bottom: 12px;
  /deep/ .el-input__inner {
    width: 250px !important;
  }
  span {
    display: inline-block;
    width: 100px;
    font-weight: bold;
    text-align: right;
    margin-right: 12px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>
