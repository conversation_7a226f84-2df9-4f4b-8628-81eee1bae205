<template>
  <div>
    <el-dialog
      :title="type == 0 ? '新增' : type == 1 ? '编辑' : '删除'"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="data_content">
        <div v-if="type == 1">
          <p class="title2">修改内容：</p>
          <div class="table">
            <div class="left">
              <div style="background: #e4e4e4"></div>
              <div>项目名称</div>
              <div :style="{height:Math.max(myOldData.custList.length,newData.custList.length)*30+'px' }">担保公司</div>
              <div >资产方</div>
              <div :style="{height:Math.max(myOldData.fundList.length,newData.fundList.length)*30+'px' }">资金方</div>
              <div :style="{height:Math.max(myOldData.otherUnitList.length,newData.otherUnitList.length,1)*30+'px' }">其他公司</div>
              <div :style="{height:Math.max(myOldData.projectTypeList.length,newData.projectTypeList.length,1)*30+'px' }">项目类型</div>
              <div :style="{height:Math.max(myOldData.businessTypeList.length,newData.businessTypeList.length,1)*30+'px' }">业务类型</div>
              <div>所属公司</div>
              <div>启用状态</div>
            </div>
            <div class="center">
              <div style="background: #e4e4e4">修改前</div>
               <el-tooltip class="item" effect="dark" :content="myOldData.projectName" placement="top-start" style="padding:0 0 0 10px;margin:0">
                <div class="truncate ...">{{ myOldData.projectName }}</div>
              </el-tooltip>
              
              <div :style="{height:Math.max(myOldData.custList.length,newData.custList.length)*30+'px' }">
                  <div v-for="(item,index) in myOldData.custList">{{item.unitShortName}}</div>
              </div>
              <div>{{myOldData.partnerList[0]&&myOldData.partnerList[0].unitShortName}}</div>
              <div :style="{height:Math.max(myOldData.fundList.length,newData.fundList.length)*30+'px' }">
                  <div v-for="(item,index) in myOldData.fundList">{{item.unitShortName}}</div>
              </div>
              <div :style="{height:Math.max(myOldData.otherUnitList.length,newData.otherUnitList.length,1)*30+'px' }">
                  <div v-for="(item,index) in myOldData.otherUnitList">{{item.unitShortName}}</div>
              </div>
             <div :style="{height:Math.max(myOldData.projectTypeList.length,newData.projectTypeList.length,1)*30+'px' }">
                  <div v-for="(item,index) in myOldData.projectTypeList">{{item.typeName}}</div>
              </div>
              <div :style="{height:Math.max(myOldData.businessTypeList.length,newData.businessTypeList.length,1)*30+'px' }">
                  <div v-for="(item,index) in myOldData.businessTypeList">{{item.typeName}}</div>
              </div>
              <div>{{myOldData.companyName}}</div>
              <div>{{ myOldData.isEnable == "Y" ? "开启" : "关闭" }}</div>
            </div>
            <div class="right">
              <div style="background: #e4e4e4">修改后</div>
               <el-tooltip  effect="dark" :content="newData.projectName" placement="top-start" style="padding:0 0 0 10px;margin:0">
                <div class="truncate ...">{{ newData.projectName }}</div>
              </el-tooltip>
             <div :style="{height:Math.max(myOldData.custList.length,newData.custList.length)*30+'px' }">
                  <div v-for="(item,index) in newData.custList">{{item.unitShortName}}</div>
              </div>
              <div>{{newData.partnerList[0]&&newData.partnerList[0].unitShortName}}</div>
               <div :style="{height:Math.max(myOldData.fundList.length,newData.fundList.length)*30+'px' }">
                  <div v-for="(item,index) in newData.fundList">{{item.unitShortName}}</div>
              </div>
              <div :style="{height:Math.max(myOldData.otherUnitList.length,newData.otherUnitList.length,1)*30+'px' }">
                  <div v-for="(item,index) in newData.otherUnitList">{{item.unitShortName}}</div>
              </div>
               <div :style="{height:Math.max(myOldData.projectTypeList.length,newData.projectTypeList.length,1)*30+'px' }">
                  <div v-for="(item,index) in newData.projectTypeList">{{item.typeName}}</div>
              </div>
              <div :style="{height:Math.max(myOldData.businessTypeList.length,newData.businessTypeList.length,1)*30+'px' }">
                  <div v-for="(item,index) in newData.businessTypeList">{{item.typeName}}</div>
              </div>
              <div>{{myOldData.companyName}}</div>
              <div>{{ newData.isEnable == "Y" ? "开启" : "关闭" }}</div>
            </div>
          </div>
        </div>
        <p class="title2">
          *{{ type == 0 ? "新增项目" : type == 1 ? "修改" : "删除项目" }}说明：
        </p>
        <el-input
          type="textarea"
          :rows="4"
          show-word-limit
          maxlength="200"
          placeholder="请输入内容"
          v-model="textarea"
        >
        </el-input>
        <div style="display: flex; margin-top: 16px">
          <p style="margin-right: 100px">
            <span style="font-weight: bold">提交人</span>：{{ userName }}
          </p>
        </div>
        <el-divider></el-divider>
        <p style="text-align: center">
          点击提交后，将由业务总监进行审核，审核完成后立即生效：
        </p>
        <p style="text-align: center">
          <span style="font-weight: bold">业务总监</span>：{{ yewuList }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit" :disabled="!textarea"
          >提 交</el-button
        >
      </span>
    </el-dialog>

    <!-- <div class="btn-container">
      <el-button @click="shangyibu">取消</el-button>
      <el-button type="primary" @click="nextSubmit" :disabled="!textarea"
        >提交</el-button
      >
    </div> -->
  </div>
</template>

<script>
import Cookies from "js-cookie";
import XEUtils from "xe-utils";

export default {
  props: {
    oldData: Object,
    newData: Object,
    userList: Object,
    type: Number,
    companytypeList:Array
  },
  data() {
    return {
      dialogVisible: true,
      textarea: "",
      data1: null,
      data2: null,
      userName: "",
      yewuList: null,
      myOldData:{},
      otherUnitTypeListAll: [],

    };
  },
  mounted() {
    this.myOldData=XEUtils.clone(this.oldData,true);
    this.handleForm();
    this.yewuList = this.userList.yewu.map((item) => item.label);
        this.userName = sessionStorage.getItem('userNickName')

    console.log(this.personData);
    this.data1 = JSON.parse(JSON.stringify(this.myOldData));
    this.data2 = JSON.parse(JSON.stringify(this.newData));
    console.log(this.data1, this.data2);
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleForm(){
      if(this.type!=1) return;
      this.myOldData.custList.forEach(item=>item.unitShortName=item.unitShortName||'暂不确定担保公司');
      this.myOldData.partnerList.forEach(item=>item.unitShortName=item.unitShortName||'暂不确定资产方');
      this.myOldData.fundList.forEach(item=>item.unitShortName=item.unitShortName||'暂不确定资金方');
      this.myOldData.otherUnitList.forEach(item=>{
        this.companytypeList.forEach(item1=>{
          if(item1.dictCode==item.unitTypeId){
            item.unitShortName=`${item1.dictLabel}:${item.unitShortName||'暂不确定'+item1.dictLabel}`
          }
        })
      });
    },
    submit() {
      let data = {
        ...this.newData,
        oaProjectDeployBoOldDataId: this.oldData.oaProjectDeployBoOldDataId,
        editInfo: this.textarea,
        editType: this.type,
      };
      this.$emit("submit", data);
    },
  },
};
</script>

<style lang="less" scoped>
.data_content {
  width: 95%;
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
  }
  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    .left,
    .center,
    .right {
      width: 45%;
      >div {
        height: 30px;
        width: 100%;
        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
      }
    }
    .left div {
      font-weight: bold;
    }
    .left {
      width: 10%;
    }
  }
}
</style>