<template>
  <div class="p-5">
    <el-tabs v-model="queryType" type="card" @tab-click="handleClick">
      <el-tab-pane label="我的代理人" name="1">
        <p class="mb-0">指定 [授权代理人] ，由代理人替您进行通用授权工作</p>
        <p class="mb-0">
          代理人能够将您拥有的权限授权给您的直属下级。但代理人并不会有您的实际业务权限。取消代理人后，代理人已授权的结果依然有效
        </p>
        <div class="mt-3">
          <span class="mr-3">状态</span>
          <el-select style="width: 200px" v-model="type" @change="getList">
            <el-option label="正常" :value="0"></el-option>
            <el-option label="失效" :value="1"></el-option>
            <el-option label="全部" :value="2"></el-option>
          </el-select>
        </div>
        <el-button
          @click="addUser"
          class="mt-3"
          size="mini"
          type="primary"
          plain
          >+ 添加代理人</el-button
        >
        <MyTable
          class="mt-3"
          :columns="dataColumns"
          :queryParams="queryParams"
          :source="dataList"
          :showIndex="true"
          ref="table"
        >
          <template #user="{ record }">
            <span class="user"
              ><el-tag>{{ record.agentId | user }}</el-tag></span
            >
          </template>
          <template #moduleLength="{ record }">
            <span
              v-if="record.agencyModulesCount == record.authorityModulesCount"
              >全部</span
            >
            <span v-else
              >{{ record.agencyModulesCount }}/{{
                record.authorityModulesCount
              }}</span
            >
          </template>
          <template #permissionTime="{ record }">
            {{ record.permissionTime ? record.permissionTime : "永久" }}
          </template>
          <template #status="{ record }">
            <el-tag v-if="record.status == 1" type="danger">失效</el-tag>
            <el-tag v-else>正常</el-tag>
          </template>
          <template #opertion="{ record }">
            <el-button type="text" @click="detail(record)">查看详情</el-button>
            <el-button type="text" @click="edit(record)">编辑</el-button>
            <el-button type="text" @click="cancelAuth(record)" v-if="record.status != 1"
              >停止代理</el-button
            >
            <el-button type="text" @click="authRecord(record)"
              >代理人授权记录</el-button
            >
          </template>
        </MyTable>
      </el-tab-pane>
      <el-tab-pane label="我是代理人" name="2">
        <p class="mb-0">
          您已被其他用户（被代理人）指定为他的 [授权代理人]
          ，可替被代理人执行通用授权工作
        </p>
        <p class="mb-0">
          您能够将被代理人拥有的权限授权给他的直属下级，但您并不会拥有被代理人的实际业务权限
        </p>
        <div class="mt-3">
          <span class="mr-3">状态</span>
          <el-select style="width: 200px" v-model="type" @change="getList">
            <el-option label="正常" :value="0"></el-option>
            <el-option label="失效" :value="1"></el-option>
            <el-option label="全部" :value="2"></el-option>
          </el-select>
        </div>

        <MyTable
          class="mt-3"
          :columns="dataColumns2"
          :queryParams="queryParams"
          :source="dataList"
          :showIndex="true"
        >
          <template #user="{ record }">
            <span class="user"
              ><el-tag>{{ record.principalId | user }}</el-tag></span
            >
          </template>
          <template #moduleLength="{ record }">
            <span
              v-if="record.agencyModulesCount == record.authorityModulesCount"
              >全部</span
            >
            <span v-else
              >{{ record.agencyModulesCount }}/{{
                record.authorityModulesCount
              }}</span
            >
          </template>
          <template #permissionTime="{ record }">
            {{ record.permissionTime ? record.permissionTime : "永久" }}
          </template>
          <template #status="{ record }">
            <el-tag v-if="record.status == 1" type="danger">失效</el-tag>
            <el-tag v-else>正常</el-tag>
          </template>
          <template #opertion="{ record }">
            <el-button type="text" @click="detail(record)">查看详情</el-button>

            <el-button type="text" @click="authRecord(record)"
              >我的授权记录</el-button
            >
          </template>
        </MyTable>
      </el-tab-pane>
    </el-tabs>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <AddingUsers
      :subordinateList="subordinateList"
      :isProxy="true"
      v-if="addingUsersType"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />

    <OperationRecord
      :recordList="recordList"
      v-if="operationRecordType"
      @close="operationRecordType = false"
    />
    <Detail
      :userList="userList"
      v-if="detailType"
      @close="detailType = false"
      :authDetail="authDetail"
    />
    <SelectAuth
      :editData="editData"
      v-if="selectAuthType"
      @close="selectAuthType = false"
      @submit="submitSelectAuth"
      @edit="editSelectAuth"
    />
  </div>
</template>

<script>
import {
  getAgentList,
  getAgencyAuthRecord,
  subordinateForAgencyAuth,
  agentHandle,
  cancelAgent,
} from "@/api/businessInformation/proxyAuth";
import {
  subordinate,
  getUserListAll,
} from "@/api/businessInformation/currencyCompany";
import OperationRecord from "./components/OperationRecord.vue";
import Detail from "./components/Detail.vue";
import SelectAuth from "./components/SelectAuth.vue";
let that = "";
export default {
  components: {
    Detail,
    OperationRecord,
    SelectAuth,
  },
  data() {
    return {
      editData: null,
      selectAuthType: false,
      detailType: false,
      queryType: "1",
      subordinateList: [],
      operationRecordType: false,
      type: 2,
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dataList: [{}],
      dataColumns: [
        {
          label: "代理人",
          prop: "user",
          key: "user",
          fixed: "left",
        },
        {
          label: "代理模块",
          prop: "moduleLength",
          key: "moduleLength",
        },
        {
          label: "添加日期",
          prop: "startTime",
        },
        {
          label: "代理有效期",
          prop: "permissionTime",
          key: "permissionTime",
        },
        {
          label: "状态",
          prop: "status",
          key: "status",
        },

        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      dataColumns2: [
        {
          label: "被代理人",
          prop: "user",
          key: "user",
          fixed: "left",
        },
        {
          label: "代理模块",
          prop: "moduleLength",
          key: "moduleLength",
        },
        {
          label: "添加日期",
          prop: "startTime",
        },
        {
          label: "代理有效期",
          prop: "permissionTime",
          key: "permissionTime",
        },
        {
          label: "状态",
          prop: "status",
          key: "status",
        },

        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      changeType: false,
      authMethodType: false,
      addingUsersType: false,
      selectItemData: null,
      userList: [],
      recordList: [],
      authDetail: null,
      addUserId: null,
    };
  },
  created() {
    that = this;
    this.init();
  },
  mounted() {
    setTimeout(() => {
      if (this.$route.query.type) {
        this.queryType = this.$route.query.type;
        this.getList();
      }
    }, 100);
  },
  filters: {
    user(e) {
      if (that.userList.length > 0) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        if (data) {
           if(data){
        return data.nickName;
      }
        }
      }
    },
  },
  methods: {
    cancelAuth(e) {
      this.$confirm(
        `是否停止对用户[${this.user2(
          e.agentId
        )}]的代理人授权？停止后，代理人已完成授权的结果依然有效`,
        "停止代理",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          cancelAgent({ ids: e.ids }).then((res) => {
            if (res.code == 200) {
              this.$message.success("取消代理人成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    user2(e) {
      if (that.userList.length > 0) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        if (data) {
           if(data){
        return data.nickName;
      }
        }
      }
    },
    edit(e) {
      this.editData = e;
      this.selectAuthType = true;
    },
    submitSelectAuth(e, v) {
      console.log(e, v);
      let data = {
        agentId: this.addUserId,
        agencyModules: e,
        agencyTime: v ? this.$format(v, "yyyy-MM-dd") : null,
      };
      agentHandle({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message.success("添加代理人成功");
          this.selectAuthType = false;
          this.addingUsersType = false
          this.getList();
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    editSelectAuth(e, v, b, n) {
      let data = {
        ids: b,
        agentId: n,
        agencyModules: e,
        agencyTime: v ? this.$format(v, "yyyy-MM-dd") : null,
      };
      agentHandle({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message.success("修改代理信息成功");
          this.selectAuthType = false;
          this.getList();
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    detail(e) {
      this.authDetail = e;
      this.detailType = true;
    },
    authRecord(e) {
      getAgencyAuthRecord({ ids: e.ids.join(",") }).then((res) => {
        if (res.code == 200) {
          this.recordList = res.data;
          this.operationRecordType = true;
        }
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
      this.getList();
    },
    addUser() {
      this.editData = null
      subordinateForAgencyAuth().then((res) => {
        if (res.code == 200) {
          if (res.data.subordinateList && res.data.subordinateList.length > 0) {
            this.subordinateList = res.data;
            this.addingUsersType = true;
          } else {
            this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
          }
        }
      });
    },

    submitAddUser(e) {
      console.log(e);
      this.addUserId = e[0].userId;
      this.selectAuthType = true;
    },

    changeAuthType() {
      this.showCheckbox = true;
      this.dataColumns = [
        {
          label: "公司名称",
          prop: "companyName",
          width: "200",
        },
        {
          label: "简称",
          prop: "company",
          width: "100",
        },
      ];
      setTimeout(() => {
        this.authType = true;
      }, 50);
    },
    getUser() {
      getUserListAll().then((res) => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    init() {
      this.getUser();
      this.getList();
    },
    getList() {
      getAgentList({
        ...this.queryParams,
        queryType: this.queryType,
        status: this.type == 2 ? null : this.type,
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          this.queryParams.total = res.total
        }
      });
    },
    submitDelet(e) {
      console.log(e);
    },
  },
};
</script>

<style>
</style>