<template>
  <div>
    <el-dialog
      title="操作记录"
      :visible.sync="dialogVisible"
      width="1200px"
      :before-close="handleClose"
    >
      <MyTable
        class="mt-3"
        border
        :columns="dataColumns"
        :queryParams="queryParams"
        :source="recordList"
      >
        <template #authDimensionality="{ record }">
          {{
            record.authDimensionality == 1
              ? "项目"
              : record.authDimensionality == 2
              ? "公司"
              : record.authDimensionality == 3
              ? "个例"
              : "所有"
          }}
        </template>
        <template #moduleType="{ record }">
          <div v-for="item in record.moduleType" :key="item">
            {{ item | moduleName }}
          </div>
        </template>
        <template #handleType="{ record }">
          {{ record.handleType == 1 ? "新增" : "取消" }}
        </template>
        <template #roleType="{ record }">
          {{ record.roleType | roleName }}
        </template>
        <template #responseName="{ record }">
          <div v-for="item in record.responseName" :key="item">{{ item }}</div>
        </template>
        <template #thirdNameList="{ record }">
          <span>{{ record.thirdNameList.join(",") }}</span>
        </template>
      </MyTable>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    recordList: Array,
  },
  data() {
    return {
      dataList: [],
      dialogVisible: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dataColumns: [
        {
          label: "操作日期",
          prop: "handleDate",
          width: "130",
        },
        {
          label: "授权维度",
          prop: "authDimensionality",
          key: "authDimensionality",
        },
        {
          label: "授权模板",
          prop: "authTemplateName",
        },
        {
          label: "授权模块",
          prop: "moduleType",
          key: "moduleType",
        },
        {
          label: "操作类型",
          prop: "handleType",
          key: "handleType",
        },
        {
          label: "授权项",
          prop: "responseName",
          key: "responseName",
        },
        {
          label: "功能角色",
          prop: "roleType",
          key: "roleType",
        },
        {
          label: "授权用户",
          prop: "thirdNameList",
          key: "thirdNameList",
        },
        {
          label: "授权到期时间",
          prop: "permissionTime",
        },
      ],
    };
  },
  filters: {
    moduleName(e) {
      if (e == "OALAUNCH") {
        return "OA流程发起权限";
      } else if (e == "OAVIEW") {
        return "OA流程查看权限";
      } else if (e == "FINANCESYS") {
        return "智慧财务系统";
      } else if (e == "FINANCEPROJ") {
        return "财务项目管理";
      } else if (e == "CARBOOLMANGER") {
        return "车贷绿本出入库";
      } else if (e == "PROJSETUP") {
        return "项目立项管理";
      } else if (e == "ARCHIVES") {
        return "档案管理";
      } else if (e == "INFORMATION") {
        return "资料管理";
      } else if (e == "PERSONNEL") {
        return "人事管理";
      } else if (e == "LICENSE") {
        return "证照管理";
      } else if (e == "ATTENDANCE") {
        return "考勤管理";
      } else if (e == "PAYROLL") {
        return "薪酬中心";
      } else if (e == "DATAREPORT") {
        return "数据报表";
      } else if (e == "ECHARTS") {
        return "Echarts";
      } else if (e == "DATATOP") {
        return "智慧数据系统首页";
      } else if (e == "PROJNAME") {
        return "项目名称";
      }
    },
    roleName(e) {
      if (e == 1) {
        return "会计";
      } else if (e == 2) {
        return "出纳";
      } else if (e == 3) {
        return "业务";
      } else if (e == 4) {
        return "项目经理";
      } else if (e == 5) {
        return "风险经理";
      } else if (e == 6) {
        return "综合管理部主任";
      } else if (e == 7) {
        return "项目责任人";
      } else if (e == 8) {
        return "主管会计";
      } else if (e == 9) {
        return "财务管理员";
      } else if (e == 10) {
        return "业务管理员";
      } else if (e == 11) {
        return "风险管理员";
      } else if (e == 12) {
        return "综合部管理员";
      } else if (e == 99) {
        return "出纳";
      }
    },
  },
  mounted() {
    console.log(this.recordList);
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>