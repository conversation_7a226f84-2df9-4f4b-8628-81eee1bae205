<template>
  <div>
    <el-dialog title="查看详情" :visible.sync="dialogVisible" width="700px" :before-close="handleClose">
      <div class="item">
        <div><span>代理人：</span>{{ authDetail.agentId | user }}</div>
        <div><span>被代理人：</span>{{ authDetail.principalId | user }}</div>
      </div>
      <div class="item">
        <div><span>添加日期：</span>{{ authDetail.startTime }}</div>
        <div><span>代理有效期：</span>{{ authDetail.permissionTime ? authDetail.permissionTime : '永久' }}</div>
      </div>
      <el-divider></el-divider>
      <div>
        <span style="font-weight: bold">代理模块：</span>{{ authDetail.authorityModulesCount }}
      </div>
      <el-checkbox-group disabled="" v-model="authDetail.agencyModules">
        <el-checkbox style="width: 120px;margin-top: 10px;" v-for="item in authDetail.authorityModuleType" :label="item"
          :key="item">{{ item | moduleName }}</el-checkbox>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
let that = "";
export default {
  props: {
    authDetail: Object,
    userList: Array,
  },
  data() {
    return {
      dialogVisible: true,
      checkedCities: [],
    };
  },
  filters: {
    moduleName(e) {
      if (e == "OALAUNCH") {
        return "OA流程发起权限";
      } else if (e == "OAVIEW") {
        return "OA流程查看权限";
      } else if (e == "FINANCESYS") {
        return "智慧财务系统";
      } else if (e == "FINANCEPROJ") {
        return "财务项目管理";
      } else if (e == "CARBOOLMANGER") {
        return "车贷绿本出入库";
      } else if (e == "PROJSETUP") {
        return "项目立项管理";
      } else if (e == "ARCHIVES") {
        return "档案管理";
      } else if (e == "INFORMATION") {
        return "资料管理";
      } else if (e == "PERSONNEL") {
        return "人事管理";
      } else if (e == "LICENSE") {
        return "证照管理";
      } else if (e == "ATTENDANCE") {
        return "考勤管理";
      } else if (e == "PAYROLL") {
        return "薪酬中心";
      } else if (e == "DATAREPORT") {
        return "数据报表";
      } else if (e == "ECHARTS") {
        return "Echarts";
      } else if (e == "DATATOP") {
        return "智慧数据系统首页";
      } else if (e == "PROJNAME") {
        return "项目名称";
      } else if (e == "NOTICE") {
        return "通知公告";
      } else if (e == "PERFORMANCE") {
        return "绩效考核";
      } else if (e == "JGINFORMATION") {
        return "监管报送资料";
      } else if (e == "OFFSUPPLY") {
        return "办公用品领用";
      } else if (e == "BADSYSTEM") {
        return "不良资产系统";
      }else if (e == "SXINFORMATION") {
        return "授信及贷后资料";
      }
    },
    user(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
  },
  created() {
    that = this;
  },
  mounted() {
    console.log(this.authDetail);
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  display: flex;
  justify-content: space-between;

  div {
    span {
      font-weight: bold;
    }
  }
}
</style>