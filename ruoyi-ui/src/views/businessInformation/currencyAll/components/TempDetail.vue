<template>
  <div>
    <el-dialog
      title="模板详情"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <MyTable
        v-if="dataList.length > 0"
        class="mt-3"
        :columns="dataColumns"
        :source="dataList"
      >
        <template #check="{ record }">
          {{ record.check ? record.check : "禁止" }}
        </template>
        <template #word="{ record }">
          {{ record.word ? record.word : " " }}
        </template>
      </MyTable>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTempDetail,
  getTemConfig,
} from "@/api/businessInformation/authTemplate";
export default {
  props: {
    detail: Object,
  },
  data() {
    return {
      detailData: null,
      dataList: [],
      dialogVisible: true,
      dataColumns: [
        {
          label: "模块",
          prop: "moduleName",
          minWidth: "130",
        },
        {
          label: "授权情况",
          key: "check",
        },
        {
          label: "指定功能角色",
          key: "word",
          showOverflowTooltipField: true,
          minWidth: "340",
        },
      ],
    };
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      getTemConfig({
        flag: "all",
        agencyUserId: this.$store.state.principalId,
      }).then((r) => {
        this.dataList = r.data;
        this.dataList.forEach((item) => {
          item.word = "";
          this.detail.userModuleCodeAndRoleList.forEach((i) => {
            if (item.code == i.code) {
              item.check = "允许";
              if (item.tempCheckData[0].tempCheckData.length > 0) {
                item.tempCheckData[0].tempCheckData.forEach((c, x) => {
                  i.role.forEach((b) => {
                    console.log(c.tempCode, b);
                    if (c.roleCode == b) {
                      item.word += c.choiceDescription + ",";
                    }
                  });
                });
              }
            }
          });
        });
      });
    },

    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>