<template>
  <div class="p-5">
    <div class="top_auth" v-if="haveAuthList[0]&&haveAuthList[0].showFlag">
      您已指定 [授权代理人] ，由代理人替您进行通用授权工作
      <el-button style="margin-left: 16px" type="text" @click="toProxy(1)"
        >查看详情></el-button
      >
    </div>
    <div
      v-if="haveAuthList[1]&&haveAuthList[1].showFlag"
      class="top_auth"
      style="background: #fffbe6; display: flex; justify-content: space-between"
    >
      <span>
        您已被指定为 [授权代理人]
        ，可替被代理人执行通用授权的工作。请在右侧选择您当前作为授权人的身份
        <el-button @click="toProxy(2)" style="margin-left: 16px" type="text"
          >查看详情 ></el-button
        ></span
      >
      <span>
        当前授权身份：<el-select
          style="width: 180px"
          v-model="personId"
          clearable=""
          @change="changeAuthPerson"
        >
          <el-option
            v-for="item in authPersonList"
            :key="item.principalId"
            :label="item.createNickName + item.createUserStatus"
            :value="item.principalId"
          ></el-option
        ></el-select>
      </span>
    </div>
    <p class="mb-0">设置智慧平台中默认拥有所有权限的用户</p>
    <p class="mb-0">
      此设置优先级高于按项目、按公司设置的权限，在此处设置的用户，在其他权限设置界面无法被修改
    </p>

    <div class="mt-3">
      <el-input
        class="mr-3"
        v-model="queryParams.queryName"
        style="width: 200px"
        placeholder="请输入用户名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary" @click="getList()"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
    <div class="mt-3">
      <span>
        <el-button
          size="mini"
          type="primary"
          v-hasPermi="['currencyAll:addPerson']"
          @click="addPerson"
          >+ 添加用户</el-button
        >
        <el-button
          size="mini"
          v-hasPermi="['authTemplate:all']"
          type="primary"
          @click="toTemp"
          >授权模板管理</el-button
        >
      </span>
    </div>

    <MyTable
      class="mt-3"
      v-show="userList.length > 0"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template v-slot:h_authorizedUserList="">
        <div>
          授权用户
          <el-tooltip
            class="item"
            effect="dark"
            content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </template>
      <template #authorizedUserList="{ record }">
        <div class="item_div">
          <span class="user" @click="getUserData(record.id)">
            {{ record.id | user
            }}<span style="color: #cccccc">{{ record.id | userStatus }}</span>
          </span>
        </div>
      </template>
      <template #userName="{ record }">
        {{ record.id | userName }}
      </template>
      <template #status="{ record }">
        {{ record.id | userStatus2 }}
      </template>
      <template #dept="{ record }">
        <span v-for="(v, i) in userPostListAll" :key="i">
          <span v-if="v.userId == record.id">{{ v.deptName }},</span>
        </span>
      </template>
      <template #post="{ record }">
        <span v-for="(v, i) in userPostListAll" :key="i">
          <span v-if="v.userId == record.id">{{ v.postName }},</span>
        </span>
      </template>
      <template #time="{ record }">
        {{
          record.permissionType == 2
            ? $format(record.permissionTime, "yyyy-MM-dd")
            : "永久授权"
        }}
      </template>
      <template #opertion="{ record }">
        <el-button type="text" @click="detail(record)">查看授权详情</el-button>
        <el-button
          type="text"
          v-hasPermi="['currencyAll:cencelAuth']"
          @click="delUser(record)"
          >- 取消授权</el-button
        >
      </template>
    </MyTable>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <UserDepPostSelect
      title="user"
      v-model="userDepPostSelectType"
      @on-submit-success-user="userSuccess"
      @submit="submitAddUser"
    />

    <AuthMethod
      v-if="authMethodType"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
      type="all"
    />
    <SelectAuthTime
      @submit="submitDate"
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
    <TempDetail
      :detail="detailData"
      v-if="tempDetailType"
      @close="tempDetailType = false"
    />
    <UserDetail2
      :userId="userId"
      authType="所有"
      v-if="userDetailType"
      @close="userDetailType = false"
    />
  </div>
</template>
  <script>
import { haveAgencyQuery } from "@/api/businessInformation/proxyAuth";

import {
  newAuthority,
  getUserListAll,
  subordinate,
  newAuthTemp,
  queryCancelUser,
  cancelAuthorization,
  queryAllCancelUser,
  cancelAllAuthorization,
} from "@/api/businessInformation/currencyCompany";
import { authTempUser } from "@/api/businessInformation/authTemplate";
import { userPostSetList } from "@/api/system/post";
import TempDetail from "./components/TempDetail";
let that = "";
export default {
  components: {
    TempDetail,
  },
  data() {
    return {
      authPersonList: [],
      haveAuthList: [],
      personId: "",
      userId: "",
      userDetailType: false,
      tempDetailType: false,
      detailData: null,
      selectItemData: null,
      userList: [],
      userDepPostSelectType: false,
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        custId: null,
        pageSize: 10,
        total: 0,
        queryType: "all",
        queryName: "",
        queryCode: "ALL",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      },
      dataList: [],

      dataColumns: [
        {
          label: "授权用户",
          prop: "authorizedUserList",
          key: "authorizedUserList",
          width: "180",
          isHSlot: true,
        },
        {
          label: "账号",
          key: "userName",
          width: "120",
        },
        {
          label: "状态",
          key: "status",
          width: "100",
        },
        {
          label: "所属部门",
          key: "dept",
          width: "170",
        },
        {
          label: "所属岗位",
          key: "post",
          width: "120",
        },
        {
          label: "授权期限",
          key: "time",
          width: "180",
        },
        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,

      openSelect: false,
      selectList: [],
      userPostListAll: [],
      selectAuthUsers: [],
      selectAuthMethList: [],
    };
  },
  created() {
    that = this;
    haveAgencyQuery({ agencyType: 3 }).then((res) => {
      if (res.code == 200) {
        this.haveAuthList = res.data;
        if (
          this.haveAuthList[1].principalList &&
          this.haveAuthList[1].principalList.length > 0
        ) {
          this.authPersonList = this.haveAuthList[1].principalList;
          this.authPersonList.push({
            createNickName:'本人',
            principalId:null,
            createUserStatus:''
          })
          console.log('---');
          this.personId = this.authPersonList[0].principalId;
          this.$store.state.principalId = this.personId;
        }
      }
    });
    this.init();
  },
  watch: {
    "$store.state.changeAuthType": {
      handler(newval, oldval) {
        console.log(newval);
        this.changeAuthType();
      },
      deep: true,
    },
    "$store.state.principalId": {
      handler(newval, oldval) {
        console.log(newval);
        this.init();
      },
      deep: true,
    },
  },

  filters: {
    user(e) {
      console.log(e, "===");
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
       if(data){
        return data.nickName;
      }
    },
    userName(e) {
      console.log(e);
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      return data.userName;
    },
    userStatus(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data&&data.status == 0) {
        return "";
      } else {
        return "（已停用）";
      }
    },
    userStatus2(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data.status == 0) {
        return "正常";
      } else {
        return "停用";
      }
    },
  },
  methods: {
    toProxy(e) {
      this.$router.push({
        path: "/businessInformation/proxyAuth",
        query: {
          type: e,
        },
      });
    },
    getUserData(e) {
      console.log(e);
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      console.log(data);
      this.userId = data.userName;
      this.userDetailType = true;
    },
    delUser(v) {
      this.$confirm(
        `是否确认取消对用户[${this.getNickName(
          v.id
        )}]的授权？取消后不影响对用户在项目、公司等维度单独进行的授权`,
        "取消授权",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        cancelAuthorization({
          authorizedType: "all",
          authIdList: v.authIdList,
          unAuthorizedUserId:v.id,
          principalId: this.$store.state.principalId,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success("取消成功");
            this.getList();
          }
        });
      });
    },
    detail(v) {
      this.detailData = v;
      this.tempDetailType = true;
    },
    reset() {
      this.changeType = false;
      this.changeUserType = false;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: "all",
        queryName: "",
        custId: null,
        queryCode: "ALL",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      };
      this.getList();
    },
    addPerson() {
      this.userDepPostSelectType = true;
    },
    userSuccess(e) {
      console.log(e);
      this.selectAuthUsers = e;
      this.authMethodType = true;
    },
    toTemp() {
      this.$router.push({
        path: "/businessInformationOther/authTemplate",
        query: {
          type: "all",
        },
      });
    },
    submitDate(e) {
      console.log(this.selectAuthUsers);
      let data = {
        id: this.selectAuthMethList[0].id,
        authDate: e ? this.$format(e, "yyyy-MM-dd 23:59:59") : null,
        authType: "all",
        authUserIds: this.selectAuthUsers.map((item) => item.userId),
        agencyUserId: this.$store.state.principalId,
      };
      console.log(data);
      newAuthTemp({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
          this.selectAuthTimeTpye = false;
          this.authMethodType = false;
          this.addingUsersType = false;
        }
      });
    },
    submitMeth(e) {
      console.log(e);
      this.selectAuthMethList = e;
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      console.log(e);
      this.selectAuthUsers = e;
      this.authMethodType = true;
    },
    allSelect() {},
    tableSelect(e) {
      console.log(e);
      this.selectList = [...e];
    },
    changeAuthType() {
      this.showCheckbox = true;
      this.dataColumns = [
        {
          label: "公司名称",
          prop: "companyName",
          width: "200",
        },
        {
          label: "简称",
          prop: "company",
          width: "100",
        },
      ];
      setTimeout(() => {
        this.authType = true;
      }, 50);
    },
    addUser(e) {
      console.log(e);
    },
    init() {
      this.getUser();
      this.getUserPostSetListF();
      this.getList();
    },
    getList() {
      newAuthority({
        ...this.queryParams,
        queryUserId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;

          this.queryParams.total = res.total;
        }
      });
    },
    submitDelet(e) {
      console.log(e);
    },
    getUserPostSetListF() {
      userPostSetList().then((response) => {
        this.userPostListAll = response.data;
      });
    },
    getNickName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
       if(data){
        return data.nickName;
      }
    },
    getUser() {
      getUserListAll().then((res) => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    changeAuthPerson(e) {
      this.$store.state.principalId = e;
    },
  },
};
</script>
  <style lang="less" scoped>
.user {
  display: inline-block;
  border: 1px solid #cccccc;
  padding: 0 10px;
  cursor: pointer;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
.top_auth {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #f2f2f2;
  padding: 0 16px;
}
</style>
  