<template>
  <div class="p-5">
    <div class="item">
      <span><i>*</i>模板名称：</span>
      <el-input
        v-model="params.templateName"
        placeholder="请输入模板名称"
        class="w-60"
      ></el-input>
    </div>
    <div class="item">
      <span style="margin-left: 11px">公司分类：</span>
      <el-select
        v-model="params.companyType"
        filterable=""
        placeholder="请选择"
        class="w-60"
      >
        <el-option
          v-for="item in functList"
          :key="item.unitCode"
          :value="item.unitCode"
          :label="item.unitName"
        ></el-option>
      </el-select>
    </div>
    <div class="item">
      <span style="margin-left: 11px">职能分类：</span>
      <el-select
        v-model="params.functionType"
        filterable=""
        placeholder="请选择"
        class="w-60"
      >
        <el-option
          v-for="item in unitList"
          :key="item.code"
          :value="item.code"
          :label="item.info"
        ></el-option>
      </el-select>
    </div>
    <div class="item flex">
      <span><i style="opacity: 0">*</i>模板说明：</span>
      <el-input
        type="textarea"
        :rows="4"
        style="width: 500px"
        placeholder="请输入内容"
        v-model="params.templateExplain"
      >
      </el-input>
    </div>
    <div class="item_Module" v-for="(item, index) in dataList" :key="index">
      <div class="moduleName">{{ item.moduleName }}</div>
      <div class="radio">
        <el-radio v-model="item.type" label="1">允许</el-radio><br />
        <div
          class="pl-10 mt-5"
          v-if="
            item.type == 1 && item.tempCheckData[0].tempCheckData.length > 0
          "
        >
          <div
            v-for="(i, d) in item.tempCheckData[0].tempCheckData"
            :key="i.order"
          >
            <el-radio v-model="item.checkType" :label="d + 1">{{
              i.choiceDescription
            }}</el-radio>
            <p v-if="i.choiceHint" class="pl-6" style="color: #999999">
              {{ i.choiceHint }}
            </p>
          </div>
        </div>
        <el-radio class="mt-5" v-model="item.type" label="2">禁止</el-radio>
      </div>
    </div>
    <div class="btn">
      <el-button type="primary" size="mini" @click="save">保存</el-button>
      <el-button size="mini" @click="$router.back()">关闭</el-button>
    </div>
  </div>
</template>

<script>
import {
  getTemConfig,
  getUnit,
  getFunct,
  addTemplate,
  getTempDetail,
  editTemplate,
} from "@/api/businessInformation/authTemplate";

export default {
  name: "addTemplate",
  data() {
    return {
      dataList: [],
      textarea: "",
      unitList: [],
      functList: [],
      params: {
        templateExplain: "",
        templateName: "",
        companyType: "",
        functionType: "",
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    save() {
      if (!this.params.templateName) {
        this.$message.warning("请输入模板名称");
        return;
      }
      let flag = false;
      this.dataList.forEach((item) => {
        if (!item.type) {
          flag = true;
        }
        if (
          item.type == 1 &&
          item.tempCheckData[0].tempCheckData.length > 0 &&
          !item.checkType
        ) {
          flag = true;
        }
      });
      if (flag) {
        this.$message.warning("请选择完整每一项");
        return;
      }
      let arr = this.dataList.filter((item) => {
        return item.type == 1;
      });
      let data = [];
      if (arr && arr.length > 0) {
        arr.forEach((item) => {
          if (item.checkType) {
            data.push({
              moduleType: item.code,
              permissionRule:
                item.tempCheckData[0].tempCheckData[item.checkType - 1]
                  .tempCode,
            });
          } else {
            data.push({
              moduleType: item.code,
              permissionRule: item.tempCode,
            });
          }
        });
      }
      let params = {
        ...this.params,
        authTemplateDetailList: data,
        templateType: this.$route.query.type,
        agencyUserId:this.$store.state.principalId

      };
      if (this.$route.query.id && !this.$route.query.copy) {
        editTemplate({ ...params, id: this.$route.query.id }).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$router.back();
          }
        });
      } else {
        addTemplate({ ...params }).then((res) => {
          if (res.code == 200) {
            this.$message.success("编辑成功");

            this.$router.back();
          }
        });
      }
    },
    init() {
      let flag = this.$route.query.type;
      getTemConfig({flag,agencyUserId:this.$store.state.principalId}).then((res) => {
        if (res.code == 200) {
          this.dataList = res.data;
          this.dataList.map((item) => {
            this.$set(item, "checkType", null);
            if (
              item.tempCheckData &&
              item.tempCheckData.length > 0 &&
              item.tempCheckData[0].tempCheckData &&
              item.tempCheckData[0].tempCheckData.length > 0
            ) {
              item.tempCheckData[0].tempCheckData.sort((a,b)=>a.order-b.order)
            }
          });
          if (this.$route.query.id) {
            getTempDetail(this.$route.query.id).then((res) => {
              this.params.templateExplain = res.data.templateExplain;
              this.params.templateName = res.data.templateName;
              this.params.companyType = res.data.companyType;
              this.params.functionType = res.data.functionType;
              if (
                res.data.authTemplateDetailList &&
                res.data.authTemplateDetailList.length > 0
              ) {
                this.dataList.forEach((item) => {
                  res.data.authTemplateDetailList.forEach((i) => {
                    if (item.code == i.moduleType) {
                      this.$set(item, "type", "1");
                      if (item.tempCheckData[0].tempCheckData.length > 0) {
                        item.tempCheckData[0].tempCheckData.forEach((c, x) => {
                          if (c.tempCode == i.permissionRule) {
                            this.$set(item, "checkType", x + 1);
                          }
                        });
                      }
                    }
                  });
                });
                console.log(this.dataList);
                this.dataList.forEach((item) => {
                  if (!item.type) {
                    this.$set(item, "type", "2");
                  }
                });
              }
            });
          }
        }
      });
      getUnit().then((res) => {
        if (res.code == 200) {
          this.unitList = res.data;
        }
      });
      getFunct().then((res) => {
        if (res.code == 200) {
          this.functList = res.data;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  margin-bottom: 16px;
  span {
    font-weight: bold;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
.item_Module {
  .moduleName {
    padding: 5px 10px;
    background: #f2f2f2;
    font-weight: bold;
    margin: 16px 0;
  }
  .radio {
    padding-left: 26px;
  }
}
.btn {
  text-align: center;
  margin-top: 40px;
}
</style>