<template>
  <div class="p-5">
    <div v-if="!detailType">
      <div class="mb-5 flex items-center">
        <div class="mr-3">
          配置平台统一的公司信息，各功能模块的公司名称从此列表获取。项目经理或风险经理可新增、编辑公司
        </div>
        <el-button type="text" @click="openExplain = true">更多说明</el-button>
      </div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="100px"
        style="margin-bottom: 10px"
      >
        <el-form-item label="公司名称" prop="companyName" label-width="100px">
          <el-autocomplete
            v-model.trim="queryParams.companyName"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入"
            @select="handleSelect"
            value-key="companyName"
            clearable
            @clear="getList"
            style="width: 300px"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="支持的产品分类" prop="companyBusinessTypeCode" label-width="120px">
          <el-select
            v-model="queryParams.companyBusinessTypeCode"
            placeholder="支持的产品分类"
            @clear="getList"
            @change="getList"
            clearable
          >
            <el-option
              v-for="dict in businessTypeValuesList"
              :key="dict.id"
              :label="dict.dataName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="是否内部公司" prop="isInside" label-width="100px">
          <el-select
            v-model="queryParams.isInside"
            placeholder=""
            @clear="getList"
            @change="getList"
            clearable
          >
            <el-option
              v-for="item in isInternalList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <MoreSearch modelCode="INTERNALCOMPANY" :params="queryParams" v-show="showMoreSearch"></MoreSearch>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
          <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
            >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
          >
        </el-form-item>
      </el-form>
      <div class="flex">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['companyInformation:addEdit']"
          >新增公司</el-button
        >
        <!-- <el-button
          type="primary"
          size="mini"
          v-hasPermi="['businessInformation:companyType']"
          @click="
            $router.push({ path: '/businessInformationOther/companyType' })
          "
          >管理公司类型</el-button
        > -->
        <!-- <el-button
          type="primary"
          size="mini"
          v-hasPermi="['businessInformation:businessType']"
          @click="
            $router.push({ path: '/businessInformationOther/businessType' })
          "
          >管理支持业务类型</el-button
        > -->
      </div>
      <div class="flex my-3">
        <div class="mr-3" style="height: 40px; line-height: 40px">
          公司类型:
        </div>
        <div class="flex-1 flex-wrap min-h-0 flex">
          <div
            v-for="(item, index) in unitTypeList"
            :key="index"
            style="height: 40px; line-height: 40px"
            :style="{
              color:
                item.dictValue == currentType ? 'black' : 'rgb(24, 144, 255)',
            }"
            class="cursor-pointer mr-2"
            @click="changeUnitType(item)"
          >
            {{ item.dictLabel }}
          </div>
        </div>
      </div>
      <MyTable
        :columns="columns"
        :source="configList"
        :showIndex="true"
        :queryParams="queryParams"
      >
        <template #h_companyShortName="{ record }">
          公司简称
          <el-tooltip
            class="item"
            effect="dark"
            content="创建项目时，公司简称将作为项目名称的一部分每个项目名称由[担保公司]简称+[资产方]简称+[资金方]简称3部分组成"
            placement="top-start"
          >
            <span class="relative bottom-1">①</span>
          </el-tooltip>
        </template>
        <!-- <template #h_isInside="{ record }">
          是否内部公司
          <el-tooltip
            class="item"
            effect="dark"
            content="内部公司可以用于创建组织架构，OA流程分类，财务模块账套等"
            placement="top-start"
          >
            <span class="relative bottom-1">①</span>
          </el-tooltip>
        </template> -->
        <template #companyName="{ record }">
          <div
            @click="handleView(record)"
            class="runcate ... cursor-pointer"
            style="color: #1890ff"
          >
            {{ record.companyName }}
          </div>
        </template>
        <template #companyTypeMappingList="{ record }">
          <div class="flex flex-wrap items-center">
            <div
              v-for="(item, index) in record.companyTypeMappingList"
              :key="index"
              class="mr-3 mt-1 border border-solid rounded px-2"
              :style="{
                borderColor: '#b3d8ff',
                backgroundColor:'#ecf5ff',
                fontSize: '13px',
                color:'#409eff'
              }"
            >
              {{ item && item.dictLabel }}
            </div>
          </div>
        </template>
        <template #companyBusinessTypeMappingList="{ record }">
          <div class="flex flex-wrap items-center">
            <div
              v-for="(item, index) in record.companyBusinessTypeMappingList"
              :key="index"
              class="mr-3 mt-1 border border-solid rounded px-2"
              :style="{
                borderColor: 'rgba(204, 204, 204, 1)',
                backgroundColor: 'rgba(242, 242, 242, 1)',
                fontSize: '13px',
              }"
            >
              {{ item && item.dictLabel }}
            </div>
          </div>
        </template>
        <template #isInside="{ record }">
          {{ isInternalObj[record.isInside] }}
        </template>
        <template #checkStatus="{ record }">
          {{
            record.checkStatus == 0
              ? "新增公司审核中"
              : record.checkStatus == 1
              ? "修改公司审核中"
              : record.checkStatus == 2
              ? "删除公司审核中"
              : record.checkStatus == 3 || record.checkStatus === null
              ? "-"
              : ""
          }}
        </template>
        <template #status="{ record }">
          <dict-tag :options="statusList" :value="record.status" />
        </template>
        <template #operate="{ record }">
          <el-button type="text" @click="handleView(record)"
            >公司详情</el-button
          >
          <el-button
            type="text"
            @click="handleUpdate(record)"
            v-if="record.checkStatus == 3 || record.checkStatus === null"
            v-hasPermi="['companyInformation:addEdit']"
            >编辑</el-button
          >
        </template>
      </MyTable>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <DetailDialogExplain v-model="openExplain" />
    </div>
    <CompanyInformationDetail
      @update="updateDetail"
      v-else
      :id="detailId"
      :type="detailType"
      @addUpdateCallBack="addUpdateCallBack"
    />
  </div>
</template>

<script>
import config from "./components/config";
import DetailDialogExplain from "./components/DetailDialogExplain.vue";
import CompanyInformationDetail from "./components/companyInformationDetail";
import { companyListMore,companyList } from "@/api/businessInformation/companyInformation";
import { getDataManageList } from "@/api/notice/management";

import { getDicts, byParams } from "@/api/system/dict/data";
export default {
  name: "CompanyInformation",
  components: { DetailDialogExplain, CompanyInformationDetail },

  data() {
    return {
      ...config,
      restaurants: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: undefined,
        companyTypeCode: undefined,
        companyBusinessTypeCode: undefined,
        isInside: undefined,
        auxiliaryField: "内部",
      },
      configList: [],
      total: 0,
      openExplain: false,
      unitTypeList: [],
      currentType: undefined,
      detailType: "",
      detailId: "",
      businessTypeValuesList: [],
      showMoreSearch:false
    };
  },
  created() {
    this.init();
  },
  activated() {
    this.getDic();
  },
  methods: {
    init() {
      if (this.$route.query.id) {
        this.handleView({ id: this.$route.query.id });
        return;
      }
      this.getList();
      this.getDic();
      this.getRestaurants();
      this.getDataManageList();
    },
    async getList() {
      const { rows, total } = await companyListMore(this.queryParams);
      this.configList = rows;
      this.total = total;
    },
     async getDataManageList() {
      const { rows } = await getDataManageList({
        firstDataCode: "business_type",
      });
      this.businessTypeValuesList = rows[0].fPiattaformas;
    },
    async getDic() {
      byParams({ dictType: "company_type", auxiliaryField: "内部" }).then(
        (res) => {
          this.unitTypeList = res.data;
          this.unitTypeList.unshift({
            dictLabel: "全部",
            dictValue: undefined,
          });
        }
      );
      
    },
    async getRestaurants() {
      const { rows } = await companyList({ auxiliaryField: "内部" });
      this.restaurants = rows;
    },
    createStateFilter(queryString) {
      return (state) => {
        return (
          state.companyName.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        );
      };
    },
    querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;

      cb(results);
    },
    handleSelect() {
      this.getList();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.moreSearch=undefined;
      this.handleQuery();
    },
    changeUnitType(value) {
      this.currentType = value.dictValue;
      this.queryParams.companyTypeCode = value.dictCode;
      this.getList();
    },
    handleView(value) {
      this.detailType = "view";
      this.goDetail(value.id);
    },
    handleUpdate(value) {
      this.detailType = "update";
      this.goDetail(value.id);
    },
    handleAdd() {
      this.detailType = "add";
      this.goDetail();
    },
    goDetail(value) {
      this.detailId = value || "";
    },
    addUpdateCallBack() {
      this.detailType = false;
      this.getList();
    },
    updateDetail(e) {
      console.log(e);
      this.detailType = "update";
      this.goDetail(e);
    },
  },
};
</script>
