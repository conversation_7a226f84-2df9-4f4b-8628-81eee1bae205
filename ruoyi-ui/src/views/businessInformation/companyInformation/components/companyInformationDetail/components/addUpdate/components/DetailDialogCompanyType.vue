<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="选择公司类型"
      :visible.sync="innerValue"
      width="350px"
      @open="handlerOpen"
    >
      <div style="color: #1890ff">
        <el-radio-group @input="inputRadio">
          <el-radio
            :disabled="mySelectCompany.includes(item.dictLabel)"
            v-for="(item, index) in companyTypeList"
            :key="index"
            :label="item.dictValue"
            class="block my-4"
            >{{ item.dictLabel }}</el-radio
          >
        </el-radio-group>
        <!-- <div @click="openNewBusinessType=true" class="ml-7 cursor-pointer">+ 创建新类型</div> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
    <NewBusinessType
      v-model="openNewBusinessType"
      :uuId="uuId"
      @on-save-success="addBusinessTypeCallBack"
    />
  </div>
</template>

<script>
import { getCompanyTypeList } from "@/api/businessInformation/companyInformation";
import NewBusinessType from "./NewBusinessType.vue";
import vModelMixin from "@/mixin/v-model";
import { getDicts,byParams } from "@/api/system/dict/data";

export default {
  mixins: [vModelMixin],
  components: { NewBusinessType },

  props: {
    selectCompany: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  computed: {
    mySelectCompany() {
      return this.selectCompany.map((item) => item.dictLabel);
    },
  },
  data() {
    return {
      uuId: "",
      companyTypeList: [],
      openNewBusinessType: false,
    };
  },
  watch: {},
  mounted() {},
  methods: {
    async handlerOpen() {
      byParams({ dictType: "company_type", auxiliaryField: "内部" }).then(
        (res) => {
          this.companyTypeList = res.data;
        }
      );
      // const { rows } = await getCompanyTypeList();
      // this.companyTypeList = rows;
    },
    inputRadio(value) {
      const data = this.companyTypeList.filter(
        (item) => item.dictValue == value
      );
      this.$emit("on-save-success", data);
      this.innerValue = false;
    },

    addBusinessTypeCallBack(v) {
      this.uuId = v.returnUUid;
      this.$emit("uuId", this.uuId);
      this.handlerOpen();
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-radio__label {
  color: #1890ff;
}
</style>
