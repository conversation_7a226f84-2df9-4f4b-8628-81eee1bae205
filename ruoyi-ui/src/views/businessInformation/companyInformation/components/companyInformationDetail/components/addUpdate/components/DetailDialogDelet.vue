<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="删除公司"
      :before-close="handleClose"
      :visible.sync="innerValue"
      width="650px"
    >
      <div v-if="hasRole">
        <div v-if="delData.code == 'Y'" style="color: red">
          {{ delData.msg }}
        </div>
        <div v-else>
          <p style="color: red">删除公司信息将无法恢复，请谨慎删除！</p>
          <div>
            删除公司信息，需要由管理员进行审核，点击下一步，将发起OA流程
          </div>
          <div>申请如果被通过，公司将从公司信息列表中移除，立即生效</div>
          <div>申请如果被驳回，公司信息继续存在</div>
          <div>审核结果请关注 [OA办公-我的流程]</div>
        </div>
      </div>
      <div v-else>您没有权限删除公司，请联系业务部总监或风险部总监进行操作</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" class="ml-3">取消</el-button>
        <el-button
          v-show="hasRole && delData.code == 'N'"
          @click="onSubmit"
          class="ml-3"
          type="primary"
          >下一步</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanyIsReference } from "@/api/oa/deploy";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import vModelMixin from "@/mixin/v-model";
import { deleteSystemCompany } from "@/api/businessInformation/companyInformation";
export default {
  mixins: [vModelMixin],
  props: {
    projectCount: {
      type: [String, Number],
      required: true,
      default: 0,
    },
    id: {
      type: [Number, String],
      required: true,
    },
  },
  data() {
    return {
      innerValue: true,
      hasRole: false,
      delData: {},
    };
  },
  watch: {},
  mounted() {
    this.hasRole = this.checkPermi(["companyInformation:delet"]);
    getCompanyIsReference({ id: this.id }).then((res) => {
      this.delData = res;
    });
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    checkPermi,

    async onSubmit() {
      // await deleteSystemCompany(this.id);
      // this.$modal.msgSuccess("删除公司成功！");
      this.innerValue = false;
      this.$emit("on-save-success");
    },
  },
};
</script>

