<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="选择业务类型"
      :visible.sync="innerValue"
      width="350px"
      @open="handlerOpen"
    >
      <div>
        <el-radio-group @input="inputRadio">
          <el-radio
            :disabled="mySelectCompany.includes(item.dictLabel)"
            v-for="(item, index) in businessTypeList"
            :key="index"
            :label="item.dictValue"
            class="block my-4"
            >{{ item.dictLabel }}</el-radio
          >
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanyBusinessTypeList } from "@/api/businessInformation/companyInformation";
import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {
    selectBusiness: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  computed: {
    mySelectCompany() {
      return this.selectBusiness.map((item) => item.dictLabel);
    },
  },
  data() {
    return {
      businessTypeList: [],
    };
  },
  watch: {},
  mounted() {},
  methods: {
    async handlerOpen() {
      const { rows } = await getCompanyBusinessTypeList();
      this.businessTypeList = rows;
    },
    inputRadio(value) {
      const data = this.businessTypeList.filter(
        (item) => item.dictValue == value
      );

      this.$emit("on-save-success", data);
      this.innerValue = false;
    },
  },
};
</script>

