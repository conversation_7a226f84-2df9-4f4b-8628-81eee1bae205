<template>
  <div>
    <div style="margin-bottom: 20px">
      <el-button size="mini" @click="cancel">关闭</el-button>
      <el-button
        size="mini"
        v-if="myform.checkStatus == 3 || myform.checkStatus === null"
        @click="update"
        type="primary"
        >编辑</el-button
      >
      <el-button
        size="mini"
        v-if="myform.checkStatus == 3 || myform.checkStatus === null"
        @click="openDeletClick"
        style="margin-left: 30px"
        type="warning"
        >删除公司</el-button
      >
    </div>
    <el-tabs v-model="currentTab" type="card">
      <el-tab-pane
        :key="index"
        v-for="(item, index) in tabsList"
        :label="item.label"
        :name="item.name"
      >
      </el-tab-pane>
    </el-tabs>
    <component :is="currentTab" :id="id" :form="form"></component>
    <DetailDialogDelet
      v-if="openDelet"
      @close="openDelet = false"
      @on-save-success="deletCallBack"
      :projectCount="form.projectCount"
      :id="id"
    />
  </div>
</template>

<script>
import { getDeploy, getDataByTemplName } from "@/api/oa/deploy";

import XEUtils from "xe-utils";
import config from "@/views/businessInformation/companyInformation/components/config";
import BasicInformation from "./components/BasicInformation.vue";
import SummaryPermissions from "./components/SummaryPermissions.vue";
import DetailDialogDelet from "../addUpdate/components/DetailDialogDelet.vue";
export default {
  name: "ViewDetail",
  components: { BasicInformation, SummaryPermissions, DetailDialogDelet },
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    id: {
      type: [String, Number],
      required: true,
      default: "",
    },
  },

  data() {
    return {
      openDelet: false,
      ...config,
      currentTab: "BasicInformation",
      myform: {},
      queryParams: {
        nickName: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
    };
  },
  watch: {
    form: {
      handler(val) {
        this.myform = XEUtils.clone(val, true);
      },
      immediate: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    handlerParams() {
      this.myform.companyTypeCodes = this.myform.companyTypeMappingList.map(
        (item) => item.dictCode
      );
      if (this.myform.companyBusinessTypeMappingList) {
        this.myform.companyBusinessTypeCodes =
          this.myform.companyBusinessTypeMappingList.map(
            (item) => item.dictCode
          );
      }
    },
    deletCallBack() {
      this.handlerParams();
      sessionStorage.setItem("delCompanyData", JSON.stringify(this.myform));
      getDataByTemplName({
        templateName:
          this.myform.isInside == 1
            ? "业务信息配置-删除内部公司信息申请"
            : "业务信息配置-删除外部公司信息申请",
      }).then((res) => {
        if (res.code == 200) {
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              delCompany: true,
            },
          });
        }
      });
      // this.cancel();
    },
    openDeletClick() {
      this.openDelet = true;
    },
    init() {},
    async handleQuery() {},
    resetQuery() {
      this.queryParams.nickName = "";
    },
    cancel() {
      this.$parent.addUpdateCallBack();
    },
    update() {
      console.log(this.id);
      this.$emit("update", this.id);
    },
  },
};
</script>
