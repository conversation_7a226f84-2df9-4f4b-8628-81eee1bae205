const chineseRegex = /^[\u4e00-\u9fa5]{2,10}$/;
export default {
  columns:Object.freeze([
    { label: "公司类型", key: "dictLabel", minWidth: "300" },
    { label: "顺序号", prop: "dictSort", width: "100" },
    { label: "公司数量", prop: "companyCount", width: "100" },
    { label: "说明", prop: "remark", minWidth: "700" },
    { label: "操作", key: "operate", fixed: "right", align: "left", width: "200" },
  ]),
  columnsView:Object.freeze([
    { label: "公司名称", prop: "companyName",minWidth: "300"  },
    { label: "公司简称", prop: "companyShortName",minWidth: "200"  },
    { label: "状态", key: "status",width: "100"  },
  ]),
  statusList:Object.freeze([
    { label: "正常", value: "0",raw:{listClass:'primary'} },
    { label: "停用", value: "1",raw:{listClass:'danger'}},
   
  ]),
  rules: Object.freeze({
    dictLabel: [
      {required: true, pattern: chineseRegex, message: "请按规则输入公司类型", trigger: "change"},
    ],
  }),
  unitTypeListBackColor: Object.freeze({
    '资金方': "#EDF5FC",
    '资产方': "rgb(253, 247, 232)",
    '担保公司': "rgb(240, 249, 240)",
    '其他': "#F2F2F2",
  }),
  unitTypeListColor: Object.freeze({
    '资金方': "#A9D1EF",
    '资产方': "rgb(240, 198, 100)",
    '担保公司': "rgb(143, 206, 140)",
    '其他': "#CCCCCC",
  }),
  formColumns:Object.freeze([
    {  slotName: "companyName", type: "slot" },
  ])
};
