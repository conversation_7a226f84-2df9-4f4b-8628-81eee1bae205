<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="850px"
      @open="handleOpen"
      @close="handleClose"
    >
        <div >
          <el-form :model="form" label-width="130px">
            <el-form-item label="公司类型:">
              <div
                class="border border-solid rounded px-2 inline-block h-6 leading-6"
                :style="{
                borderColor:form?
                  unitTypeListColor[form.dictLabel] || unitTypeListColor['其他']:'',
                backgroundColor:form?
                  unitTypeListBackColor[form.dictLabel] ||
                  unitTypeListBackColor['其他']:'',
                  fontSize:'13px'
              }"
              >
                {{ form.dictLabel }}
              </div>
            </el-form-item>
            <el-form-item label="备注说明:">
              <div>{{ form.remark }}</div>
            </el-form-item>
            <el-form-item label="公司列表:"> </el-form-item>
            <MyForm  v-model="queryParams" :columns="formColumns" @onSearchList="handleQuery" >
               <template #companyName>
                <el-form-item label="公司名称"  label-width="80px" prop="companyName">
                  <el-autocomplete
                    v-model.trim="queryParams.companyName"
                    :fetch-suggestions="querySearchAsync"
                    placeholder="请输入"
                    @select="getList"
                    value-key="companyName"	
                    clearable
                    @clear="getList"
                    style="width:240px"
                  ></el-autocomplete>
                </el-form-item>
              </template>
            </MyForm>
            <MyTable :columns="columnsView" :source="configList" >
              <template #status="{ record }">
                <dict-tag :options="statusList" :value="record.status" />
              </template>
            </MyTable>
            <pagination
              v-show="total > 20"
              :total="total"
              :page.sync="queryParams.pageNum"
              :page-sizes="[20, 50, 100]"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-form>
        </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { companyList } from "@/api/businessInformation/companyInformation";

export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      ...config,
      configList: [],
      queryParams: {
        companyName:'',
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      restaurants:[]
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handleOpen(){
      this.getRestaurants();
      this.getList()
    },
    async getList() {
      const { rows, total } = await companyList({...this.queryParams,companyTypeCode:this.form.dictCode});
      this.configList = rows;
      this.total = total;
    },
     handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
     async getRestaurants() {
      const { rows } = await companyList({companyTypeCode:this.form.dictCode});
      this.restaurants = rows;
    },
    createStateFilter(queryString) {
      return (state) => {
        return (
          state.companyName.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;
      cb(results)
    },
    handleClose(){
      this.queryParams={
        companyName:'',
        pageNum: 1,
        pageSize: 20,
      }
    }
  },
};
</script>
