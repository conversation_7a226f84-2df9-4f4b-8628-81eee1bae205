<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="650px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div style="max-height: 80vh">
          <el-form
            ref="form"
            :model="myForm"
            label-width="130px"
            :rules="rules"
          >
            <el-form-item label="顺序号">
              <el-input
                v-model.trim="myForm.dictSort"
                class="w-1/5"
                @input="handleInput"
              ></el-input>
              <div>顺序号用于多个公司类型出现时的显示顺序，必须为正整数</div>
              <div style="color: red" v-show="sortError">
                {{ sortError }}
              </div>
            </el-form-item>
            <el-form-item label="公司类型" prop="dictLabel">
              <el-input
                v-model.trim="myForm.dictLabel"
                class="w-1/2"
                placeholder="请输入"
              ></el-input>
              <div class="flex">
                <div>2-10个汉字</div>
                <div class="ml-5" style="color: red" v-show="laberError">
                  {{ laberError }}
                </div>
              </div>
            </el-form-item>

            <el-form-item label="备注说明">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3 }"
                v-model.trim="myForm.remark"
                placeholder="请输入备注说明"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button type="primary" @click="onSubmit" class="mr-3"
            >确定</el-button
          >
          <div v-show="title == '编辑公司类型'" class="mx-3">
            <el-button
              v-hasPermi="['companyType:delet']"
              type="danger"
              @click="onDelet"
              >删除公司类型</el-button
            >
          </div>
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
    <DetailDialogDelet
      v-model="openDelet"
      :cooperationProjectNum="myForm.companyCount"
      :id="deletId"
      @on-save-success="deletSubmit"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import DetailDialogDelet from "./DetailDialogDelet.vue";
import {
  updateCompanyType,
  companyInsertCompanyType,
  companyCheckCompanyType,
} from "@/api/businessInformation/companyInformation";

export default {
  components: { DetailDialogDelet },
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      ...config,
      myForm: {},
      openDelet: false,
      deletId: "",
      laberError: "",
      sortError: "",
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handleOpen() {
      this.myForm = XEUtils.clone(this.form, true);
      this.$nextTick(() => this.$refs["form"].clearValidate());
      this.laberError="";
      this.sortError="";
      if (this.title == "新增公司类型") this.myForm.dictSort='0';
    },
    handleInput() {
      this.myForm.dictSort = this.myForm.dictSort.replace(/\D/g, "");
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          //调接口获取判断是否有重复
          const { data } = await companyCheckCompanyType(
            this.myForm
          );
          this.laberError = data.laberError;
          this.sortError = data.sortError;
          if (!this.laberError && !this.sortError) {
            if (this.title == "编辑公司类型") {
              await updateCompanyType(this.myForm);
              this.$modal.msgSuccess("编辑公司类型成功");
            } else {
              await companyInsertCompanyType(this.myForm);
              this.$modal.msgSuccess("创建公司类型成功！");
            }
            this.innerValue = false;
            this.$emit("on-submit-success");
          }
        }
      });
    },
    onDelet() {
      this.deletId = this.myForm.dictCode;
      this.openDelet = true;
    },
    deletSubmit() {
      this.innerValue = false;
      this.$emit("on-submit-success");
    },
    handleClose() {
      this.$refs.form.resetFields();
    },
  },
};
</script>
