<template>
  <div class="p-5">
    <div class="mb-5">
      配置公司中的公司类型。一个公司可同时属于多个公司类型。项目经理可新增、编辑公司类型
    </div>

    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="88px"
      style="margin-bottom: 10px"
      @submit.native.prevent
    >
      <el-form-item label="公司类型" prop="dictLabel">
        <el-input
          v-model.trim="queryParams.dictLabel"
          placeholder="请输入公司类型"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-button
      class="mb-5"
      type="primary"
      plain
      icon="el-icon-plus"
      size="mini"
      @click="handleAdd"
      v-hasPermi="['companyType:addEdit']"
      >新增公司类型</el-button
    >
    <MyTable
      :columns="columns"
      :source="configList"
      :showIndex="true"
      :queryParams="queryParams"
    >
      <template #dictLabel="{ record }">
        <div
          class="border border-solid rounded px-2 inline-block h-6 leading-6"
          :style="{
            borderColor: record
              ? unitTypeListColor[record.dictLabel] || unitTypeListColor['其他']
              : '',
            backgroundColor: record
              ? unitTypeListBackColor[record.dictLabel] ||
                unitTypeListBackColor['其他']
              : '',
            fontSize: '13px',
          }"
        >
          {{ record.dictLabel }}
        </div>
      </template>
      <template #operate="{ record }">
        <div class="flex justify-cente">
          <el-button type="text" class="mr-2" @click="onView(record)"
            >查看详情</el-button
          >
          <div
            v-if="
              ['担保公司', '资产方', '资金方', '技术服务方', '导流方'].includes(
                record.dictLabel
              )
            "
          >
            <el-button
              type="text"
              v-if="roles.includes('admin')"
              @click="onEdit(record)"
              >编辑</el-button
            >
          </div>
          <div v-else>
            <el-button
              type="text"
              v-hasPermi="['companyType:addEdit']"
              @click="onEdit(record)"
              >编辑</el-button
            >
          </div>

          <el-button type="text" class="ml-2" @click="editRecord(record)"
            >更新记录</el-button
          >
        </div>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      :form="form"
      :title="formTitle"
      v-model="open"
      @on-submit-success="getList"
    />
    <DetailDialogView
      :form="form"
      title="查看公司类型详情"
      v-model="openView"
    />
    <RecordDialog
      :params="paramsRecord"
      v-model="openRecord"
      :columnsRecord="columnsRecord"
      :formColumns="formColumns"
      title="更新记录"
    />
  </div>
</template>

<script>
import { getCompanyTypeList } from "@/api/businessInformation/companyInformation";
import DetailDialog from "./components/DetailDialog.vue";
import DetailDialogView from "./components/DetailDialogView.vue";
import config from "./components/config";
import { mapGetters } from "vuex";

export default {
  name: "CompanyType",
  components: { DetailDialog, DetailDialogView },
  provide: {
    contrast: {dictLabel:'公司类型',dictSort:'顺序号',remark:'备注说明',},//对比表格映射的字段
  },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dictLabel: "",
      },
      configList: [],
      total: 0,
      form: {},
      formTitle: "",
      open: false,
      openView: false,
      openRecord: false,
      paramsRecord: {},
      columnsRecord: Object.freeze([
        { label: "时间", prop: "operTime" },
        { label: "操作人", prop: "nickName" },
        { label: "操作类型", key: "businessType" },
        // { label: "操作", key: "operate" },
      ]),
      formColumns: Object.freeze([
        {
          label: "操作人",
          prop: "operName",
          type: "input",
          placeholder: "请输入操作人",
        },
        {
          label: "时间",
          prop: "operTime",
          type: "datePicker",
          dateType: "daterange",
          placeholder: "请选择时间",
        },
      ]),
    };
  },
  computed: {
    ...mapGetters(["roles"]),
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    async getList() {
      const { rows, total } = await getCompanyTypeList(this.queryParams);
      this.configList = rows;
      this.total = total;
    },
    handleAdd() {
      this.formTitle = "新增公司类型";
      this.form = {};
      this.open = true;
    },
    onView(value) {
      this.form = { ...value };
      this.openView = true;
    },
    onEdit(value) {
      this.formTitle = "编辑公司类型";
      this.form = { ...value };
      this.open = true;
    },
    editRecord(value) {
      this.paramsRecord = {
        relationId: value.dictCode,
        functionNode: "COMPANYTYPE",
        title: "COMPANY",
      };
      this.openRecord = true;
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
