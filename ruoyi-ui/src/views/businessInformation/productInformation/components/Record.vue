<template>
  <div>
    <el-dialog
      title="编辑记录"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div style="display: flex; flex-wrap: wrap">
        <div style="display: flex; align-items: center; margin-right: 9px">
          <span style="margin-right: 9px">编辑人员</span>
          <el-input
            style="width: 180px"
            placeholder="请输入"
            v-model="queryParams2.nickName"
          ></el-input>
        </div>
        <div style="display: flex; align-items: center; margin-right: 9px">
          <span style="margin-right: 9px">编辑类型</span>
          <el-select placeholder="请选择" v-model="queryParams2.operation">
            <el-option label="新增" value="0"></el-option>
            <el-option label="修改" value="1"></el-option>
            <el-option label="删除" value="2"></el-option>
          </el-select>
        </div>
        <div style="display: flex; align-items: center; margin-right: 9px">
          <span style="margin-right: 9px">审核状态</span>
          <el-select placeholder="请选择" v-model="queryParams2.checkStatus">
            <el-option label="审核中" value="0"></el-option>
            <el-option label="通过" value="1"></el-option>
            <el-option label="驳回" value="2"></el-option>
          </el-select>
        </div>
        <div
          style="
            display: flex;
            align-items: center;
            margin-right: 9px;
            margin-top: 12px;
          "
        >
          <span style="margin-right: 9px">编辑日期</span>
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>
        <el-button
          style="margin-top: 12px"
          type="primary"
          size="mini"
          @click="search"
          >搜索</el-button
        >
        <el-button style="margin-top: 12px" size="mini" @click="reset"
          >重置</el-button
        >
      </div>
      <el-table
        border=""
        :data="tableData"
        style="width: 100%; margin-top: 16px"
      >
        <el-table-column prop="editTime" label="编辑日期" width="160" />
        <el-table-column prop="nickName" label="编辑人员" />
        <el-table-column prop="nickName" label="编辑类型">
          <template slot-scope="scope">
            {{
              scope.row.operation == 0
                ? "新增"
                : scope.row.operation == 1
                ? "修改"
                : "删除"
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="editInfo"
          label="说明"
          show-overflow-tooltip=""
          width="150"
        />

        <el-table-column prop="editTime" label="审核时间" width="160" />
        <el-table-column prop="date" label="审核状态" width="100">
          <template slot-scope="scope">
            {{
              scope.row.checkStatus == 0
                ? "审核中"
                : scope.row.checkStatus == 1
                ? "通过"
                : "驳回"
            }}
          </template>
        </el-table-column>

        <el-table-column prop="date" width="130" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="see(scope.row)"
              >查看编辑内容</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total2 > 20"
        :total="total2"
        :page.sync="queryParams2.pageNum"
        :page-sizes="[20, 50, 100]"
        :limit.sync="queryParams2.pageSize"
        @pagination="getList2"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
    <RecordDetail v-if="RecordDetailType" @close="RecordDetailType = false" :data="data"/>
  </div>
</template>

<script>
import RecordDetail from "./RecordDetail.vue";
import { traderDetail, selectEditRecord2 } from "@/api/oa/voucharRules";

export default {
  components: {
    RecordDetail,
  },
  name: "Record",

  props: {
    oaApplyId: Number,
  },
  data() {
    return {
      data: null,
      RecordDetailType: false,
      contrastDetail: false,
      time: [],
      tableData: [],
      queryParams2: {
        pageNum: 1,
        pageSize: 20,
        applyType: 2,
        oaApplyId: "",
        nickName: "",
        operation: "",
        checkStatus: "",
        createTime: "",
        updateTime: "",
      },
      total2: 0,
      dialogVisible: true,
      operId: "",
    };
  },
  mounted() {
    this.getList2();
  },
  methods: {
    see(v) {
      this.data = v;
      this.RecordDetailType = true;
    },
    handleClose() {
      this.$emit("close");
    },
    reset() {
      this.queryParams2 = {
        pageNum: 1,
        pageSize: 20,
        applyType: 2,
        oaApplyId: "",
        nickName: "",
        operation: "",
        checkStatus: "",
        createTime: "",
        updateTime: "",
      };
      this.time = [];
      this.getList2();
    },
    search() {
      this.queryParams2.pageNum = 1;
      this.getList2();
    },
    getList2() {
      this.queryParams2.oaApplyId = this.oaApplyId;
      if (this.time && this.time.length > 0) {
        this.queryParams2.createTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.queryParams2.updateTime = this.$format(this.time[1], "yyyy-MM-dd");
      } else {
        this.queryParams2.createTime = "";
        this.queryParams2.updateTime = "";
      }
      selectEditRecord2(this.queryParams2).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.total2 = res.total || 0;
        }
      });
    },
  },
};
</script>

<style>
</style>