<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="删除产品"
      :visible.sync="innerValue"
      width="550px"
    >
    <div>
      <div>是否确认删除此产品信息？</div>
      <div style="color:red">删除产品后已经配置的产品业务数据查看权限将失效，请谨慎删除！</div>
    </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button type="primary" @click="onSubmit" class="mr-3"
            >确定</el-button
          >
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import {
  deleteProjectParameter,
} from "@/api/businessInformation/productInformation";
export default {
  mixins: [vModelMixin],
  props: {
    id: {
      type: [Number,String],
      required: true,
    },
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {
    async onSubmit() {
      await deleteProjectParameter(this.id);
      this.$modal.msgSuccess("删除成功");
      this.innerValue = false;
      this.$emit('deletSubmit')
    },
  },
};
</script>
