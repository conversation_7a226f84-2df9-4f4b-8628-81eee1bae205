<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="650px"
    >
      <el-scrollbar>
        <div style="max-height: 80vh">
          <div class="row">
            <div><span>产品名称：</span>{{ form.productName }}</div>
            <div><span>产品编码：</span>{{ form.productNo }}</div>
          </div>
          <div class="row">
            <div>
              <span>系统：</span> {{ dict.label.system_product[form.systemNo] }}
            </div>
           
          </div>
          <div class="row">
            <div><span>项目名称：</span>{{ form.projectName }}</div>
          </div>
          <div class="row">
            <div><span>说明：</span>{{ form.description }}</div>
          </div>
          <div class="row">
            <div>
              <span>启用状态：</span
              ><el-tag style="width: auto" v-if="form.status == 0">正常</el-tag>
              <el-tag v-else type="danger" style="width: auto">停用</el-tag>
            </div>
          </div>
          <el-divider></el-divider>
          <div style="display: flex; justify-content: space-between">
            <div class="left">
              <div>
                <span>是否映射成功:</span
                >{{ form.isMapping && form.isMapping == "Y" ? "是" : "否" }}
              </div>
              <div>
                <span>是否为企业客户:</span
                >{{
                  form.isProjectCompany && form.isProjectCompany == "Y"
                    ? "是"
                    : "否"
                }}
              </div>
              <div>
                <span>是否有还款计划:</span
                >{{
                  form.isProjectPlan && form.isProjectPlan == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>是否有还款计划缩期情况:</span
                >{{
                  form.isProjectPlanReset && form.isProjectPlanReset == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>是否有提前还款数据:</span
                >{{
                  form.isProjectRepay4 && form.isProjectRepay4 == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>是否有代偿还款数据:</span
                >{{
                  form.isProjectRepay7 && form.isProjectRepay7 == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>累计还款数据:</span
                >{{
                  form.isProjectTotalRepay &&
                  form.isProjectTotalRepay == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>代偿时结清:</span
                >{{
                  form.isProjectRepay7Finish &&
                  form.isProjectRepay7Finish == "Y"
                    ? "是"
                    : "否"
                }}
              </div>
              <div>
                <span>FPD10是否可用:</span
                >{{
                  form.isResultFpd10 && form.isResultFpd10 == "Y"
                    ? "是"
                    : "否"
                }}
              </div>
              <div>
                <span>余额分布是否可用:</span
                >{{
                  form.isResultBalanceDistribution &&
                  form.isResultBalanceDistribution == "Y"
                    ? "是"
                    : "否"
                }}
              </div>
            </div>
            <div class="right">
              <div>
                <span>项目是否已结束:</span
                >{{
                  form.isProjectFinish && form.isProjectFinish == "Y"
                    ? "是"
                    : "否"
                }}
              </div>
              <div>
                <span>是否通道简版数据:</span
                >{{
                  form.isProjectTd && form.isProjectTd == "Y" ? "是" : "否"
                }}
              </div>
              <div>
                <span>是否有还款计划实还更新数据:</span
                >{{
                  form.isProjectPlanUpdate &&
                  form.isProjectPlanUpdate == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>是否有正常还款数据:</span
                >{{
                  form.isProjectRepay1 && form.isProjectRepay1 == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>是否有提前结清数据:</span
                >{{
                  form.isProjectRepay5 && form.isProjectRepay5 == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>累计代偿还款数据:</span
                >{{
                  form.isProjectTotalRepay7 &&
                  form.isProjectTotalRepay7 == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>追偿还款数据:</span
                >{{
                  form.isProjectRepay8 && form.isProjectRepay8 == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>追偿还款是否按正常还款提供:</span
                >{{
                  form.isProjectRepay8Normal &&
                  form.isProjectRepay8Normal == "Y"
                    ? "有"
                    : "否"
                }}
              </div>
              <div>
                <span>Vintage是否可用:</span
                >{{
                  form.isResultVintage &&
                  form.isResultVintage == "Y"
                    ? "是"
                    : "否"
                }}
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";

export default {
  dicts: ["system_product"],
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
  },
};
</script>
<style lang="less" scoped>
.row {
  display: flex;
  margin-top: 20px;

  div {
    span {
      display: inline-block;
      font-weight: bold;
      width: 120px;
      text-align: right;
    }
  }
}
.left,
.right {
  width: 50%;
  div {
    margin-top: 16px;
    span {
      font-weight: bold;
      display: inline-block;
      width: 190px;
      text-align: right;
    }
  }
}
</style>
