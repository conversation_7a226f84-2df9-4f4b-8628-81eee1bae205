<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="850px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div>
          <el-form
            ref="form"
            :model="myForm"
            label-width="130px"
            :rules="rules"
          >
            <el-form-item label="项目名称" prop="projectId">
              <el-select
                v-model="myForm.projectId"
                placeholder="请选择项目名称"
                @change="changePro"
                filterable
              >
                <el-option
                  v-for="dict in projectList"
                  :key="dict.id"
                  :label="dict.projectName"
                  :value="dict.id"
                />
              </el-select>
              <div class="leading-4 h-3 my-2" style="color: grey">
                选择产品要关联的项目<br />一个项目下可以包含多个产品<br />
                如果未找到所需产品，请在 [项目名称] 菜单中进行创建
              </div>
            </el-form-item>
            <div style="display: flex; margin-top: 50px">
              <el-form-item label="产品名称" prop="productName">
                <el-input
                  style="width: 200px"
                  placeholder="请输入"
                  v-model.trim="myForm.productName"
                  class="w-1/2"
                ></el-input>
              </el-form-item>
              <el-form-item label="产品编码" prop="productNo">
                <el-input
                  placeholder="请输入"
                  style="width: 200px"
                  v-model.trim="myForm.productNo"
                  class="w-1/2"
                ></el-input>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="系统" prop="systemNo">
                <el-select
                  style="width: 200px"
     
                  v-model="myForm.systemNo"
                  placeholder="请选择系统"
                >
                  <el-option
                    v-for="dict in systemProductList"
                    :key="dict.dictCode"
                    :label="dict.dictLabel"
                    :value="dict.dictCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-switch style="margin-right: 9px" v-model="status">
                </el-switch
                >启用
              </el-form-item>
            </div>

            <el-form-item label="说明">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3 }"
                v-model.trim="myForm.description"
                placeholder="请输入说明"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <el-divider></el-divider>
        <div style="display: flex; justify-content: space-between">
          <div class="left">
            <div>
              <span>是否映射成功:</span>
              <el-select v-model="myForm.isMapping">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否为企业客户:</span>
              <el-select v-model="myForm.isProjectCompany">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有还款计划:</span>
              <el-select v-model="myForm.isProjectPlan">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有还款计划缩期情况:</span>
              <el-select v-model="myForm.isProjectPlanReset">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有提前还款数据:</span>
              <el-select v-model="myForm.isProjectRepay4">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有代偿还款数据:</span>
              <el-select v-model="myForm.isProjectRepay7">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>累计还款数据:</span>
              <el-select v-model="myForm.isProjectTotalRepay">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>代偿时结清:</span>
              <el-select v-model="myForm.isProjectRepay7Finish">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>FPD10是否可用:</span>
              <el-select v-model="myForm.isResultFpd10">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>余额分布是否可用:</span>
              <el-select v-model="myForm.isResultBalanceDistribution">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
          </div>
          <div class="right">
            <div>
              <span>项目是否已结束:</span>
              <el-select v-model="myForm.isProjectFinish">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否通道简版数据:</span>
              <el-select v-model="myForm.isProjectTd">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有还款计划实还更新数据:</span>
              <el-select v-model="myForm.isProjectPlanUpdate">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有正常还款数据:</span>
              <el-select v-model="myForm.isProjectRepay1">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>是否有提前结清数据:</span>
              <el-select v-model="myForm.isProjectRepay5">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>累计代偿还款数据:</span>
              <el-select v-model="myForm.isProjectTotalRepay7">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>追偿还款数据:</span>
              <el-select v-model="myForm.isProjectRepay8">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>追偿还款是否按正常还款提供:</span>
              <el-select v-model="myForm.isProjectRepay8Normal">
                <el-option label="有" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
            <div>
              <span>Vintage是否可用:</span>
              <el-select v-model="myForm.isResultVintage">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div v-if="title == '新增产品'" style="margin-left: 200px; color: #999">
          新增产品信息，需要由管理员进行审核，点击下一步，将发起OA流程
        </div>
        <div v-else style="margin-left: 200px; color: #999">
          编辑产品信息，需要由管理员进行审核，点击下一步，将发起OA流程
        </div>
        <div style="margin-left: 200px; color: #999">
          申请如果被通过，立即生效
        </div>
        <div v-if="title == '新增产品'" style="margin-left: 200px; color: #999">
          申请如果被驳回，本次申请的产品信息将会被删除
        </div>
        <div v-else style="margin-left: 200px; color: #999">
          申请如果被驳回，本次的修改将无效
        </div>
        <div style="margin-left: 200px; color: #999">
          审核结果请关注 [OA办公-我的流程]
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button type="primary" @click="onSubmit" class="mr-3"
            >下一步</el-button
          >
          <!-- <div v-show="title == '编辑产品'" class="mx-3">
            <el-button
              v-hasPermi="['productInformation:delet']"
              type="danger"
              @click="onDelet"
              >删除产品</el-button
            >
          </div> -->
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
    <DetailDialogDelet
      v-model="openDelet"
      :id="deletId"
      @deletSubmit="deletSubmit"
    />
  </div>
</template>

<script>
import { getDeploy, getDataByTemplName } from "@/api/oa/deploy";

import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import DetailDialogDelet from "./DetailDialogDelet.vue";
import {
  addProjectParameter,
  updateProjectParameter,
} from "@/api/businessInformation/productInformation";
import { querySelectList } from "@/api/oa/deploy";
export default {
  dicts: ["system_product"],
  components: { DetailDialogDelet },
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    title: {
      type: String,
      required: true,
    },
    systemProductList:Array
  },
  data() {
    return {
      ...config,
      status: true,
      projectList: [],
      myForm: {},
      openDelet: false,
      deletId: "",
      oldForm: {},
    };
  },
  watch: {},
  mounted() {
    
  },
  methods: {
     
    changePro(e) {
      let data = this.projectList.find((item) => {
        return item.id == e;
      });
      this.myForm.projectName = data.projectName;
    },
    async init() {
      this.getProjectList();
    },
    async getProjectList() {
      const { rows } = await querySelectList({ isEnable: "Y" });
      this.projectList = rows;
    },
    handleOpen() {
      this.init();
      this.oldForm = XEUtils.clone(this.form, true);
      this.myForm = XEUtils.clone(this.form, true);
      console.log(this.myForm);
      if (this.myForm.id) {
        this.status = this.myForm.status == 0 ? true : false;
        this.myForm.systemNo = this.myForm.systemNo&&this.myForm.systemNo*1
      }

      this.$nextTick(() => this.$refs["form"].clearValidate());
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          console.log(this.status);
          this.myForm.status = this.status ? "0" : "1";
          console.log(this.myForm.status);

          if (this.title == "新增产品") {
            sessionStorage.setItem(
              "addProductData",
              JSON.stringify(this.myForm)
            );
            console.log(this.myForm);
            getDataByTemplName({
              templateName: "业务信息配置-新增产品信息申请",
            }).then((res) => {
              if (res.code == 200) {
                this.innerValue = false;
                this.$router.push({
                  path: "/oaWork/updateProcessForm",
                  query: {
                    templateId: res.templateId,
                    classificationId: res.classificationId,
                    companyId: res.companyId,
                    addProduct: true,
                  },
                });
              }
            });
          } else {
            let data = {
              oldData: this.oldForm,
              newData: this.myForm,
            };
            sessionStorage.setItem("editProductData", JSON.stringify(data));
            getDataByTemplName({
              templateName: "业务信息配置-修改产品信息申请",
            }).then((res) => {
              if (res.code == 200) {
                this.innerValue = false;
                this.$router.push({
                  path: "/oaWork/updateProcessForm",
                  query: {
                    templateId: res.templateId,
                    classificationId: res.classificationId,
                    companyId: res.companyId,
                    editProduct: true,
                  },
                });
              }
            });
          }
        }

        return;
        if (valid) {
          if (this.title == "编辑产品") {
            await updateProjectParameter(this.myForm);
            this.$modal.msgSuccess("修改成功");
          } else {
            await addProjectParameter(this.myForm);
            this.$modal.msgSuccess("新增成功");
          }
          this.innerValue = false;
          this.$emit("on-submit-success");
        }
      });
    },
    onDelet() {
      this.deletId = this.myForm.id;
      this.openDelet = true;
    },
    deletSubmit() {
      this.innerValue = false;
      this.$emit("on-submit-success");
    },
    handleClose() {
      this.$refs.form.resetFields();
    },
  },
};
</script>
<style lang="less" scoped>
.left,
.right {
  width: 50%;
  div {
    margin-bottom: 16px;
    span {
      font-weight: bold;
      display: inline-block;
      width: 190px;
      text-align: right;
      margin-right: 5px;
    }
  }
}
</style>
