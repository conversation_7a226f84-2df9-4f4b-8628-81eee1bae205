<template>
  <div>
    <el-dialog
      title="删除产品"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <p style="color: red">
        删除产品后已经配置的产品业务数据查看权限将失效，请谨慎删除！
      </p>
      <div>删除产品信息，需要由管理员进行审核，点击下一步，将发起OA流程</div>
      <div>申请如果被通过，产品将从产品信息列表中移除，立即生效</div>
      <div>申请如果被驳回，产品信息继续存在</div>
      <div>审核结果请关注 [OA办公-我的流程]</div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="next">下一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: true,
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    next() {
      this.$emit("next");
    },
  },
};
</script>

<style>
</style>