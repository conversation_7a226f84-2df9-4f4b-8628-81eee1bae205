<template>
  <div class="p-5">
    <div class="mb-5">
      配置项目支持的产品。产品必须关联在项目之下，先创建项目才能关联产品
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="88px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="系统" class="form-item" prop="systemNo">
        <el-select
          v-model="queryParams.systemNo"
          placeholder="请选择系统"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in systemProductList"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model.trim="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model.trim="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-button
      class="mb-5"
      type="primary"
      plain
      icon="el-icon-plus"
      size="mini"
      @click="handleAdd"
      v-hasPermi="['productInformation:addEdit']"
      >新增产品</el-button
    >
    <MyTable
      :columns="columns"
      :source="configList"
      :showIndex="true"
      :queryParams="queryParams"
    >
      <template #systemNo="{ record }">
        {{ systemProductListObj[record.systemNo * 1] }}
      </template>
      <template #checkStatus="{ record }">
        {{
          record.checkStatus == 0
            ? "新增产品审核中"
            : record.checkStatus == 1
            ? "修改产品审核中"
            : record.checkStatus == 2
            ? "删除产品审核中"
            : record.checkStatus == 3 || record.checkStatus === null
            ? "-"
            : ""
        }}
      </template>
      <template #status="{ record }">
        <el-tag v-if="record.status == 0">正常</el-tag>
        <el-tag v-else type="danger">停用</el-tag>
      </template>
      <template #operate="{ record }">
        <el-button type="text" @click="onView(record)">查看详情</el-button>
        <el-button
          v-if="record.checkStatus == 3 || record.checkStatus === null"
          type="text"
          v-hasPermi="['productInformation:addEdit']"
          @click="onEdit(record)"
          >编辑</el-button
        >
        <el-button
          v-if="record.checkStatus == 3 || record.checkStatus === null"
          type="text"
          v-hasPermi="['productInformation:delet']"
          @click="del(record)"
          >删除</el-button
        >
        <el-button type="text" @click="editRecord(record)">编辑记录</el-button>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      :form="form"
      :title="formTitle"
      :systemProductList="systemProductList2"
      v-model="open"
      @on-submit-success="getList"
    />
    <DetailDialogView :form="form" title="产品信息" v-model="openView" />
    <RecordDialog :params="paramsRecord" v-model="openRecord" />
    <Record
      :oaApplyId="oaApplyId"
      v-if="recordType"
      @close="recordType = false"
    />
    <DelDialog
      v-if="DelDialogType"
      @close="DelDialogType = false"
      @next="next"
    />
  </div>
</template>

<script>
import { getDeploy, getDataByTemplName } from "@/api/oa/deploy";

import { projectParameter } from "@/api/businessInformation/productInformation";
import DetailDialog from "./components/DetailDialog.vue";
import DelDialog from "./components/DelDialog.vue";
import Record from "./components/Record.vue";
import DetailDialogView from "./components/DetailDialogView.vue";
import config from "./components/config";
import { getDicts } from "@/api/system/dict/data";
import { arrToObj } from "@/utils";

export default {
  name: "ProductInformation",
  components: { DetailDialog, DetailDialogView, Record, DelDialog },
  provide: {
    contrast: {
      projectName: "项目名称",
      productName: "产品名称",
      productNo: "产品编码",
      systemNo: "系统",
      description: "说明",
    }, //对比表格映射的字段
    dictionary: { systemNo: "system_product" }, //需要转字典的对象集合 字符串则转字典 枚举值则根据枚举转
  },
  data() {
    return {
      oaApplyId: "",
      DelDialogType: false,
      recordType: false,
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemNo: undefined,
        productName: undefined,
        projectName: undefined,
      },
      systemProductList: [],
      systemProductListObj: {},
      configList: [],
      total: 0,
      form: {},
      formTitle: "",
      open: false,
      openView: false,
      openRecord: false,
      paramsRecord: {},
      systemProductList2: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    del(v) {
      this.form = v;
      this.DelDialogType = true;
    },
    next() {
      console.log(this.form);
      sessionStorage.setItem("delProductData", JSON.stringify(this.form));
      getDataByTemplName({
        templateName: "业务信息配置-删除产品信息申请",
      }).then((res) => {
        if (res.code == 200) {
          this.DelDialogType = false;
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              delProduct: true,
            },
          });
        }
      });
    },
    init() {
      this.getList();
      this.getDic();
    },
    async getList() {
      const { rows, total } = await projectParameter(this.queryParams);
      this.configList = rows;
      this.total = total;
    },
    async getDic() {
      const { data } = await getDicts("system_product");
      this.systemProductList2 = JSON.parse(JSON.stringify(data));

      this.systemProductList = data;
      this.systemProductList.unshift({
        dictLabel: "全部",
        dictValue: undefined,
      });
      this.systemProductListObj = arrToObj(this.systemProductList);
      console.log(this.systemProductListObj,'--');
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.formTitle = "新增产品";
      this.form = {};
      this.open = true;
    },
    onView(value) {
      this.form = { ...value };
      this.openView = true;
    },
    onEdit(value) {
      this.formTitle = "编辑产品";
      this.form = { ...value };
      this.open = true;
    },
    editRecord(value) {
      this.oaApplyId = value.id;
      this.recordType = true;
    },
  },
};
</script>
