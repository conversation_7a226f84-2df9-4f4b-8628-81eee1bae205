<template>
  <div>
    <p class="mb-0">
      本页面展示您在智慧OA系统可以发起哪些公司的流程，它们来自于您的上级用户对您的授权
    </p>
    <p class="mb-0">您可以将自己已拥有的OA流程权限授权给自己的下级用户</p>

    <div class="mt-3">
      <el-input
        class="mr-3"
        style="width: 200px"
        placeholder="请输入公司名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary">搜索</el-button>
      <el-button icon="el-icon-refresh">重置</el-button>
    </div>
    <div class="mt-3">
      <!-- <span class="mr-6">
        <el-button size="mini" type="primary" @click="changeAuthType"
          >批量授权</el-button
        >
      </span> -->
      <el-switch class="mr-3" v-model="changeType"> </el-switch
      >未对他人分配权限的公司
    </div>
    <MyTable
      class="mt-3"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template v-slot:h_users="">
        <div>
          授权用户
          <el-tooltip
            class="item"
            effect="dark"
            content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </template>
      <template #users="{ record }">
        <span class="user"
          >张三<span style="color: #cccccc">（已停用）</span
          ><i class="el-icon-close"></i
        ></span>
      </template>

      <template #opertion="{ record }">
        <el-button type="text" @click="addUser(record)">+ 添加用户</el-button>
        <el-button type="text" @click="addUser(record)">查看详情</el-button>
        <el-button type="text" @click="addUser(record)"
          >- 取消用户在本模块所有授权</el-button
        >
      </template>
    </MyTable>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <AddingUsers
      v-if="addingUsersType"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />
    <AuthMethod
      v-if="authMethodType"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
    />
    <SelectAuthTime
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dataList: [{}],
      dataColumns: [
        {
          label: "可发起流程的公司",
          prop: "companyName",
          width: "200",
        },
        {
          label: "流程模板数量",
          prop: "company",
          width: "100",
        },
        {
          label: "授权用户",
          prop: "users",
          key: "users",
          width: "300",
          isHSlot: true,
        },
        {
          label: "账号",
          prop: "userCode",
          width: "100",
        },

        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,
      columnsSelect: [
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: "300",
          showOverflowTooltipField: true,
        },
      ],
      tableDataSelect: [
        {
          projectName:
            "中保国信-360-富邦银行水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水啊啊啊啊啊啊啊啊啊",
          id: 1,
        },
        { projectName: "中保国信-360-北部湾 金科服务费", id: 2 },
        { projectName: "海南正堂-度小满-通商银行", id: 3 },
      ],
      openSelect: false,
      selectList: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    toTemp() {
      this.$router.push({
        path: "/businessInformationOther/authTemplate",
        query: {
          type: "unit",
        },
      });
    },
    submitMeth(e) {
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      this.authMethodType = true;
    },
    allSelect() {},
    tableSelect(e) {
      console.log(e);
      this.selectList = [...e];
    },
    changeAuthType() {
      this.$emit("authType");
    },
    addUser(e) {
      console.log(e);
    },
    init() {
      this.getList();
    },
    getList() {},
    submitDelet(e) {
      console.log(e);
    },
  },
};
</script>
<style lang="less" scoped>
.user {
  display: inline-block;
  border: 1px solid #cccccc;
  padding: 0 10px;
cursor: pointer;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
