<template>
  <div>
    <el-dialog
      title="取消用户所有授权"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleClose"
    >
      <el-radio v-model="radio" label="1"
        >取消您对 [{{ selectUserName }}] 在公司维度的所有授权</el-radio
      ><br />
      <el-radio v-model="radio" label="2"
        >取消您对 [{{ selectUserName }}] 在所有维度的所有授权</el-radio
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :disabled="!radio" @click="submit">下一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    selectUserName: String,
  },
  data() {
    return {
      dialogVisible: true,
      radio: "",
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit", this.radio);
    },
  },
};
</script>

<style>
</style>