<template>
  <div>
    <el-dialog
      title="查看详情"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <p>当前用户已经具有的OA流程权限汇总</p>
      <el-divider></el-divider>
      <div style="display: flex">
        <div style="width: 200px; overflow-x: auto; flex-shrink: 0">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            ref="tree"
            :default-expand-all="false"
            @node-click="handleNodeClick"
            style="float: left;"
          >
            <span class="custom-tree-node" slot-scope="{ node }">
              <span>
                <i class="el-icon-folder-opened"></i>
              </span>
              <span>{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
        <el-table :data="dataList" style="flex: 1">
          <el-table-column
            label="序号"
            align="center"
            type="index"
            :index="indexMethod" />
          <el-table-column label="模板名称" align="center" prop="templateName">
          </el-table-column>
          <el-table-column
            label="备注说明"
            show-overflow-tooltip=""
            align="center"
            prop="remark"
        /></el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { listTemplate } from "@/api/oa/processTemplate";
import { treeSelectFroNewAuthority } from "@/api/businessInformation/currencyCompany";
export default {
  props: {
    tempDetailUserId: Number,
  },
  data() {
    return {
      dialogVisible: true,
      deptOptions: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      dataList: [],
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        classificationId: "",
      },
      total: 0,
    };
  },
  computed: {
    indexMethod: function () {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1;
    },
  },
  mounted() {
    treeSelectFroNewAuthority({ userId: this.tempDetailUserId }).then((res) => {
      this.deptOptions = res.data;
    });
  },
  methods: {
    handleNodeClick(data) {
      this.queryParams.classificationId = data.id;
      this.getList();
    },
    getList() {
      listTemplate(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
</style>