<template>
  <div>
    <p class="mb-0">
      通过公司维度，配置债转管理数据查看的权限<br />您可以将自己已拥有的权限授权给自己的下级用户
    </p>

    <div class="mt-3">
      <el-input
        class="mr-3"
        v-model="queryParams.queryName"
        style="width: 200px"
        placeholder="请输入公司名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary" @click="getList()"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
    <div class="mt-3">
      <!-- <span class="mr-6">
          <el-button
            size="mini"
            type="primary"
            v-hasPermi="['currencyCompany:allAuth']"
            @click="changeAuthType"
            >批量授权</el-button
          >
        </span> -->
      <el-switch class="mr-3" v-model="changeUserType" @change="getList">
      </el-switch
      >仅看直属下级
      <el-switch class="mr-3 ml-8" v-model="changeType" @change="getList">
      </el-switch
      >未对他人分配权限的公司
    </div>

    <MyTable
      class="mt-3"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template v-slot:h_authorizedUserList="">
        <div>
          授权用户
          <el-tooltip
            class="item"
            effect="dark"
            content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </template>
      <template #authorizedUserList="{ record }">
        <div
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span
            class="user"
            @click="getUserData(item.authorizedUserId, item)"
            :style="{
              background:
                item.authorizedUserIsCurrentUserFlag != 1 ||
                item.authorizedUserHaveAllPermissionFlag == 1
                  ? '#f2f2f2'
                  : '',
            }"
          >
            {{ item.authorizedUserId | user
            }}<span style="color: #cccccc">{{
              item.authorizedUserId | userStatus
            }}</span
            ><i
              @click.stop="delUser(record, item)"
              class="el-icon-close"
              v-if="
                item.authorizedUserIsCurrentUserFlag == 1 &&
                item.authorizedUserHaveAllPermissionFlag != 1
              "
            ></i>
          </span>
        </div>
      </template>
      <template #userName="{ record }">
        <div
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span>
            {{ item.authorizedUserId | userName }}
          </span>
        </div>
      </template>
      <template #dept="{ record }">
        <div
          class="item_div postName"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span v-for="(v, i) in userPostListAll" :key="i">
            <span v-if="v.userId == item.authorizedUserId"
              >{{ v.deptName }},</span
            >
          </span>
        </div>
      </template>
      <template #post="{ record }">
        <div
          class="item_div postName"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span v-for="(v, i) in userPostListAll" :key="i">
            <span v-if="v.userId == item.authorizedUserId"
              >{{ v.postName }},</span
            >
          </span>
        </div>
      </template>
      <template #opertion="{ record }">
        <div
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <el-button
            type="text"
            v-hasPermi="['currencyCompany:addPerson']"
            v-if="item.authorizedUserIsCurrentUserFlag == 0"
            @click="addUser(record)"
            >+ 添加用户</el-button
          >
          <el-button
            v-hasPermi="['currencyCompany:cencelAllAuth']"
            v-if="
              item.authorizedUserIsCurrentUserFlag == 1 &&
              item.authorizedUserHaveAllPermissionFlag != 1
            "
            type="text"
            @click.stop="delUser(record, item)"
            >- 取消授权</el-button
          >
        </div>
      </template>
    </MyTable>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <AddingUsers
      v-if="addingUsersType"
      :subordinateList="subordinateList"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />
    <AuthMethod
      v-if="authMethodType"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
    />
    <SelectAuthTime
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
    <UserDetail2
      :userId="userId"
      :authType="authTypeName"
      v-if="userDetailType"
      @close="userDetailType = false"
    />
  </div>
</template>
  <script>
import {
  newAuthority,
  getUserListAll,
  subordinate,
  newAuthTemp,
  queryCancelUser,
  cancelAuthorization,
  queryAllCancelUser,
  cancelAllAuthorization,
  addAuthorization,
} from "@/api/businessInformation/currencyCompany";
import { userPostSetList } from "@/api/system/post";

let that = "";
export default {
  data() {
    return {
      userId: "",
      authTypeName: "",
      userDetailType: false,
      changeUserType: false,
      subordinateList: [],
      userList: [],
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: 2,
        queryName: "",
        queryCode: "DEBTCONVERSION",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      },
      dataList: [{}],
      dataColumns: [
        {
          label: "公司名称",
          prop: "responseName",
          width: "200",
        },

        {
          label: "授权用户",
          prop: "authorizedUserList",
          key: "authorizedUserList",
          width: "180",
          isHSlot: true,
        },
        {
          label: "账号",
          key: "userName",
          width: "180",
        },
        {
          label: "所属部门",
          key: "dept",
          width: "180",
        },
        {
          label: "所属岗位",
          key: "post",
          width: "180",
        },

        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,
      columnsSelect: [
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: "300",
          showOverflowTooltipField: true,
        },
      ],
      tableDataSelect: [
        {
          projectName:
            "中保国信-360-富邦银行水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水啊啊啊啊啊啊啊啊啊",
          id: 1,
        },
        { projectName: "中保国信-360-北部湾 金科服务费", id: 2 },
        { projectName: "海南正堂-度小满-通商银行", id: 3 },
      ],
      openSelect: false,
      selectList: [],
      selectItemData: null,
      userPostListAll: [],
    };
  },
  created() {
    that = this;

    this.init();
  },
  watch: {
    "$store.state.principalId": {
      handler(newval, oldval) {
        console.log(newval);
        this.init();
      },
      deep: true,
    },
  },
  filters: {
    user(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
    userName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
       if(data){
 return data.userName;
}
     
    },
    userStatus(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data && data.status == 0) {
        return "";
      } else {
        return "（已停用）";
      }
    },
  },
  methods: {
    getUserData(e, v) {
      console.log(e);
      if (v.authorizedUserHaveAllPermissionFlag == 1) {
        this.authTypeName = "所有";
      }
      if (
        !v.authorizedUserHaveAllPermissionFlag &&
        !v.authorizedUserHaveCompanyPermissionFlag
      ) {
        this.authTypeName = "项目";
      }
      if (v.authorizedUserHaveCompanyPermissionFlag == 1) {
        this.authTypeName = "公司";
      }
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      console.log(data);
      this.userId = data.userName;
      this.userDetailType = true;
    },
    getUserPostSetListF() {
      userPostSetList().then((response) => {
        this.userPostListAll = response.data;
      });
    },
    getNickName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
    delUser(v, i) {
      console.log(v, i);
      let params = {
        id: v.id,
        authorizedType: 2,
        authorizedCode: "DEBTCONVERSION",
        unAuthorizedUserId: i.authorizedUserId,
      };
      queryCancelUser({
        ...params,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.msg == 0) {
          this.$confirm(
            `是否确认取消对用户[${this.getNickName(
              i.authorizedUserId
            )}]的授权？点击确定后，取消授权将立即生效！`,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        } else if (res.msg > 0) {
          this.$confirm(
            `是否确认取消对用户[${this.getNickName(
              i.authorizedUserId
            )}]的授权？ [${this.getNickName(
              i.authorizedUserId
            )}]已经将本权限授权于其下级用户，取消后其下级用户也将失去此权限,点击确定后，取消授权将立即生效！ `,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        }
      });
    },
    cancelAuthorization(e) {
      cancelAuthorization({
        ...e,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
        }
      });
    },
    submitMeth(e) {
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      let data = {
        id: this.selectItemData.id,
        businessType: 2,
        businessCode: "DEBTCONVERSION",
        authorizedUserIdList: e.map((item) => item.userId),
        ancestors: this.selectItemData.ancestors
          ? this.selectItemData.ancestors
          : "",
      };
      addAuthorization({
        ...data,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("添加成功");
          this.getList();
          this.addingUsersType = false;
        }
      });
    },
    allSelect() {},
    tableSelect(e) {
      console.log(e);
      this.selectList = [...e];
    },
    changeAuthType() {
      this.$emit("authType");
    },
    addUser(e) {
      console.log(e);
      subordinate({ principalId: this.$store.state.principalId }).then(
        (res) => {
          if (res.code == 200) {
            if (
              res.data.subordinateList &&
              res.data.subordinateList.length > 0
            ) {
              this.subordinateList = res.data;
              this.selectItemData = e;
              this.addingUsersType = true;
            } else {
              this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
            }
          }
        }
      );
    },
    init() {
      this.getUser();
      this.getUserPostSetListF();
      this.getList();
    },
    getList() {
      this.queryParams.subordinateFlag = this.changeUserType ? 1 : 0;
      this.queryParams.unassignedCompaniesFlag = this.changeType ? 1 : 0;
      newAuthority({
        ...this.queryParams,
        queryUserId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          this.queryParams.total = res.total;
        }
      });
    },
    getUser() {
      getUserListAll().then((res) => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    submitDelet(e) {
      console.log(e);
    },
    reset() {
      this.changeType = false;
      this.changeUserType = false;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: 2,
        queryName: "",
        queryCode: "DEBTCONVERSION",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      };
      this.getList();
    },
  },
};
</script>
  <style lang="less" scoped>
.item_div {
  height: 58px;
  line-height: 52px;
  border-bottom: 1px solid #e6ebf5;
}
.user {
  display: inline-block;
  height: 25px;
  line-height: 25px;
  border: 1px solid #cccccc;
  padding: 0 10px;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
/deep/ .el-table .cell {
  padding: 0 !important;
  margin: 0 !important;
}
/deep/ .el-table .el-table__cell {
  padding: 0 !important;
}
.postName {
  white-space: nowrap;
  overflow-x: auto;
}
</style>
  