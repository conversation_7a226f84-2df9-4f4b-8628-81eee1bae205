<template>
    <div>
      <p class="mb-0">
        通过公司维度，配置物品管理数据查看的权限<br />您可以将自己已拥有的权限授权给自己的下级用户
      </p>
  
      <div class="mt-3">
        <el-input
          class="mr-3"
          v-model="queryParams.queryName"
          style="width: 200px"
          placeholder="请输入公司名称"
        ></el-input>
        <el-button icon="el-icon-search" type="primary" @click="getList()"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
      </div>
      <div class="mt-3">
        <!-- <span class="mr-6">
          <el-button
            size="mini"
            v-hasPermi="['currencyCompany:allAuth']"
            type="primary"
            @click="changeAuthType"
            >批量授权</el-button
          >
        </span> -->
        <el-switch class="mr-3" v-model="changeUserType" @change="getList">
        </el-switch
        >仅看直属下级
        <el-switch class="mr-3 ml-8" v-model="changeType" @change="getList">
        </el-switch
        >未对他人分配权限的公司
      </div>
  
      <MyTable
        class="mt-3"
        :columns="dataColumns"
        :queryParams="queryParams"
        :source="dataList"
        :showIndex="true"
      >
        <template #authorizedUserList2="{ record }">
          <div
            v-if="
              record.authorizedFeatureDetailList[17].featureUserList.length > 0
            "
          >
            <div
              v-for="(item, i) in record.authorizedFeatureDetailList[17]
                .featureUserList"
              :key="i"
            >
              <span
                class="user"
                @click="getUserData(item.featureUserId, item)"
                :style="{
                  background:
                    item.authorizedUserIsCurrentUserFlag != 1 ||
                    item.authorizedUserHaveAllPermissionFlag == 1
                      ? '#f2f2f2'
                      : '',
                }"
              >
                {{ item.featureUserId | user
                }}<span style="color: #cccccc">{{
                  item.featureUserId | userStatus
                }}</span
                ><i
                  @click.stop="
                    delUser(
                      record,
                      item,
                      record.authorizedFeatureDetailList[17].featureUserFlag
                    )
                  "
                  class="el-icon-close"
                  v-if="
                    item.authorizedUserIsCurrentUserFlag == 1 &&
                    item.authorizedUserHaveAllPermissionFlag != 1
                  "
                ></i>
              </span>
            </div>
          </div>
        </template>
        <template v-slot:h_authorizedUserList="">
          <div>
            物品查看和领用权限
            <el-tooltip
              class="item"
              effect="dark"
              content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
              placement="top"
            >
              <i class="el-icon-info"></i>
            </el-tooltip>
          </div>
        </template>
        <template #authorizedUserList="{ record }">
          <div
            v-if="
              record.authorizedFeatureDetailList[18].featureUserList.length > 0
            "
          >
            <div
              v-for="(item, i) in record.authorizedFeatureDetailList[18]
                .featureUserList"
              :key="i"
            >
              <span
                class="user"
                @click="getUserData(item.featureUserId, item)"
                :style="{
                  background:
                    item.authorizedUserIsCurrentUserFlag != 1 ||
                    item.authorizedUserHaveAllPermissionFlag == 1
                      ? '#f2f2f2'
                      : '',
                }"
              >
                {{ item.featureUserId | user
                }}<span style="color: #cccccc">{{
                  item.featureUserId | userStatus
                }}</span
                ><i
                  @click.stop="
                    delUser(
                      record,
                      item,
                      record.authorizedFeatureDetailList[18].featureUserFlag
                    )
                  "
                  class="el-icon-close"
                  v-if="
                    item.authorizedUserIsCurrentUserFlag == 1 &&
                    item.authorizedUserHaveAllPermissionFlag != 1
                  "
                ></i>
              </span>
            </div>
          </div>
        </template>
        <template #dept="{ record }">
          <div
            class="item_div postName"
            v-for="(item, i) in record.authorizedUserList"
            :key="i"
          >
            <span v-for="(v, i) in userPostListAll" :key="i">
              <span v-if="v.userId == item.authorizedUserId"
                >{{ v.deptName }},</span
              >
            </span>
          </div>
        </template>
        <template #post="{ record }">
          <div
            class="item_div postName"
            v-for="(item, i) in record.authorizedUserList"
            :key="i"
          >
            <span v-for="(v, i) in userPostListAll" :key="i">
              <span v-if="v.userId == item.authorizedUserId"
                >{{ v.postName }},</span
              >
            </span>
          </div>
        </template>
        <template #opertion="{ record }">
          <el-dropdown v-hasPermi="['currencyCompany:addPerson']" trigger="click">
            <span class="el-dropdown-link"> +添加用户 </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button
                  type="text"
                  v-if="getPerminsson(17, record.id)"
                  @click="addUser(record, 17)"
                  >指定物品管理员</el-button
                >
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button
                  type="text"
                  v-if="
                    getPerminsson(18, record.id) || getPerminsson(17, record.id)
                  "
                  @click="addUser(record, 18)"
                  >分配物品查看和领用权限</el-button
                >
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </MyTable>
  
      <pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
  
      <AddingUsers
        v-if="addingUsersType"
        :subordinateList="subordinateList"
        @close="addingUsersType = false"
        @submit="submitAddUser"
      />
      <AuthMethod
        v-if="authMethodType"
        @close="authMethodType = false"
        @submitMeth="submitMeth"
      />
      <SelectAuthTime
        v-if="selectAuthTimeTpye"
        @close="selectAuthTimeTpye = false"
      />
      <UserDetail2
        :userId="userId"
        :authType="authTypeName"
        v-if="userDetailType"
        @close="userDetailType = false"
      />
    </div>
  </template>
  <script>
  import {
    newAuthority,
    getUserListAll,
    subordinate,
    newAuthTemp,
    queryCancelUser,
    cancelAuthorization,
    queryAllCancelUser,
    userRoleAuthority,
    cancelAllAuthorization,
    addAuthorization,
  } from "@/api/businessInformation/currencyCompany";
  import { userPostSetList } from "@/api/system/post";
  
  let that = "";
  export default {
    data() {
      return {
        authTypeName: "",
        userId: "",
        userDetailType: false,
        changeUserType: false,
        subordinateList: [],
        userList: [],
        selectAuthTimeTpye: false,
        allSelectType: false,
        showCheckbox: false,
        //批量授权状态
        authType: false,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          total: 0,
          queryType: 2,
          queryName: "",
          queryCode: "OFFSUPPLY",
          queryThirdType: 1,
          unassignedCompaniesFlag: 0,
          subordinateFlag: 0,
        },
        dataList: [{}],
        dataColumns: [
          {
            label: "公司名称",
            prop: "responseName",
            width: "200",
          },
          {
            label: "简称",
            prop: "responseAbbreviationName",
            width: "120",
          },
          {
            label: "物品管理员",
            prop: "authorizedUserList2",
            key: "authorizedUserList2",
            width: "180",
          },
          {
            label: "授权用户",
            prop: "authorizedUserList",
            key: "authorizedUserList",
            width: "180",
            isHSlot: true,
          },
          {
            label: "操作",
            key: "opertion",
            width: "200",
          },
        ],
        companyType: "1",
        changeType: false,
        authMethodType: false,
        addingUsersType: false,
        columnsSelect: [
          {
            label: "项目名称",
            prop: "projectName",
            minWidth: "300",
            showOverflowTooltipField: true,
          },
        ],
        tableDataSelect: [],
        openSelect: false,
        selectList: [],
        selectItemData: null,
        userPostListAll: [],
        roleData: null,
        addType: "",
      };
    },
    created() {
      that = this;
  
      this.init();
  
    },
    watch: {
      "$store.state.principalId": {
        handler(newval, oldval) {
          console.log(newval);
          this.init();
        },
        deep: true,
      },
    },
    filters: {
      user(e) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        if (data) {
          return data.nickName;
        }
      },
      userName(e) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
         if(data){
   return data.userName;
  }
       
      },
      userStatus(e) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        if (data && data.status == 0) {
          return "";
        } else {
          return "（已停用）";
        }
      },
    },
    methods: {
      userRoleAuthority() {
        userRoleAuthority({
          businessType: 2,
          businessCode: "OFFSUPPLY",
          principalId: this.$store.state.principalId,
          inputIdList:this.dataList.length>0?this.dataList.map(item=>item.id).join(','):''
        }).then((res) => {
          this.roleData = res.data;
        });
      },
      getPerminsson(flag, companyId) {
        let data = this.roleData.userAuthorizedDetail.find((item) => {
          return item.userFlag == flag;
        });
        if (data && data.companyIdList.includes(companyId)) {
          return true;
        } else {
          return false;
        }
      },
      getUserData(e, v) {
        console.log(e);
        if (v.authorizedUserHaveAllPermissionFlag == 1) {
          this.authTypeName = "所有";
        }
        if (
          !v.authorizedUserHaveAllPermissionFlag &&
          !v.authorizedUserHaveCompanyPermissionFlag
        ) {
          this.authTypeName = "项目";
        }
        if (v.authorizedUserHaveCompanyPermissionFlag == 1) {
          this.authTypeName = "公司";
        }
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        console.log(data);
        this.userId = data.userName;
        this.userDetailType = true;
      },
      getUserPostSetListF() {
        userPostSetList().then((response) => {
          this.userPostListAll = response.data;
        });
      },
      getNickName(e) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        if (data) {
          return data.nickName;
        }
      },
      delUser(v, i, x) {
        console.log(v, i, x);
        let params = {
          id: v.id,
          authorizedType: 2,
          authorizedCode: "OFFSUPPLY",
          unAuthorizedUserId: i.featureUserId,
          authRoleCode: x,
        };
        queryCancelUser({
          ...params,
          principalId: this.$store.state.principalId,
        }).then((res) => {
          if (res.msg == 0) {
            this.$confirm(
              `是否确认取消对用户[${this.getNickName(
                i.featureUserId
              )}]的授权？点击确定后，取消授权将立即生效！`,
              "取消授权",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            ).then(() => {
              this.cancelAuthorization(params);
            });
          } else if (res.msg > 0) {
            this.$confirm(
              `是否确认取消对用户[${this.getNickName(
                i.authorizedUserId
              )}]的授权？ [${this.getNickName(
                i.authorizedUserId
              )}]已经将本权限授权于其下级用户，取消后其下级用户也将失去此权限,点击确定后，取消授权将立即生效！ `,
              "取消授权",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            ).then(() => {
              this.cancelAuthorization(params);
            });
          }
        });
      },
      cancelAuthorization(e) {
        cancelAuthorization({
          ...e,
          principalId: this.$store.state.principalId,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.getList();
          }
        });
      },
      submitMeth(e) {
        this.selectAuthTimeTpye = true;
      },
      submitAddUser(e) {
        let data = {
          id: this.selectItemData.id,
          businessType: 2,
          businessCode: "OFFSUPPLY",
          authorizedUserIdList: e.map((item) => item.userId),
          authRoleCode: this.addType,
          ancestors: this.selectItemData.ancestors
            ? this.selectItemData.ancestors
            : "",
        };
        addAuthorization({
          ...data,
          principalId: this.$store.state.principalId,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success("添加成功");
            this.getList();
            this.addingUsersType = false;
          }
        });
      },
      allSelect() {},
      tableSelect(e) {
        console.log(e);
        this.selectList = [...e];
      },
      changeAuthType() {
        this.$emit("authType");
      },
      addUser(e, v) {
        console.log(e, v);
        this.selectItemData = e;
        this.addType = v;
        subordinate({ principalId: this.$store.state.principalId }).then(
          (res) => {
            if (res.code == 200) {
              if (
                res.data.subordinateList &&
                res.data.subordinateList.length > 0
              ) {
                this.subordinateList = res.data;
                this.addingUsersType = true;
              } else {
                this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
              }
            }
          }
        );
      },
      init() {
        this.getUser();
        this.getUserPostSetListF();
        this.getList();
      },
      getList() {
        this.queryParams.subordinateFlag = this.changeUserType ? 1 : 0;
        this.queryParams.unassignedCompaniesFlag = this.changeType ? 1 : 0;
        newAuthority({
          ...this.queryParams,
          queryUserId: this.$store.state.principalId,
        }).then((res) => {
          if (res.code == 200) {
            this.dataList = res.rows;
            this.queryParams.total = res.total;
            this.userRoleAuthority();
          }
        });
      },
      getUser() {
        getUserListAll().then((res) => {
          if (res.code == 200) {
            this.userList = res.data;
          }
        });
      },
      submitDelet(e) {
        console.log(e);
      },
      reset() {
        this.changeType = false;
        this.changeUserType = false;
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          total: 0,
          queryType: 2,
          queryName: "",
          queryCode: "OFFSUPPLY",
          queryThirdType: 1,
          unassignedCompaniesFlag: 0,
          subordinateFlag: 0,
        };
        this.getList();
      },
    },
  };
  </script>
  <style lang="less" scoped>
  .item_div {
    height: 70px;
    line-height: 52px;
    border-bottom: 1px solid #e6ebf5;
  }
  .user {
    display: inline-block;
    height: 25px;
    line-height: 25px;
    border: 1px solid #cccccc;
    padding: 0 10px;
    color: #409eff;
    border-radius: 4px;
    margin-right: 10px;
    i {
      color: #666666;
      margin-left: 5px;
      cursor: pointer;
    }
  }
  
  .postName {
    white-space: nowrap;
    overflow-x: auto;
  }
  .user {
    display: inline-block;
    border: 1px solid #cccccc;
    padding: 0 10px;
    cursor: pointer;
    color: #409eff;
    border-radius: 4px;
    margin-top: 3px;
    margin-right: 10px;
    i {
      color: #666666;
      margin-left: 5px;
      cursor: pointer;
    }
  }
  .demonstration {
    display: block;
  }
  </style>
  