import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download,downloadPostJson } from '@/utils/request'

import Vue2OrgTree from 'vue-tree-color'

Vue.use(Vue2OrgTree)


import './assets/icons' // icon
import './permission' // permission control
import { getDicts, listData } from '@/api/system/dict/data'
import { getConfigKey } from '@/api/system/config'
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree,addDateRangeCustom } from '@/utils/ruoyi'
import {format,formaterMoney} from '@/utils/index.js'
Vue.prototype.$format = format
Vue.prototype.$formaterMoney = formaterMoney

Element.Dialog.props.closeOnClickModal.default = false
// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from '@/components/Editor'
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'

//echarts组件
import * as echarts from 'echarts'
//k-form-design表单设计
import KFormDesign from './components/k-form-design/packages/index.js'
import './components/k-form-design/styles/form-design.less'
import './components/k-form-design/self/self'
import Print from 'vue-print-nb'

import horizontalScroll from 'el-table-horizontal-scroll'


import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getToken } from '@/utils/auth'
// 全局方法挂载
Vue.prototype.$echarts = echarts
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.listData = listData
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.addDateRangeCustom = addDateRangeCustom
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.downloadPostJson = downloadPostJson
Vue.prototype.handleTree = handleTree



// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
Vue.use(KFormDesign)
Vue.use(Print)
Vue.use(horizontalScroll)
DictData.install()
import {
  getVersion
} from "@/api/auditQrcode/index";
router.beforeEach((to, from, next) => {
  if (to.path != '/login') {
    getVersion().then(res => {
      let version = res.data.version
      if (!localStorage.getItem("version") || localStorage.getItem("version") != version) {
        localStorage.setItem('version', version)
        Vue.prototype.$message({
          message: '当前系统有新版本',
          type: 'warning'
        });
        setTimeout(() => {
          window.location.reload()
        }, 2000);
      } else {
        next()
      }
    })
  }else{
    next()
  }



});
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
