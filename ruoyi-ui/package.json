{"name": "ruoyi", "version": "3.8.1", "description": "若依管理系统", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "test": "vue-cli-service serve --mode testbeta", "test2": "vue-cli-service serve --mode testbeta2", "uat": "vue-cli-service serve --mode uat", "uat2": "vue-cli-service serve --mode uat2", "uat3": "vue-cli-service serve --mode uat3", "prod": "vue-cli-service serve --mode production", "build:prod": "vue-cli-service build", "build:oauth": "vue-cli-service build --mode oauth", "build:test": "vue-cli-service build --mode testbeta", "build:test2": "vue-cli-service build --mode testbeta2", "build:uat": "vue-cli-service build --mode uat", "build:uat2": "vue-cli-service build --mode uat2", "build:uat3": "vue-cli-service build --mode uat3", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/resource": "^6.1.15", "@fullcalendar/resource-timeline": "^6.1.15", "@fullcalendar/vue": "^6.1.15", "@riophae/vue-treeselect": "0.4.0", "ant-design-vue": "^1.7.8", "axios": "0.24.0", "chinese-to-pinyin": "^1.3.1", "clipboard": "2.0.8", "core-js": "^3.25.3", "crypto-js": "4.1.1", "dayjs": "^1.11.11", "echarts": "4.9.0", "el-table-horizontal-scroll": "^1.2.4", "el-table-virtual-scroll": "^2.0.1", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html-to-image": "^1.11.11", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "^3.3.2", "less": "^3.0.4", "less-loader": "^4.1.0", "lodash": "^4.17.21", "moment": "^2.24.0", "nprogress": "0.2.0", "quill": "1.3.7", "quill-image-drop-module": "^1.0.3", "quill-image-extend-module": "^1.1.2", "quill-image-resize-module": "^3.0.0", "screenfull": "5.0.2", "sortablejs": "1.10.2", "uuid": "^9.0.1", "vcolorpicker": "^2.0.12", "vue": "2.6.12", "vue-codemirror-lite": "^1.0.4", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-json-excel": "^0.3.0", "vue-meta": "2.4.0", "vue-pdf": "^4.3.0", "vue-print-nb": "^1.7.5", "vue-quill-editor": "^3.0.6", "vue-router": "3.4.9", "vue-tree-color": "^2.3.3", "vuedraggable": "^2.23.2", "vuex": "3.6.0", "wpk-reporter": "^0.9.3", "xe-utils": "^3.5.8", "xlsx-js-style": "^1.2.0", "zm-tree-org": "^2.1.3"}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "autoprefixer": "^9.8.8", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "postcss": "^7.0.39", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "style-resources-loader": "^1.5.0", "svg-sprite-loader": "5.1.1", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue-template-compiler": "2.6.12", "webpack": "^4.42.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}